import React, { useState } from 'react';
import { View, StyleSheet, Pressable, ScrollView } from 'react-native';
import { Button, Text } from '../../components/atoms';
import color from '../../theme/pallets/pallet';
import CopyIcon from '../../components/atoms/Icons/CopyIcon';
import { TransferIcon } from '../../components/atoms/Icons';
import { useCopyCustomToast } from '../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { InvestmentStackParams } from '../../navigation/stack-investment/types';

type Props = NativeStackScreenProps<
  InvestmentStackParams,
  'InvestmentConfirmScreen'
>;
export default function InvestmentConfirmScreen({ navigation }: Props) {
  const [activeTab, setActiveTab] = useState<'AR$' | 'US$' | 'Cripto'>('AR$');
  const { showToast, toastConfig } = useCopyCustomToast();

  const handleNavigateToSuccess = () => {
    navigation.navigate('InvestmentSuccessScreen');
  };

  const renderAccountDetails = () => {
    return (
      <View style={styles.card}>
        <View
          style={{
            borderBottomWidth: 1,
            borderBottomColor: color.NEUTRALS_200,
            padding: 16,
            flexDirection: 'row',
            gap: 8,
          }}
        >
          <TransferIcon size={24} color={color.PRIMARY_500} />
          <Text variant="B6">Transferilo a esta cuenta</Text>
        </View>
        <View style={{ padding: 16, gap: 8 }}>
          <View>
            <Text variant="R7" color="NEUTRALS_800">
              Titular de la cuenta
            </Text>
            <Text variant="B6">Laura Martinez</Text>
          </View>
          <View>
            <Text variant="R7" color="NEUTRALS_800">
              CVU
            </Text>
            <View style={styles.row}>
              <Text variant="B6">0070282130009751221196</Text>
              <Pressable onPress={() => showToast('0070282130009751221196')}>
                <CopyIcon size={24} color={color.PRIMARY_500} />
              </Pressable>
            </View>
          </View>
          <View>
            <Text variant="R7" color="NEUTRALS_800">
              CUIT
            </Text>
            <Text variant="B6">30-71755043-5</Text>
          </View>
        </View>
        <Toast config={toastConfig} />
      </View>
    );
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={{ gap: 24 }}>
        <Text variant="B2">Confirmá la compra</Text>

        <View style={styles.tabs}>
          {['AR$', 'US$', 'Cripto'].map(tab => (
            <Pressable
              key={tab}
              onPress={() => setActiveTab(tab as 'AR$' | 'US$' | 'Cripto')}
              style={[
                styles.tabButton,
                activeTab === tab && styles.activeTabButton,
              ]}
            >
              <Text
                variant={activeTab === tab ? 'B6' : 'R6'}
                color={activeTab === tab ? 'PRIMARY_700' : 'NEUTRALS_800'}
              >
                {tab}
              </Text>
            </Pressable>
          ))}
        </View>

        {/* Account details */}
        {activeTab === 'AR$' && renderAccountDetails()}
        {/* Agregar lógica para otras tabs si es necesario */}
        <Text variant="R7" color="NEUTRALS_600">
          La transferencia podría demorar hasta 24hs en acreditarse.
        </Text>
      </View>
      <View
        style={{
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderWidth: 1,
          borderColor: color.NEUTRALS_200,
          padding: 16,
          marginHorizontal: -16,
          gap: 16,
        }}
      >
        <View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text variant="R5">Vas a invertir: </Text>
            <Text variant="B2" color="PRIMARY_500">
              $ 5.500.000
            </Text>
          </View>
        </View>
        <Button text="Confirmar compra" onPress={handleNavigateToSuccess} />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    flexGrow: 1,
    justifyContent: 'space-between',
  },
  backButton: {
    marginBottom: 16,
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  tabButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: color.PRIMARY_700,
  },
  activeTabText: {
    color: '#E63946',
  },
  card: {
    borderRadius: 16,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
  },
  accountTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  copyButton: {
    fontSize: 14,
    color: '#E63946',
  },
  footer: {
    marginTop: 'auto',
    alignItems: 'center',
  },
  confirmButton: {
    backgroundColor: '#E63946',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
});
