import React, { useContext, useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { InputForm } from '../../components/molecules';
import { Button, Text, TextBase } from '../../components/atoms';
import { InfoAccountModal } from '../../components/organisms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { api } from '../../services/apiService';
import { Account, AccountResponse } from '../../types/Account';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { useBackgroundState } from '../../hooks/useBackgroundState';
import { AuthContext } from '../../context/AuthContext';
import HeaderTransfer from '../../components/organisms/Headers/Transfer';
type Props = NativeStackScreenProps<any>;

export const NewAccountScreen = ({ navigation }: Props) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [text, setText] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [accountData, setAccountData] = useState<Account>();
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const handleChange = (txt: string) => {
    setText(txt);
    setError('');
  };

  const validateAccount = async () => {
    if (text === '') {
      return 'Ingrese el CBU, CVU o alias';
    }
    setLoading(true);

    try {
      const response = await api.get<AccountResponse>(
        `/digital/utils/validCvuAlias/${text}`,
      );
      setAccountData(response.data.data);
      setModalVisible(true);
      setLoading(false);
    } catch (err) {
      console.error(err);
      setError('Revisa este dato');
      setLoading(false);
    }
  };

  const navigateToAmount = () => {
    setModalVisible(false);
    navigation.navigate('AmountScreen', {
      nombre: accountData?.titulares[0].nombre,
      banco: '',
      cuit: accountData?.titulares[0].cuit,
      cvu: accountData?.cvu,
    });
  };

  const { isInBackground } = useContext(AuthContext);
  useEffect(() => {
    isInBackground && setModalVisible(false);
  }, [isInBackground]);

  return (
    <KeyboardAvoidingComponent collapse>
      <View style={styles.container}>
        <View>
          <HeaderTransfer text="Transferir" />
          <View style={{ marginTop: 24, marginBottom: 4 }}>
            <Text variant="R7">CBU, CVU o alias</Text>
          </View>
          <InputForm
            placeholder={isFocused ? '' : 'Ingresa el CBU, CVU o alias'}
            setText={handleChange}
            text={text}
            error={!!error}
            errorText={error}
            onBlur={handleBlur}
            onFocus={handleFocus}
          />
        </View>

        <InfoAccountModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          navigateToAmount={navigateToAmount}
          accountData={accountData}
        />
        <Button
          onPress={validateAccount}
          text="Validar"
          disabled={loading || !text || !!error}
          loading={loading}
        />
      </View>
    </KeyboardAvoidingComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: 16,
    justifyContent: 'space-between',
    marginBottom: 16,
  },
});
