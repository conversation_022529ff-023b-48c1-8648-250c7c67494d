import { useEffect } from 'react';
import { BackHandler } from 'react-native';

const useDisableBackButton = () => {
  useEffect(() => {
    const disableBackButton = () => true;

    BackHandler.addEventListener('hardwareBackPress', disableBackButton);

    return () => {
      BackHandler.removeEventListener('hardwareBackPress', disableBackButton);
    };
  }, []);
};

export default useDisableBackButton;
