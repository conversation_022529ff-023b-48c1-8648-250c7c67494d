import React, { FC } from 'react';
import Svg, { Path, G } from 'react-native-svg';
import { IconProps } from './types';

const ShareIcon: FC<IconProps> = ({ width, height }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 16 17" fill="none">
      <Path
        d="M13 11.9333C12.3667 11.9333 11.8 12.1833 11.3667 12.575L5.425 9.11662C5.46667 8.92495 5.5 8.73328 5.5 8.53328C5.5 8.33328 5.46667 8.14162 5.425 7.94995L11.3 4.52495C11.75 4.94162 12.3417 5.19995 13 5.19995C14.3833 5.19995 15.5 4.08328 15.5 2.69995C15.5 1.31662 14.3833 0.199951 13 0.199951C11.6167 0.199951 10.5 1.31662 10.5 2.69995C10.5 2.89995 10.5333 3.09162 10.575 3.28328L4.7 6.70828C4.25 6.29162 3.65833 6.03328 3 6.03328C1.61667 6.03328 0.5 7.14995 0.5 8.53328C0.5 9.91662 1.61667 11.0333 3 11.0333C3.65833 11.0333 4.25 10.775 4.7 10.3583L10.6333 13.825C10.5917 14 10.5667 14.1833 10.5667 14.3666C10.5667 15.7083 11.6583 16.7999 13 16.7999C14.3417 16.7999 15.4333 15.7083 15.4333 14.3666C15.4333 13.025 14.3417 11.9333 13 11.9333ZM13 1.86662C13.4583 1.86662 13.8333 2.24162 13.8333 2.69995C13.8333 3.15828 13.4583 3.53328 13 3.53328C12.5417 3.53328 12.1667 3.15828 12.1667 2.69995C12.1667 2.24162 12.5417 1.86662 13 1.86662ZM3 9.36662C2.54167 9.36662 2.16667 8.99162 2.16667 8.53328C2.16667 8.07495 2.54167 7.69995 3 7.69995C3.45833 7.69995 3.83333 8.07495 3.83333 8.53328C3.83333 8.99162 3.45833 9.36662 3 9.36662ZM13 15.2166C12.5417 15.2166 12.1667 14.8416 12.1667 14.3833C12.1667 13.925 12.5417 13.55 13 13.55C13.4583 13.55 13.8333 13.925 13.8333 14.3833C13.8333 14.8416 13.4583 15.2166 13 15.2166Z"
        fill="white"
      />
    </Svg>
  );
};

export default ShareIcon;
