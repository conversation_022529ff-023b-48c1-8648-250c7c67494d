import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: color.WHITE,
    flex: 1,
  },
  containerLogo: {
    alignItems: 'center',
  },
  error: {
    fontWeight: 'bold',
    color: color.RED_700,
    marginVertical: 12,
  },
  containerVolver: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 70,
    justifyContent: 'space-between',
  },
  textVolver: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    color: color.PRIMARY_700,
    marginLeft: 4,
    marginBottom: 2,
  },

  textBottom: {
    color: color.BLACK,
    fontSize: 22,
    marginBottom: 20,
    fontFamily: 'Satoshi-SemiBold',
  },
  boldText: {
    color: color.BLACK,
    fontWeight: 'bold',
    textDecorationLine: 'underline',
    marginHorizontal: 4,
  },
  button: {
    borderRadius: 4,
    marginBottom: 0,
    marginTop: '10%',
  },
  buttonWhitoutMargin: {
    borderRadius: 4,
    marginBottom: 0,
    marginTop: '10%',
  },
  containerInputs: {
    marginHorizontal: 16,
  },
  topTextInput: {
    marginBottom: 5,
  },
  loginOther: {
    marginTop: 30,
  },
  iconsBottom: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 35,
    paddingBottom: 40,
  },
  iconStyle: {
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    width: '25%',
    justifyContent: 'center',
    alignItems: 'center',
    height: 50,
  },
  flex1: {
    flex: 1,
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  registerText: {
    fontFamily: 'Satoshi-Bold',
    textAlign: 'center',
    color: color.PRIMARY_700,
  },
  gap24: {
    gap: 24,
  },
});

export const stylesPass = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    flex: 1,
    position: 'relative',
  },
  containerLogo: {
    backgroundColor: color.PRIMARY_50,
    alignItems: 'center',
    paddingBottom: 50,
  },
  button: {
    borderRadius: 4,
    marginTop: 40,
  },
  textButton: {
    color: color.WHITE,
  },
  iconContainer: {
    position: 'absolute',
    right: 10,
    padding: 10,
  },
  textRestablecer: {
    marginTop: 16,
  },
});
