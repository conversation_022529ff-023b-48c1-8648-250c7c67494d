import React, { useEffect, useState } from 'react';
import {
  View,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Platform,
  Pressable,
} from 'react-native';
import { stylesCountry, stylesName } from './styles';
import { useNavigation } from '@react-navigation/native';
import { But<PERSON>, Text, TextBase } from '../../components/atoms';
import { InputForm } from '../../components/molecules';
import { Picker } from '@react-native-picker/picker';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { useGetCountryCodes } from '../../hooks/utils/getCodesCountry';
import { InfoCuitCuil } from '../../components/organisms/Modals/InfoCuitCuil';
import { useVerifyCuilCuit } from '../../hooks/SignUp/VerifyCuilCuit';
import SelectCodIos from '../../components/organisms/Modals/SelectCodIos';
import color from '../../theme/pallets/pallet';
import CheckIcon from '../../components/atoms/Icons/CheckIcon';
import CheckBoxIcon from '../../components/atoms/Icons/CheckBox';

interface CheckboxProps {
  checked: boolean;
  onChange: () => void;
  labelT: string;
  labelP: string;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  labelT,
  labelP,
}) => {
  return (
    <TouchableOpacity
      onPress={onChange}
      style={{
        flexDirection: 'row',
        gap: 6,
        paddingRight: 16,
        alignItems: 'center',
      }}
    >
      {checked ? (
        <CheckBoxIcon size={25} color={color.PRIMARY_500} />
      ) : (
        <View
          style={{
            width: 20,
            height: 20,
            borderWidth: 1,
            borderColor: color.NEUTRALS_400,
          }}
        />
      )}
      <Text
        variant="R7"
        numberOfLines={1}
        allowFontScaling
        adjustsFontSizeToFit
      >
        Acepto términos y condiciones y la política de privacidad
      </Text>
    </TouchableOpacity>
  );
};

export const NameScreen = ({ route }: any) => {
  const [isChecked, setIsChecked] = useState<boolean>(false);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [errorBoolean, seterrorBoolean] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const navigation: any = useNavigation();
  const country = route.params.country;
  const [formData, setFormData] = useState<{
    firstName: string;
    lastName: string;
    phoneNumber: string;
    country: string;
    codigoAreaTelefono: string;
    prefijoPais: string;
    cuitCuil: string;
  }>({
    firstName: '',
    lastName: '',
    phoneNumber: '',
    country: country,
    codigoAreaTelefono: '',
    prefijoPais: '+54',
    cuitCuil: '',
  });
  const handleContinue = () => {
    verifyCuilCuit(formData.cuitCuil);

    if (!validarCuit(formData.cuitCuil)) {
      return setError('El CUIL/CUIT ingresado no es válido.');
    }
  };
  const isFormValid = () => {
    const { firstName, lastName, phoneNumber, cuitCuil } = formData;
    return (
      isChecked &&
      firstName.trim() !== '' &&
      lastName.trim() !== '' &&
      phoneNumber.trim() !== '' &&
      cuitCuil.trim() !== ''
    );
  };

  const { verifyCuilCuit, success, setSuccess, exists, setExists } =
    useVerifyCuilCuit({
      setError: seterrorBoolean,
      setLoading,
    });

  useEffect(() => {
    if (exists) {
      setError('El CUIL/CUIT ingresado ya se encuentra registrado.');
    }
  }, [exists]);
  if (success) {
    navigation.navigate('EmailScreen', formData);
    setSuccess(false);
    setError('');
  }
  const { getCodes, successData, isLoaded } = useGetCountryCodes();

  const prefixes = successData
    ?.sort((a, b) => a.name.localeCompare(b.name))
    .map(code => code.prefix);

  useEffect(() => {
    if (success) {
      setError('');
    }
    getCodes();
  }, []);

  const handlePrefixChange = (key: string, value: string) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const validarCuit = (cuit: any) => {
    // Eliminamos cualquier caracter que no sea numérico
    cuit = cuit.replace(/\D/g, '');

    // Verificamos que el CUIT tenga la longitud correcta
    if (cuit.length !== 11) {
      return false;
    }

    // Coeficientes usados para calcular el dígito verificador
    const coeficientes = [5, 4, 3, 2, 7, 6, 5, 4, 3, 2];

    // Calculamos la suma de los productos de los coeficientes por los dígitos del CUIT
    let suma = 0;
    for (let i = 0; i < 10; i++) {
      suma += parseInt(cuit[i]) * coeficientes[i];
    }

    // Calculamos el dígito verificador
    let verificador = 11 - (suma % 11);

    // Reglas especiales para los dígitos verificadores 11 y 10
    if (verificador === 11) {
      verificador = 0;
    } else if (verificador === 10) {
      return false; // El CUIT es inválido si el resultado es 10
    }

    // El CUIT es válido si el verificador calculado coincide con el último dígito
    return verificador === parseInt(cuit[10]);
  };

  if (isLoaded) {
    return (
      <View style={stylesCountry.container}>
        <View style={[stylesCountry.logo, { flex: 1 }]}>
          <ActivityIndicator size={64} color={color.PRIMARY_500} />
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingComponent collapse>
      <ScrollView contentContainerStyle={stylesCountry.container}>
        <View>
          <View>
            <Text variant="B3" style={stylesCountry.title}>
              Crea una cuenta
            </Text>
            <Text variant="R7" style={stylesCountry.subtitle}>
              Solo te llevará algunos minutos
            </Text>
          </View>
          <View style={stylesName.containerInputs}>
            <Text style={stylesName.topText}>Nombres</Text>
            <InputForm
              placeholder="ej. Jorge"
              text={formData.firstName}
              setText={text => setFormData({ ...formData, firstName: text })}
              maxLength={30}
            />
            <Text style={stylesName.topText}>Apellidos</Text>
            <InputForm
              placeholder="ej. Ramirez González"
              text={formData.lastName}
              setText={text => setFormData({ ...formData, lastName: text })}
              maxLength={30}
            />
            <Text style={stylesName.topText}>CUIL/CUIT</Text>
            <InputForm
              placeholder="XX-XXXXXXXX-X"
              text={formData.cuitCuil}
              setText={text => {
                const onlyNumbers = text.replace(/[^0-9]/g, '');
                setFormData({ ...formData, cuitCuil: onlyNumbers });
                setError('');
                if (onlyNumbers === '') {
                  setExists(false);
                }
              }}
              error={errorBoolean}
              numeric={true}
              maxLength={11}
            />
            {errorBoolean && (
              <TextBase color={color.RED_700} style={{ marginTop: 4 }}>
                {error}
              </TextBase>
            )}
            <Pressable
              style={{ marginTop: 4, marginBottom: 16 }}
              onPress={() => setModalVisible(true)}
            >
              <TextBase color={color.PRIMARY_500} size="s">
                ¿Cómo averiguo mi CUIL o CUIT?
              </TextBase>
            </Pressable>
            <Text style={stylesName.topText}>Teléfono móvil</Text>
            <View style={stylesName.inputNumber}>
              {Platform.OS === 'ios' ? (
                <>
                  <InputForm
                    onFocus={() => setShowDropdown(true)}
                    placeholder="+54"
                    text={formData.prefijoPais}
                    setText={() => { }}
                    numeric={true}
                    maxLength={4}
                  />
                  <SelectCodIos
                    data={prefixes ?? []}
                    onChange={handlePrefixChange}
                    name="prefijoPais"
                    showDropdown={showDropdown}
                    setShowDropdown={setShowDropdown}
                  />
                </>
              ) : (
                <View
                  style={{
                    height: 45,
                    width: 80,
                    borderWidth: 1,
                    borderRadius: 12,
                    borderColor: color.NEUTRALS_200,
                    justifyContent: 'center',
                    overflow: 'hidden',
                  }}
                >
                  <Picker
                    selectedValue={formData.prefijoPais}
                    itemStyle={{
                      height: 45,
                    }}
                    placeholder="+54"
                    style={stylesName.pickerPhone}
                    onValueChange={itemValue =>
                      setFormData({ ...formData, prefijoPais: itemValue })
                    }
                  >
                    {successData
                      .sort((a, b) => a.name.localeCompare(b.name))
                      .map((code, index) => (
                        <Picker.Item
                          key={index}
                          label={code.prefix + ' ' + code.name}
                          value={code.prefix}
                        />
                      ))}
                  </Picker>
                </View>
              )}
              {!showDropdown && (
                <>
                  <InputForm
                    placeholder="11"
                    text={formData.codigoAreaTelefono}
                    setText={text => {
                      const onlyNumbers = text.replace(/[^0-9]/g, '');
                      setFormData({
                        ...formData,
                        codigoAreaTelefono: onlyNumbers,
                      });
                      setError('');
                    }}
                    numeric={true}
                    maxLength={4}
                  />
                  <InputForm
                    placeholder="XXXX XXXX"
                    text={formData.phoneNumber}
                    setText={text => {
                      const onlyNumbers = text.replace(/[^0-9]/g, '');
                      setFormData({
                        ...formData,
                        phoneNumber: onlyNumbers,
                      });
                      setError('');
                    }}
                    numeric={true}
                    maxLength={10}
                    style={{ flex: 1 }}
                  />
                </>
              )}
            </View>
          </View>
        </View>
        <View style={stylesName.checkboxStyle}>
          <Checkbox
            labelT="términos y condiciones"
            labelP="política de privacidad"
            checked={isChecked}
            onChange={() => setIsChecked(!isChecked)}
          />
          <Button
            text="Siguiente"
            onPress={handleContinue}
            disabled={!isFormValid()}
            loading={loading}
          />
        </View>
        <InfoCuitCuil
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
        />
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
};
