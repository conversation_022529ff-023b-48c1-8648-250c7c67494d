import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const StarCheckIcon: FC<IconProps> = ({ color = '#FF0033', size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M19.1666 9.99167L17.1332 7.66667L17.4166 4.59167L14.4083 3.90833L12.8333 1.25L9.99992 2.46667L7.16658 1.25L5.59158 3.90833L2.58325 4.58333L2.86659 7.66667L0.833252 9.99167L2.86659 12.3167L2.58325 15.4L5.59158 16.0833L7.16658 18.75L9.99992 17.525L12.8333 18.7417L14.4083 16.0833L17.4166 15.4L17.1332 12.325L19.1666 9.99167ZM15.8749 11.225L15.4083 11.7667L15.4749 12.475L15.6249 14.1L14.0416 14.4583L13.3416 14.6167L12.9749 15.2333L12.1499 16.6333L10.6666 15.9917L9.99992 15.7083L9.34158 15.9917L7.85825 16.6333L7.03325 15.2417L6.66658 14.625L5.96658 14.4667L4.38325 14.1083L4.53325 12.475L4.59992 11.7667L4.13325 11.225L3.05825 10L4.13325 8.76667L4.59992 8.225L4.52492 7.50833L4.37492 5.89167L5.95825 5.53333L6.65825 5.375L7.02492 4.75833L7.84992 3.35833L9.33325 4L9.99992 4.28333L10.6583 4L12.1416 3.35833L12.9666 4.75833L13.3333 5.375L14.0333 5.53333L15.6166 5.89167L15.4666 7.51667L15.3999 8.225L15.8666 8.76667L16.9416 9.99167L15.8749 11.225Z"
        fill={color}
      />
      <Path
        d="M8.40837 11.4583L6.47503 9.51668L5.2417 10.7583L8.40837 13.9333L14.525 7.80002L13.2917 6.55835L8.40837 11.4583Z"
        fill={color}
      />
    </Svg>
  );
};

export default StarCheckIcon;
