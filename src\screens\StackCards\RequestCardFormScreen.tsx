import React, { FC, useCallback, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import TextBase from '../../components/atoms/TextBase';
import { Button } from '../../components/atoms';
import SelectInput from '../../components/atoms/SelectInput';
import { InputWithLabel } from '../../components/atoms/InputWithLabel';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { getArgentinaProvinces } from '../../services/utils';
import { containsAlphanumeric } from '../../helpers/stringHelper';
import { useFocusEffect } from '@react-navigation/native';
import color from '../../theme/pallets/pallet';

const RequestCardScreen: FC<any> = ({ route, navigation }) => {
  const { virtualCardSelected, physicalCardSelected } = route.params;

  const [provinces, setProvinces] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [formValid, setFormValid] = useState(false);

  const [address, setAddress] = useState({
    region: '',
    city: '',
    neighborhood: '',
    zipCode: '',
    streetName: '',
    streetNumber: '',
    floor: '',
    apartment: '',
    additionalInfo: '',
  });

  useFocusEffect(
    useCallback(() => {
      getProvinces();
    }, []),
  );

  const navigateToRequestCardScreen = () => {
    navigation.navigate('StackCards', {
      screen: 'RequestCardTermsAndConditions',
      params: {
        virtualCardSelected,
        physicalCardSelected,
        address,
      },
    });
  };

  const getProvinces = async () => {
    const response = await getArgentinaProvinces();
    const provincesNames =
      response && response?.map((province: any) => province.nombre);

    setProvinces(provincesNames);
  };

  const handleAddressChange = (key: string, value: string) => {
    const updatedAddress = { ...address, [key]: value };
    setAddress(updatedAddress);
    validateForm(updatedAddress);
  };

  const validateForm = (updatedAddress: typeof address) => {
    const isFormValid = [
      updatedAddress.region,
      updatedAddress.city,
      updatedAddress.neighborhood,
      updatedAddress.zipCode,
      updatedAddress.streetName,
      updatedAddress.streetNumber,
    ].every(field => containsAlphanumeric(field) && field.trim() !== '');
    setFormValid(isFormValid);
  };

  return (
    <KeyboardAvoidingComponent scrollEnabled={!showDropdown}>
      <ScrollView style={styles.container}>
        <View style={{ gap: 18 }}>
          <TextBase style={styles.title}>Tarjeta Prepaga</TextBase>
          <TextBase style={styles.text}>
            Ingresa tu domicilio para continuar.
          </TextBase>
        </View>
        <View style={styles.botContainer}>
          <SelectInput
            label="Provincia"
            placeholder="Selecciona tu provincia"
            data={provinces}
            input={address.region}
            name={'region'}
            onChange={handleAddressChange}
            showDropdown={showDropdown}
            setShowDropdown={setShowDropdown}
          />
          <InputWithLabel
            label="Ciudad"
            placeholder="Ingresa tu ciudad"
            maxLength={100}
            value={address.city}
            name={'city'}
            onChange={handleAddressChange}
          />
          <InputWithLabel
            label="Localidad"
            placeholder="Ingresa tu localidad o barrio"
            maxLength={100}
            value={address.neighborhood}
            name={'neighborhood'}
            onChange={handleAddressChange}
          />
          <InputWithLabel
            label="Código postal"
            placeholder="Ingresa solo números"
            maxLength={4}
            value={address.zipCode}
            name={'zipCode'}
            onChange={handleAddressChange}
          />
          <InputWithLabel
            label="Calle"
            placeholder="Nombre de la calle"
            maxLength={100}
            value={address.streetName}
            name={'streetName'}
            onChange={handleAddressChange}
          />
          <View style={styles.flexRow}>
            <InputWithLabel
              label="Número"
              placeholder="ej. 843"
              value={address.streetNumber}
              name={'streetNumber'}
              onChange={handleAddressChange}
            />
            <InputWithLabel
              label="Piso"
              placeholder="ej. PB"
              value={address.floor}
              name={'floor'}
              onChange={handleAddressChange}
            />
            <InputWithLabel
              label="Depto."
              placeholder="ej. C"
              value={address.apartment}
              name={'apartment'}
              onChange={handleAddressChange}
            />
          </View>
          <InputWithLabel
            label="Información adicional"
            value={address.additionalInfo}
            name={'additionalInfo'}
            onChange={handleAddressChange}
            maxLength={100}
            numberOfLines={4}
          />
        </View>
        <View style={styles.buttonContainer}>
          <Button
            onPress={navigateToRequestCardScreen}
            text="Continuar"
            disabled={!formValid}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    height: '100%',
    backgroundColor: color.WHITE,
  },
  flexRow: {
    flexDirection: 'row',
    gap: 16,
    width: '100%',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
  },
  buttonContainer: {
    paddingVertical: 20,
    alignItems: 'center',
    display: 'flex',
  },
  text: {
    lineHeight: 20,
    letterSpacing: 0.17,
  },
  botContainer: {
    paddingVertical: 24,
    gap: 24,
    flex: 1,
  },
});

export default RequestCardScreen;
