import * as yup from 'yup';
import { PASSWORD_REGEX } from '../regex';

export const loginValidationSchema = yup.object().shape({
  email: yup
    .string()
    .email('El correo electronico ingresado es incorrecto')
    .required('Debe ingresarse un email'),
  password: yup
    .string()
    .matches(PASSWORD_REGEX, 'Contraseña invalida')
    .min(6, 'La contraseña debe tener al menos 6 caracteres')
    .max(50, 'Máximo 50 caracteres')
    .required('Debe ingresarse una contraseña'),
});
