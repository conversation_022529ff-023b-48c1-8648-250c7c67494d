import React from 'react';
import { Modal, View } from 'react-native';
import { styles } from './styles';
import { TextBase } from '../../../atoms';
import ButtonOutline from '../../../atoms/ButtonOutline';
import CheckWithBorderIcon from '../../../atoms/Icons/CheckWithBorderIcon';
import WarningIcon from '../../../atoms/Icons/Warning';
import color from '../../../../theme/pallets/pallet';

type Props = {
  modalVisible: boolean;
  onPress: () => void;
  error?: boolean;
  setModalVisible: (value: boolean) => void;
};

export const SuccessModal = ({
  modalVisible,
  onPress,
  error,
  setModalVisible,
}: Props) => {
  const handlePress = () => {
    error && setModalVisible(false);
    !error && onPress();
  };
  return (
    <Modal animationType="slide" transparent={true} visible={modalVisible}>
      <View style={styles.modal}>
        <View style={styles.indicator}>
          {error ? <WarningIcon /> : <CheckWithBorderIcon size={24} />}
          <View>
            <TextBase
              size="xl"
              color={color.NEUTRALS_800}
              type="Bold"
              style={{ textAlign: 'center' }}
            >
              {error ? 'Error al eliminar el contacto' : 'Contacto eliminado'}
            </TextBase>
            <TextBase
              size="m"
              color={color.NEUTRALS_800}
              type="Regular"
              style={{ marginTop: 6, textAlign: 'center' }}
            >
              {error
                ? 'Por favor intente nuevamente mas tarde.'
                : 'Has eliminado este contacto.'}
            </TextBase>
          </View>
          <ButtonOutline text="Entendido" onPress={handlePress} />
        </View>
      </View>
    </Modal>
  );
};
