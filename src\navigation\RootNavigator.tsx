import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { rootScreens } from '../constants/screens';
import { WebViewScreen } from '../screens/WevView';
import StackCreateAccount from './StackCreateAccount';
import WelcomeScreen from '../screens/WelcomeScreen';
import StackUnlock from './StackUnlock';
import { ErrorScreen } from '../screens';
import StackVerifyAccount from './StackVerifyAccount';
import { ChangePassword } from '../screens/StackVerifyAccount/ChangePassword';
import { Success } from '../screens/StackVerifyAccount/Success';
import { useUnlockStore } from '../stores';
import BlockingBiometricsScreen from '../screens/BlockingBiometricsScreen';
import LoginScreen from '../screens/LoginScreen';
import GoBack from '../components/organisms/Buttons/GoBackButton';
import Logo from '../components/atoms/Icons/Logo';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator();

function ArrowBack() {
  return <GoBack />;
}

function LogoToshify() {
  return <Logo height={24} width={120} />;
}
const RootNavigator = () => {
  const unlockMethod = useUnlockStore(state => state.unlockMethod);

  return (
    <Stack.Navigator>
      {unlockMethod?.type === 'biometric' && (
        <Stack.Screen
          name="BlockingBiometricsScreen"
          component={BlockingBiometricsScreen}
        />
      )}
      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name="BienvenidaScreen"
        component={WelcomeScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: LogoToshify,
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          headerTitleAlign: 'center',
        }}
        name="Login"
        component={LoginScreen}
      />

      {rootScreens.map((screen, index) => (
        <Stack.Screen
          name={screen.name}
          component={screen.component}
          key={index}
          options={{ headerShown: false }}
        />
      ))}
      <Stack.Screen
        name="WebViewScreen"
        component={WebViewScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="StackCreateAccount"
        component={StackCreateAccount}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="StackUnlock"
        component={StackUnlock}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ErrorScreen"
        component={ErrorScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="StackVerifyAccount"
        component={StackVerifyAccount}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ChangePassword"
        component={ChangePassword}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Success"
        component={Success}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default RootNavigator;
