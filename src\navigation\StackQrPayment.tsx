import React from 'react';

import GoBack from '../components/organisms/Buttons/GoBackButton';
import { useNavigation } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { QrScreen } from '../screens/StackQrPayment/QrScreen';
import { QrPaymentScreen } from '../screens/StackQrPayment/QrPaymentScreen';
import { QrConfirmScreen } from '../screens/StackQrPayment/QrConfirmScreen';
import { QrSuccessScreen } from '../screens/StackQrPayment/QrSuccessScreen';
import { QrErrorScreen } from '../screens/StackQrPayment/QrErrorScreen';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<any>();

function ArrowBack() {
  const navigation = useNavigation();
  return <GoBack onPress={() => navigation.goBack()} />;
}

export default function StackQrPayment() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'QrScreen'}
        component={QrScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'QrPayment'}
        component={QrPaymentScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'QrConfirm'}
        component={QrConfirmScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          gestureEnabled: false,
        }}
        name={'QrSuccess'}
        component={QrSuccessScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerStyle: {
            backgroundColor: '#FFF6F6',
          },
          gestureEnabled: false,
        }}
        name={'QrError'}
        component={QrErrorScreen}
      />
    </Stack.Navigator>
  );
}
