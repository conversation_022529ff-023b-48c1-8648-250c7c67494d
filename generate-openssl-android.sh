#!/bin/sh

# Step 1: Read the API_URL from the .env file and remove the 'https://' prefix if it exists
API_URL=$(grep -E '^API_URL=' .env | cut -d '=' -f2)
API_URL=${API_URL#https://}  # Remove 'https://' if present
echo "Using API URL: $API_URL"

# Step 2: Fetch the SSL certificate from the server
openssl s_client -connect "$API_URL:443" -showcerts </dev/null > certificate.pem
echo "Certificate fetched, saved to certificate.pem."

# Step 3: Extract the public key from the certificate
openssl x509 -pubkey -noout -in certificate.pem > publickey.pem
echo "Public key extracted, saved to publickey.pem."

# Step 4: Generate the SHA-256 hash of the public key (binary format)
openssl pkey -pubin -in publickey.pem -outform der | openssl dgst -sha256 -binary > publickey.sha256
echo "SHA-256 hash generated, saved to publickey.sha256."

# Step 5: Base64 encode the SHA-256 hash
openssl enc -base64 -in publickey.sha256 -out publickey.sha256.base64
echo "Base64 encoding complete, saved to publickey.sha256.base64."
echo "---- Your SSL HASHED KEY -----"
# Step 6: Output the Base64-encoded SHA-256 hash
cat publickey.sha256.base64

# Step 7: Remove all temporary files
rm -f certificate.pem publickey.pem publickey.sha256 publickey.sha256.base64
