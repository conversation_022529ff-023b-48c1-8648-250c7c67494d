import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/core';
import Logo from '../components/atoms/Icons/Logo';
import { Button } from '../components/atoms';
import ButtonOutline from '../components/atoms/ButtonOutline';
import IntroSwiper from '../components/shared/IntroSwiper';
import color from '../theme/pallets/pallet';

const WelcomeScreen = () => {
  const navigation: any = useNavigation();

  const handleContinue = () => {
    navigation.navigate('StackCreateAccount', { screen: 'CountryScreen' });
  };

  const handleSkip = () => {
    navigation.navigate('Login');
  };

  return (
    <View style={styles.container}>
      <View style={styles.logo}>
        <Logo height={100} width={100} />
      </View>
      <IntroSwiper
        titleOne="La billetera del transporte que te suma al negocio"
        subtitleOne="Podrás invertir tu dinero en cualquier momento y generar ganancias mensuales."
        titleTwo="Realiza pagos de la forma que prefieras"
        subtitleTwo="Utiliza la app para pagar con QR o aprovecha la tarjeta de débito para comprar."
        titleThree="Resguarda tu dinero en un lugar seguro"
        subtitleThree="Medidas de seguridad para que siempre seas tú quien decida que hacer con el dinero."
      />
      <View style={styles.buttonsContainer}>
        <Button onPress={handleContinue} text="Abrir una cuenta gratuita" />
        <View style={styles.buttonSpacer} />
        <ButtonOutline
          onPress={handleSkip}
          text="Iniciar sesión en mi cuenta"
          variant="B6"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.WHITE,
  },
  buttonsContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginBottom: 50,
    alignItems: 'center',
    display: 'flex',
  },
  omitir: {
    color: color.PRIMARY_500,
    marginTop: 20,
  },
  logo: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonSpacer: {
    marginVertical: 10,
  },
});

export default WelcomeScreen;
