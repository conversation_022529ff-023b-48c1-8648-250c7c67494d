import Svg, { Path } from 'react-native-svg'
import { LoanIconTypeColor } from './types';
import { FC } from 'react';

const CarRounded: FC<LoanIconTypeColor> = ({ size, iconColor }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32" fill="none">
      <Path d="M21.7667 10.175C21.6 9.68331 21.1333 9.33331 20.5833 9.33331H11.4167C10.8667 9.33331 10.4083 9.68331 10.2333 10.175L8.5 15.1666V21.8333C8.5 22.2916 8.875 22.6666 9.33333 22.6666H10.1667C10.625 22.6666 11 22.2916 11 21.8333V21H21V21.8333C21 22.2916 21.375 22.6666 21.8333 22.6666H22.6667C23.125 22.6666 23.5 22.2916 23.5 21.8333V15.1666L21.7667 10.175ZM11.7083 11H20.2833L21.1833 13.5916H10.8083L11.7083 11ZM21.8333 19.3333H10.1667V15.1666H21.8333V19.3333Z" fill={iconColor} />
      <Path d="M12.25 18.5C12.9404 18.5 13.5 17.9403 13.5 17.25C13.5 16.5596 12.9404 16 12.25 16C11.5596 16 11 16.5596 11 17.25C11 17.9403 11.5596 18.5 12.25 18.5Z" fill={iconColor} />
      <Path d="M19.75 18.5C20.4404 18.5 21 17.9403 21 17.25C21 16.5596 20.4404 16 19.75 16C19.0596 16 18.5 16.5596 18.5 17.25C18.5 17.9403 19.0596 18.5 19.75 18.5Z" fill={iconColor} />
    </Svg>
  )
}

export default CarRounded;