import * as React from "react"
import Svg, {
  G,
  Rect,
  Circle,
  Path,
  Ellipse,
  Defs,
  LinearGradient,
  Stop,
  ClipPath
} from "react-native-svg"
import { IconProps } from '../types';
import { Dimensions } from "react-native";

export const getPrepaidBannerSize = () => {
  const { width } = Dimensions.get('window');

  const originalWidth = 360;
  const originalHeight = 176;

  const aspectRatio = originalHeight / originalWidth;
  const svgHeight = width * aspectRatio;

  const height = svgHeight;

  return { width, height };
};

const GetPrepaidCardBanner: React.FC<IconProps> = ({ width, height}) => {
  return (
    <Svg
      width={width}
      height={height}   
      viewBox="0 0 360 176"
      fill="none"
    >
      <G filter="url(#filter0_d_9223_6832)">
        <G clipPath="url(#clip0_9223_6832)">
          <Rect
            x={16}
            y={16}
            width={328}
            height={144}
            rx={16}
            fill="url(#paint0_linear_9223_6832)"
          />
          <Circle opacity={0.5} cx={255} cy={137} r={80} fill="#FF583F" />
          <Path
            d="M432.5 110c0 47.226-37.837 85.5-84.5 85.5s-84.5-38.274-84.5-85.5 37.837-85.5 84.5-85.5 84.5 38.274 84.5 85.5z"
            stroke="#FF0033"
          />
          <Circle opacity={0.5} cx={21} cy={16} r={133.5} stroke="#FF583F" />
          <G filter="url(#filter1_dd_9223_6832)">
            <G clipPath="url(#clip1_9223_6832)">
              <G clipPath="url(#clip2_9223_6832)">
                <Rect
                  width={150.933}
                  height={96.6337}
                  rx={5.52192}
                  transform="matrix(.99607 .08854 -.17503 .98456 212.914 34)"
                  fill="#841A0A"
                />
                <Path
                  d="M223.607 54.322c-.534-.047-.99-.177-1.368-.39a2.763 2.763 0 01-.895-.817 2.957 2.957 0 01-.438-1.09c-.068-.398-.065-.806.009-1.222l.033-.188a3.62 3.62 0 01.41-1.135 3.66 3.66 0 01.778-.963 3.32 3.32 0 012.504-.784c.546.048 1.011.194 1.395.436.385.237.674.549.865.936.192.382.268.814.228 1.296l-1.219-.108a1.242 1.242 0 00-.177-.71 1.365 1.365 0 00-.527-.498 1.883 1.883 0 00-.765-.224 2.087 2.087 0 00-.821.083c-.251.081-.476.21-.674.382a2.284 2.284 0 00-.504.641 3.004 3.004 0 00-.283.86 2.749 2.749 0 00-.02.944c.048.282.143.527.284.737.143.203.331.367.563.492.233.12.505.193.818.22.415.037.794-.016 1.137-.159.344-.148.612-.356.804-.622l-.388.963.286-1.606 1.117.1-.246 1.383c-.339.38-.759.664-1.26.851-.5.181-1.049.245-1.646.192zm.235-2.443l.161-.905 3.394.301-.161.906-3.394-.302zM229.935 54.885a2.653 2.653 0 01-1.048-.293 2.11 2.11 0 01-.699-.6 2.182 2.182 0 01-.358-.813 2.445 2.445 0 01-.004-.902l.032-.18c.057-.319.167-.616.332-.89.171-.28.384-.521.639-.724.263-.208.561-.361.896-.458.336-.104.7-.138 1.093-.103.392.035.733.132 1.022.29.291.153.523.354.699.6.182.242.304.514.367.814.063.295.066.602.009.92l-.032.18a2.553 2.553 0 01-.328.873 2.628 2.628 0 01-.623.726 2.676 2.676 0 01-.887.459 2.885 2.885 0 01-1.11.101zm.181-1.017c.29.026.546-.015.767-.122.222-.114.404-.274.544-.481.142-.213.238-.46.288-.738.05-.285.041-.54-.029-.766a1.04 1.04 0 00-.368-.562c-.175-.149-.403-.236-.681-.26a1.403 1.403 0 00-1.313.612 1.802 1.802 0 00-.296.737c-.049.279-.043.534.02.765.07.226.192.414.368.562.177.143.41.228.7.253zM233.586 55.045l1.109-6.236 1.185.105-1.109 6.236-1.185-.105zm.371-5.4l.158-.888 1.765.157-.158.889-1.765-.157zM236.184 55.276l1.109-6.236 1.185.105-1.108 6.236-1.186-.105zm.371-5.4l.158-.888 1.765.157-.158.889-1.765-.157zM240.767 55.848c-.398-.036-.736-.135-1.013-.298a1.95 1.95 0 01-.645-.613 2.37 2.37 0 01-.305-.817 2.652 2.652 0 01.01-.884l.029-.162c.054-.308.155-.6.303-.876.154-.281.347-.524.58-.73a2.4 2.4 0 01.829-.472c.314-.111.662-.15 1.043-.116.5.045.898.193 1.194.445.303.246.507.557.614.93.108.369.125.758.053 1.168l-.076.427-4.102-.364.129-.726 3.36.298-.431.326c.053-.296.055-.553.008-.771a.909.909 0 00-.306-.522c-.15-.129-.356-.205-.618-.228a1.261 1.261 0 00-.697.12 1.306 1.306 0 00-.511.484c-.13.214-.223.477-.279.79-.051.291-.052.554-.001.79.051.23.159.42.323.567.166.142.393.226.683.251.29.026.536-.01.738-.108a.914.914 0 00.428-.404l1.091.097a2.18 2.18 0 01-1.382 1.291c-.319.105-.669.14-1.049.107zM246.013 56.21c-.426-.038-.769-.123-1.028-.256a1.055 1.055 0 01-.514-.619c-.089-.28-.093-.644-.013-1.094l.755-4.202 1.109.098-.769 4.28c-.041.227-.012.409.085.545.103.13.269.206.496.226l.725.065-.18 1.016-.666-.059zm-1.929-4.059l.155-.871 3.275.291-.155.871-3.275-.29z"
                  fill="#fff"
                />
                <Path
                  d="M251.762 50.32c-.463.967-1.601 1.447-2.54 1.071-.94-.375-1.327-1.463-.863-2.43.463-.967 1.6-1.447 2.54-1.072.94.376 1.326 1.464.863 2.43zM247.082 48.451c-.231.484-.8.724-1.27.536-.47-.188-.663-.732-.432-1.215.232-.484.801-.724 1.271-.536.47.188.663.732.431 1.215z"
                  fill="#FDC228"
                />
                <Path
                  d="M313.89 58.3l.083-.467.897.08a.984.984 0 00.492-.067.836.836 0 00.335-.28.995.995 0 00.174-.415.888.888 0 00-.023-.439.61.61 0 00-.227-.324.875.875 0 00-.454-.15l-.896-.08.083-.468.797.071c.33.03.596.107.797.231a.977.977 0 01.429.48c.08.199.097.427.052.684l-.016.087a1.404 1.404 0 01-.289.654c-.147.178-.342.31-.584.395a2.063 2.063 0 01-.853.079l-.797-.071zm-.675 1.343l.71-3.99.539.047-.71 3.99-.539-.047zm3.519.313l.709-3.991.539.048-.709 3.99-.539-.047zm2.48.22l-1.017-1.885.633.056 1.039 1.887-.655-.058zm-1.89-1.626l.084-.473 1.078.096a.914.914 0 00.453-.065.82.82 0 00.331-.258.94.94 0 00.173-.41.81.81 0 00-.025-.421.635.635 0 00-.229-.313.78.78 0 00-.416-.148l-1.078-.096.084-.467.979.087c.293.026.542.092.746.199a.943.943 0 01.44.448c.089.192.108.43.057.717l-.015.087c-.051.286-.155.515-.312.685-.153.17-.347.29-.583.357a2.127 2.127 0 01-.788.062l-.979-.087zm3.261 1.748l.705-3.969.539.048-.705 3.969-.539-.048zm.429.038l.085-.478 1.947.173-.085.478-1.947-.173zm.316-1.778l.085-.478 1.777.158-.086.478-1.776-.158zm.304-1.712l.085-.479 1.892.168-.085.479-1.892-.168zm2.923 2.402l.083-.467.897.08a.98.98 0 00.492-.067.83.83 0 00.335-.28.97.97 0 00.174-.415.889.889 0 00-.022-.438.618.618 0 00-.228-.324.873.873 0 00-.453-.151l-.897-.08.083-.467.798.07c.33.03.595.107.797.232a.986.986 0 01.429.48c.08.198.097.426.051.683l-.015.087a1.405 1.405 0 01-.29.654c-.147.178-.342.31-.584.395-.238.082-.522.108-.852.08l-.798-.072zm-.674 1.343l.709-3.99.539.047-.709 3.991-.539-.048zm2.804.25l2.152-3.84.914.08.785 4.102-.556-.05-.704-************-.649-.058.304-.106-1.948 3.505-.539-.048zm1.075-1.11l.266-.461 1.733.154.096.494-2.095-.186zm5.159 1.763a2.146 2.146 0 01-.82-.228 1.709 1.709 0 01-.579-.487 1.79 1.79 0 01-.302-.69 2.213 2.213 0 01-.001-.84l.021-.12c.045-.253.133-.494.263-.721.134-.231.302-.434.504-.607a2.07 2.07 0 01.707-.396c.266-.09.553-.122.861-.094.334.03.619.12.857.27.238.145.417.338.534.577.119.235.165.5.138.796l-.55-.049a.953.953 0 00-.122-.568.989.989 0 00-.392-.372 1.511 1.511 0 00-1.15-.102c-.189.057-.36.148-.513.275a1.689 1.689 0 00-.379.468 2.033 2.033 0 00-.22.638 2.118 2.118 0 00-.017.727c.041.21.117.39.228.54.112.146.253.262.424.347.175.085.373.138.593.157.311.028.595-.01.852-.112.261-.105.47-.252.626-.441l-.313.63.21-1.18.506.045-.168.946a2.093 2.093 0 01-.79.482 2.41 2.41 0 01-1.008.109zm.137-1.59l.079-.445 2.151.19-.08.447-2.15-.191zm2.181 1.696l2.152-3.84.913.081.785 4.102-.556-.05-.704-************-.649-.058.304-.106-1.947 3.505-.539-.048zm1.074-1.108l.266-.462 1.733.154.096.494-2.095-.186z"
                  fill="#fff"
                />
                <Path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M350.491 55.5c.25-.116.525-.009.613.239.589 1.655.729 3.486.405 5.308a11.742 11.742 0 01-2.25 5.07c-.172.226-.476.281-.677.124-.202-.156-.225-.465-.053-.69a10.627 10.627 0 002.036-4.588c.293-1.649.167-3.305-.366-4.802a.546.546 0 01.292-.66zm-2.27.904c.25-.117.525-.01.613.238.466 1.308.576 2.755.32 4.195a9.28 9.28 0 01-1.779 4.009c-.172.225-.475.28-.677.124-.201-.157-.225-.466-.052-.69a8.163 8.163 0 001.564-3.527 7.23 7.23 0 00-.281-3.69.546.546 0 01.292-.66zm-2.157.82c.25-.115.524-.007.611.242.342.977.422 2.056.231 3.131a6.984 6.984 0 01-1.32 2.993c-.171.226-.474.283-.677.127-.202-.155-.227-.464-.056-.69a5.868 5.868 0 001.109-2.514c.16-.903.093-1.81-.194-2.63a.547.547 0 01.296-.658zm-2.203.916c.244-.13.525-.042.629.198.278.648.358 1.377.228 2.105a4.216 4.216 0 01-.958 1.999c-.184.215-.488.251-.679.082-.19-.17-.196-.482-.012-.697.368-.429.611-.938.705-1.468a2.702 2.702 0 00-.169-1.546c-.103-.24.011-.541.256-.673z"
                  fill="#F7F8FE"
                />
                <Path
                  opacity={0.5}
                  d="M284.182 32.511c-4.248 23.896-27.29 41.525-51.465 39.376-24.175-2.149-40.328-23.262-36.08-47.158 4.248-23.895 27.289-41.524 51.464-39.376 24.175 2.15 40.329 23.263 36.081 47.158z"
                  stroke="#FF0033"
                  strokeWidth={0.46016}
                />
                <Ellipse
                  opacity={0.5}
                  cx={44.1754}
                  cy={44.1754}
                  rx={44.1754}
                  ry={44.1754}
                  transform="matrix(.99607 .08854 -.17503 .98456 266.712 87.565)"
                  fill="#FF583F"
                />
                <Circle
                  cx={37.273}
                  cy={37.273}
                  r={37.0429}
                  transform="matrix(.99607 .08854 -.17503 .98456 311.161 75.869)"
                  stroke="#FFE7E3"
                  strokeWidth={0.46016}
                />
                <Path
                  d="M212.465 108.061l-.85-.791.129-.723 1.1-.617.266.706-.84.248-.027.152.728.381-.506.644zm-.443-2.752l-.339 1.175-.699.305-1.018-.58.515-.625.577.567.148-.064.031-.835.785.057zm-2.247 1.972l1.189-.384.571.418-.082 1.197-.782-.076.262-.814-.121-.088-.751.449-.286-.702zm6.35 1.105l-.85-.79.128-.724 1.101-.617.265.707-.839.247-.027.152.728.381-.506.644zm-.443-2.752l-.339 1.175-.7.305-1.018-.58.515-.624.577.566.149-.064.031-.834.785.056zm-2.247 1.972l1.189-.384.571.418-.083 1.198-.781-.076.262-.815-.121-.088-.752.449-.285-.702zm6.349 1.106l-.85-.791.129-.723 1.1-.618.266.707-.839.248-.027.152.727.38-.506.645zm-.442-2.752l-.34 1.174-.699.305-1.018-.58.515-.624.577.567.148-.064.031-.835.786.057zm-2.248 1.971l1.19-.384.571.418-.083 1.198-.782-.076.263-.814-.122-.088-.751.448-.286-.702zm6.35 1.106l-.85-.791.128-.723 1.101-.617.266.706-.84.248-.027.152.728.38-.506.645zm-.443-2.752l-.339 1.175-.699.305-1.019-.58.516-.625.577.567.148-.064.031-.835.785.057zm-2.247 1.972l1.189-.384.571.418-.082 1.197-.782-.076.262-.814-.121-.088-.751.448-.286-.701zm4.644 3.292l.169-.951c.036-.199.098-.369.189-.512.095-.141.225-.263.391-.364a2.64 2.64 0 01.636-.279l.815-.256c.206-.063.365-.15.476-.26a.772.772 0 00.221-.438.588.588 0 00-.092-.472c-.094-.129-.253-.203-.475-.223-.223-.02-.403.027-.54.139a.768.768 0 00-.261.479l-1.02-.09c.052-.292.162-.549.332-.77.17-.225.394-.394.67-.507.277-.113.601-.153.973-.12s.671.126.896.279c.229.154.388.346.477.578.094.231.116.485.067.759l-.013.077c-.063.355-.214.64-.452.855-.238.211-.577.387-1.02.528l-.808.257a.927.927 0 00-.306.146.393.393 0 00-.12.235l-.056.317-.214-.232 2.567.228-.159.895-3.343-.298zm4.039-.543l.193-1.084 2.274-2.633.721.599-1.946 2.256.066.141 2.804.249-.146.825-3.966-.353zm2.3 1.107l.593-3.336.969.086-.593 3.336-.969-.086zm3.712.452c-.372-.033-.68-.131-.925-.295a1.39 1.39 0 01-.52-.632 1.5 1.5 0 01-.069-.818l1.013.09a.718.718 0 00.029.389c.05.112.129.205.237.279.109.07.241.112.395.125a.893.893 0 00.448-.063.84.84 0 00.327-.261.948.948 0 00.169-.404.828.828 0 00-.023-.421.608.608 0 00-.217-.315.742.742 0 00-.405-.146.969.969 0 00-.434.058.771.771 0 00-.318.217l-.989-.088.702-2.592 2.843.253-.158.888-2.425-.216.451-.231-.408 1.491-.233-.085c.156-.141.332-.254.53-.339.203-.09.452-.121.747-.095.342.03.621.126.835.287.22.157.373.357.46.601.092.24.113.502.063.785l-.015.083c-.05.283-.168.541-.352.774a1.818 1.818 0 01-.729.541c-.296.128-.639.175-1.029.14zm3.189.161l.701-3.945.244.241-1.2-.107.16-.901 1.964.175-.823 4.63-1.046-.093z"
                  fill="#fff"
                />
                <Path
                  d="M207.711 118.978l.705-3.968.88.078 1.42 3.671.176.016-.127.088.638-3.588.517.046-.706 3.969-.891-.079-1.42-3.672-.176-.016.127-.088-.637 3.588-.506-.045zm6.448.673c-.359-.032-.657-.123-.895-.273a1.669 1.669 0 01-.555-.557 2.054 2.054 0 01-.262-.697 2.107 2.107 0 010-.691l.022-.119c.041-.236.126-.47.253-.701a2.24 2.24 0 01.501-.618c.207-.184.45-.325.728-.422.282-.096.599-.129.951-.097.352.031.645.118.881.26.239.143.427.322.564.536.137.211.227.44.269.687a2.09 2.09 0 010 .723l-.022.12c-.038.217-.121.44-.247.668a2.393 2.393 0 01-.492.63 2.115 2.115 0 01-1.696.551zm.087-.489a1.623 1.623 0 001.203-.385 1.865 1.865 0 00.605-1.078 1.8 1.8 0 00.006-.629 1.377 1.377 0 00-.213-.533 1.213 1.213 0 00-.422-.391 1.46 1.46 0 00-.61-.181 1.66 1.66 0 00-.661.068 1.704 1.704 0 00-.932.784 1.93 1.93 0 00-.215.611c-.037.21-.038.414-.003.613.036.194.107.374.212.538.105.16.244.292.416.395.175.104.38.167.614.188zm2.891.654l.706-3.969.715.064.717 2.753.088.008 1.653-2.542.721.064-.706 3.968-.539-.048.603-3.387.036.015-1.593 2.454-.66-.059-.69-2.657.046-.007-.602 3.387-.495-.044zm5.485.51l.083-.468 1.122.1c.217.019.394-.026.533-.135a.74.74 0 00.268-.473c.037-.207.007-.373-.09-.5-.096-.13-.254-.204-.474-.224l-1.122-.1.062-.348 1.045.093c.257.023.477.074.66.153a.794.794 0 01.407.351c.089.151.111.35.067.597l-.013.076a1.263 1.263 0 01-.249.58c-.123.151-.29.261-.5.331-.209.066-.46.086-.754.06l-1.045-.093zm-.429-.038l.713-4.013.539.048-.713 4.013-.539-.048zm.766-1.86l.062-.347.974.086c.227.02.405-.025.532-.135a.743.743 0 00.248-.453c.035-.192.007-.353-.081-.482-.085-.133-.241-.209-.468-.229l-.974-.087.083-.467.881.078c.44.039.751.161.935.365.184.2.245.472.184.817l-.013.076c-.044.243-.133.432-.269.567a1.018 1.018 0 01-.516.263c-.205.041-.437.05-.697.027l-.881-.079zm2.976 2.17l.71-3.99.539.048-.71 3.99-.539-.048zm2.481.221l-1.017-1.885.633.056 1.039 1.887-.655-.058zm-1.89-1.626l.084-.473 1.078.096a.915.915 0 00.453-.065.824.824 0 00.331-.258.938.938 0 00.173-.41.811.811 0 00-.025-.422.637.637 0 00-.229-.313.785.785 0 00-.416-.147l-1.078-.096.083-.467.979.087c.294.026.543.092.747.199a.943.943 0 01.44.447c.089.192.108.431.057.718l-.015.087c-.051.286-.155.514-.312.684-.153.171-.348.29-.583.357a2.125 2.125 0 01-.788.063l-.979-.087zm3.261 1.748l.705-3.969.539.048-.705 3.969-.539-.048zm.429.038l.085-.479 1.947.173-.085.479-1.947-.173zm.316-1.778l.085-.478 1.776.157-.085.479-1.776-.158zm.304-1.713l.085-.478 1.892.168-.085.479-1.892-.169zm3.071 3.819l2.152-3.84.913.081.785 4.101-.555-.049-.704-3.741.241.154-.649-.058.303-.105-1.947 3.505-.539-.048zm1.074-1.109l.267-.462 1.733.154.096.495-2.096-.187zm4.187.174l.083-.468.897.08a.974.974 0 00.492-.067.838.838 0 00.335-.279.997.997 0 00.174-.416.888.888 0 00-.022-.438.621.621 0 00-.228-.324.882.882 0 00-.454-.151l-.896-.079.083-.468.798.071c.33.029.595.106.797.231a.976.976 0 01.428.48c.08.199.098.427.052.684l-.016.087a1.4 1.4 0 01-.289.653 1.29 1.29 0 01-.584.396c-.238.082-.522.108-.852.079l-.798-.071zm-.674 1.343l.709-3.991.539.048-.709 3.99-.539-.047zm3.518.312l.705-3.968.539.047-.705 3.969-.539-.048zm.429.038l.085-.478 1.947.173-.085.479-1.947-.174zm.316-1.777l.085-.479 1.777.158-.086.479-1.776-.158zm.304-1.713l.085-.478 1.892.168-.085.478-1.892-.168zm2.249 3.745l.705-3.968.539.048-.705 3.968-.539-.048zm.429.039l.085-.479 1.837.163-.085.479-1.837-.163zm2.563.227l.705-3.968.539.048-.705 3.968-.539-.048zm.429.039l.085-.479 1.837.163-.085.479-1.837-.163zm2.573.228l.706-3.968.539.047-.705 3.969-.54-.048zm2.144.213l.087-.489 1.001.089c.242.021.464.002.664-.057.205-.063.386-.158.542-.284.16-.125.292-.277.394-.456a1.8 1.8 0 00.211-.589 1.67 1.67 0 00.004-.618 1.19 1.19 0 00-.223-.506 1.158 1.158 0 00-.428-.358 1.68 1.68 0 00-.624-.166l-1.001-.089.086-.489.947.084c.363.032.668.116.916.252.248.133.443.301.586.505.147.201.243.422.29.661a1.9 1.9 0 01.008.713l-.022.12a2.147 2.147 0 01-.769 1.279c-.21.173-.458.305-.747.398a2.555 2.555 0 01-.976.084l-.946-.084zm-.429-.038l.713-4.012.539.047-.713 4.013-.539-.048zm6.136.623c-.359-.032-.658-.123-.896-.273a1.66 1.66 0 01-.554-.558 2.047 2.047 0 01-.262-.697 2.048 2.048 0 010-.69l.021-.12a2.3 2.3 0 01.754-1.319c.208-.184.451-.324.729-.421a2.28 2.28 0 01.95-.098c.352.031.646.118.881.261.24.142.428.321.564.536.138.211.228.44.269.686.042.247.042.488 0 .724l-.021.12c-.039.217-.121.44-.247.668a2.47 2.47 0 01-.492.63c-.206.192-.45.339-.733.443a2.217 2.217 0 01-.963.108zm.087-.49c.235.021.453-.004.656-.074.206-.07.388-.173.547-.31a1.87 1.87 0 00.61-1.708 1.356 1.356 0 00-.212-.532 1.22 1.22 0 00-.422-.391 1.465 1.465 0 00-.611-.182 1.568 1.568 0 00-1.2.374 1.712 1.712 0 00-.393.479 1.924 1.924 0 00-.217 1.223c.036.195.107.374.211.538.106.16.245.292.416.396.175.104.38.166.615.187zM270.381 122.478l-.737-.662.093-.522.946-.512.172.446-.776.246-.04.228.665.374-.323.402zm-.462-2.272l-.274 1.008-.508.22-.859-.513.322-.396.551.513.224-.096.048-.775.496.039zm-1.819 1.716l1.011-.347.416.302-.087 1.025-.494-.049.224-.759-.183-.132-.714.4-.173-.44zm5.402.833l-.737-.662.093-.521.946-.513.172.446-.776.246-.041.228.666.374-.323.402zm-.462-2.272l-.274 1.009-.509.219-.858-.512.322-.397.551.513.224-.096.048-.774.496.038zm-1.819 1.716l1.011-.346.416.302-.087 1.025-.495-.05.225-.758-.183-.133-.714.401-.173-.441zm2.512 2.979l1.934-4.467.495.044-1.934 4.467-.495-.044zm4.78-1.977l-.737-.662.093-.522.946-.512.172.446-.775.246-.041.228.665.374-.323.402zm-.462-2.272l-.274 1.008-.508.22-.859-.513.322-.396.551.513.224-.096.048-.775.496.039zm-1.819 1.716l1.012-.347.415.302-.087 1.025-.494-.049.224-.759-.183-.132-.714.4-.173-.44zm5.402.833l-.737-.662.093-.522.946-.512.172.446-.776.246-.04.228.665.374-.323.402zm-.462-2.272l-.274 1.008-.509.22-.858-.512.322-.397.551.513.224-.096.048-.774.496.038zm-1.819 1.716l1.011-.346.416.302-.087 1.025-.494-.05.224-.758-.183-.133-.714.401-.173-.441z"
                  fill="#FFF2F0"
                />
                <Path
                  d="M322.184 131.016l-2.376-.211 3.101-8.949 2.376.212-3.101 8.948zM332.374 122.923a5.645 5.645 0 00-2.063-.576c-2.347-.208-4.219.881-4.544 2.648-.251 1.303.823 2.134 1.645 2.649.84.526 1.101.844 1.031 1.24-.118.607-.901.823-1.585.762-.949-.085-1.431-.274-2.144-.681l-.287-.173-.695 2.009c.513.3 1.5.614 2.564.718 2.494.222 4.334-.851 4.688-2.733.193-1.033-.301-1.882-1.555-2.651-.758-.489-1.217-.814-1.141-1.24.078-.385.569-.744 1.508-.66a3.854 3.854 0 011.708.505l.198.115.672-1.932zM334.529 128.846c.289-.504 1.401-2.457 1.401-2.457-.014.019.289-.514.467-.841l.027.797s.063 2.214.08 2.676l-1.975-.175zm3.976-5.603l-1.838-.164c-.567-.05-1.027.076-1.376.643l-5.01 8.014 2.493.222.741-1.318 3.051.271c.011.325.041 1.387.041 1.387l2.201.196-.303-9.251zM319.437 121.548l-3.428 5.985-.031-1.278c-.173-1.488-1.242-3.182-2.609-4.099l.72 8.131 2.513.223 5.348-8.739-2.513-.223z"
                  fill="#fff"
                />
                <Path
                  d="M314.949 121.149l-3.823-.34-.072.18c2.849 1.019 4.501 3.011 4.924 5.266l-.089-4.247c-.034-.592-.425-.794-.94-.859z"
                  fill="#fff"
                />
              </G>
            </G>
          </G>
        </G>
      </G>
      <Defs>
        <LinearGradient
          id="paint0_linear_9223_6832"
          x1={367}
          y1={5.19999}
          x2={-15.3161}
          y2={179.592}
          gradientUnits="userSpaceOnUse"
        >
          <Stop stopColor="#FF583F" />
          <Stop offset={1} stopColor="#FF0033" />
        </LinearGradient>
        <ClipPath id="clip0_9223_6832">
          <Rect x={16} y={16} width={328} height={144} rx={16} fill="#fff" />
        </ClipPath>
        <ClipPath id="clip1_9223_6832">
          <Rect
            width={150.933}
            height={96.6337}
            rx={6}
            transform="matrix(.99607 .08854 -.17503 .98456 212.914 34)"
            fill="#fff"
          />
        </ClipPath>
        <ClipPath id="clip2_9223_6832">
          <Rect
            width={150.933}
            height={96.6337}
            rx={5.52192}
            transform="matrix(.99607 .08854 -.17503 .98456 212.914 34)"
            fill="#fff"
          />
        </ClipPath>
        <ClipPath id="clip3_9223_6832">
          <Rect x={32} y={118} width={133} height={26} rx={4} fill="#fff" />
        </ClipPath>
      </Defs>
    </Svg>
  )
}

export default GetPrepaidCardBanner;
