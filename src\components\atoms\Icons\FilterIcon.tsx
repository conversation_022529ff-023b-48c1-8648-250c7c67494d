import React, { FC } from 'react';
import { Svg, Path } from 'react-native-svg';
import { IconProps } from './types';

const FilterIcon: FC<IconProps> = () => {
  return (
    <Svg width="28" height="17" viewBox="0 0 18 16" fill="none">
      <Path
        d="M4.14163 13.5C4.31379 13.0121 4.63308 12.5895 5.05546 12.2907C5.47784 11.9918 5.98253 11.8313 6.49996 11.8313C7.01739 11.8313 7.52208 11.9918 7.94446 12.2907C8.36684 12.5895 8.68612 13.0121 8.85829 13.5H17.3333V15.1667H8.85829C8.68612 15.6546 8.36684 16.0771 7.94446 16.376C7.52208 16.6749 7.01739 16.8354 6.49996 16.8354C5.98253 16.8354 5.47784 16.6749 5.05546 16.376C4.63308 16.0771 4.31379 15.6546 4.14163 15.1667H0.666626V13.5H4.14163ZM9.14163 7.66667C9.3138 7.17872 9.63308 6.75619 10.0555 6.45732C10.4778 6.15845 10.9825 5.99796 11.5 5.99796C12.0174 5.99796 12.5221 6.15845 12.9445 6.45732C13.3668 6.75619 13.6861 7.17872 13.8583 7.66667H17.3333V9.33333H13.8583C13.6861 9.82128 13.3668 10.2438 12.9445 10.5427C12.5221 10.8415 12.0174 11.002 11.5 11.002C10.9825 11.002 10.4778 10.8415 10.0555 10.5427C9.63308 10.2438 9.3138 9.82128 9.14163 9.33333H0.666626V7.66667H9.14163ZM4.14163 1.83333C4.31379 1.34539 4.63308 0.92286 5.05546 0.62399C5.47784 0.32512 5.98253 0.164623 6.49996 0.164623C7.01739 0.164623 7.52208 0.32512 7.94446 0.62399C8.36684 0.92286 8.68612 1.34539 8.85829 1.83333H17.3333V3.5H8.85829C8.68612 3.98794 8.36684 4.41047 7.94446 4.70934C7.52208 5.00821 7.01739 5.16871 6.49996 5.16871C5.98253 5.16871 5.47784 5.00821 5.05546 4.70934C4.63308 4.41047 4.31379 3.98794 4.14163 3.5H0.666626V1.83333H4.14163ZM6.49996 3.5C6.72097 3.5 6.93293 3.4122 7.08922 3.25592C7.2455 3.09964 7.33329 2.88768 7.33329 2.66667C7.33329 2.44565 7.2455 2.23369 7.08922 2.07741C6.93293 1.92113 6.72097 1.83333 6.49996 1.83333C6.27895 1.83333 6.06698 1.92113 5.9107 2.07741C5.75442 2.23369 5.66663 2.44565 5.66663 2.66667C5.66663 2.88768 5.75442 3.09964 5.9107 3.25592C6.06698 3.4122 6.27895 3.5 6.49996 3.5ZM11.5 9.33333C11.721 9.33333 11.9329 9.24553 12.0892 9.08925C12.2455 8.93297 12.3333 8.72101 12.3333 8.5C12.3333 8.27899 12.2455 8.06702 12.0892 7.91074C11.9329 7.75446 11.721 7.66667 11.5 7.66667C11.2789 7.66667 11.067 7.75446 10.9107 7.91074C10.7544 8.06702 10.6666 8.27899 10.6666 8.5C10.6666 8.72101 10.7544 8.93297 10.9107 9.08925C11.067 9.24553 11.2789 9.33333 11.5 9.33333ZM6.49996 15.1667C6.72097 15.1667 6.93293 15.0789 7.08922 14.9226C7.2455 14.7663 7.33329 14.5543 7.33329 14.3333C7.33329 14.1123 7.2455 13.9004 7.08922 13.7441C6.93293 13.5878 6.72097 13.5 6.49996 13.5C6.27895 13.5 6.06698 13.5878 5.9107 13.7441C5.75442 13.9004 5.66663 14.1123 5.66663 14.3333C5.66663 14.5543 5.75442 14.7663 5.9107 14.9226C6.06698 15.0789 6.27895 15.1667 6.49996 15.1667Z"
        fill="#191919"
      />
    </Svg>
  );
};

export default FilterIcon;
