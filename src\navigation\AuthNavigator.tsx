import React from 'react';
import TabBar from './TabBar';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import StackTransfer from './StackTransfer';
import StackCashIn from './StackCashIn';
import StackUser from './StackUser';
import { NotificationsScreen } from '../screens/NotificationsScreen';
import GoBack from '../components/organisms/Buttons/GoBackButton';
import { useNavigation } from '@react-navigation/native';
import StackQrPayment from './StackQrPayment';
import { WebViewScreen } from '../screens/WevView';
import { SecurityScreen } from '../screens/SecurityScreen';
import BlockingBiometricsScreen from '../screens/BlockingBiometricsScreen';
import { ReceiptScreen } from '../screens/Activity/ReceiptScreen';
import { ChangePassword } from '../screens/StackVerifyAccount/ChangePassword';
import { Success } from '../screens/StackVerifyAccount/Success';
import CancelAccount from '../screens/CancelAccount';
import { ProfileContactScreen } from '../screens/StackUser/ProfileContactScreen';
import useContactStore from '../context/UserZustand';
import StackLoan from './StackLoan';
import StackPaymentServices from './StackPaymentServices';
import StackCards from './StackCards';
import color from '../theme/pallets/pallet';
import StackInvestment from './stack-investment';
import StackCars from './stack-cars';

const Stack = createNativeStackNavigator<any>();

function ArrowBack() {
  const navigation = useNavigation();
  return (
    <GoBack onPress={() => navigation.goBack()} color={color.PRIMARY_700} />
  );
}

function ArrowBackWhite() {
  const navigation = useNavigation();
  const resetContactName = useContactStore(state => state.resetContactName);
  const setNameChanged = useContactStore(state => state.setNameChanged);
  const handleBack = () => {
    resetContactName();
    setNameChanged(true);
    navigation.goBack();
  };
  return <GoBack onPress={handleBack} text={true} color="#FFF" />;
}

const RootNavigator = () => {
  return (
    <>
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="TabBar" component={TabBar} />
        <Stack.Screen name="StackInvestment" component={StackInvestment} />
        <Stack.Screen
          name="StackCars"
          component={StackCars}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen name="StackTransfer" component={StackTransfer} />
        <Stack.Screen name="StackLoan" component={StackLoan} />
        <Stack.Screen name="StackCashIn" component={StackCashIn} />
        <Stack.Screen name="StackQrPayment" component={StackQrPayment} />
        <Stack.Screen name="StackUser" component={StackUser} />
        <Stack.Screen name="StackCards" component={StackCards} />
        <Stack.Screen
          name="StackPaymentServices"
          component={StackPaymentServices}
        />
        <Stack.Screen name="ChangePassword" component={ChangePassword} />

        <Stack.Screen name="Success" component={Success} />
        <Stack.Screen
          options={{
            headerShown: true,
            headerShadowVisible: false,
            headerBackVisible: false,
            headerTitle: '',
            headerLeft: ArrowBack,
            headerStyle: {
              backgroundColor: color.WHITE,
            },
          }}
          name="Notifications"
          component={NotificationsScreen}
        />
        <Stack.Screen
          options={{
            headerShown: true,
            headerShadowVisible: false,
            headerBackVisible: false,
            headerTitle: '',
            headerLeft: ArrowBack,
            headerStyle: {
              backgroundColor: color.WHITE,
            },
          }}
          name="ReceiptScreen"
          component={ReceiptScreen}
        />
        <Stack.Screen
          options={{
            headerShown: true,
            headerShadowVisible: false,
            headerBackVisible: false,
            headerTitle: '',
            headerLeft: ArrowBack,
            headerStyle: {
              backgroundColor: color.WHITE,
            },
          }}
          name="SecurityScreen"
          component={SecurityScreen}
        />
        <Stack.Group screenOptions={{ presentation: 'modal' }}>
          <Stack.Screen name="WebViewScreen" component={WebViewScreen} />

          <Stack.Screen
            options={{
              headerShown: false,
              gestureEnabled: false,
            }}
            name="BlockingBiometricsScreen"
            component={BlockingBiometricsScreen}
          />
          <Stack.Screen
            options={{
              headerShown: false,
              gestureEnabled: false,
            }}
            name="CancelAccountScreen"
            component={CancelAccount}
          />
          <Stack.Screen
            options={{
              headerShown: true,
              headerShadowVisible: false,
              headerBackVisible: false,
              headerTitle: 'Perfil de contacto',
              headerTitleAlign: 'center',
              headerTintColor: color.WHITE,
              headerLeft: ArrowBackWhite,
              headerTitleStyle: {
                fontFamily: 'Satoshi-Bold',
                fontSize: 14,
              },
              headerStyle: {
                backgroundColor: color.PRIMARY_500,
              },
            }}
            name="ProfileContactScreen"
            component={ProfileContactScreen}
          />
        </Stack.Group>
      </Stack.Navigator>
    </>
  );
};

export default RootNavigator;
