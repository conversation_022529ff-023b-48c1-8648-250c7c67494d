import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const CopyIcon: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M13.7502 0.833313L3.75016 0.833313C2.8335 0.833313 2.0835 1.58331 2.0835 2.49998L2.0835 14.1666H3.75016L3.75016 2.49998L13.7502 2.49998V0.833313ZM16.2502 4.16665L7.0835 4.16665C6.16683 4.16665 5.41683 4.91665 5.41683 5.83331L5.41683 17.5C5.41683 18.4166 6.16683 19.1666 7.0835 19.1666L16.2502 19.1666C17.1668 19.1666 17.9168 18.4166 17.9168 17.5L17.9168 5.83331C17.9168 4.91665 17.1668 4.16665 16.2502 4.16665ZM16.2502 17.5L7.0835 17.5L7.0835 5.83331L16.2502 5.83331L16.2502 17.5Z"
        fill={color}
      />
    </Svg>
  );
};

export default CopyIcon;
