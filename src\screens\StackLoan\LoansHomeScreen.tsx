import { FC, useState } from 'react';
import { View, StyleSheet, Text, useWindowDimensions } from 'react-native';
import { TabView, TabBar } from 'react-native-tab-view';
import LoanCard from '../../components/organisms/Cards/Loans';
import { RefreshControl, ScrollView } from 'react-native';
import { Button } from '../../components/atoms';
import { LoanType } from '../../types/StackLoan/LoanTypes';
import { useNavigation } from '@react-navigation/native';
import { useUserLoans } from '../../hooks/Loans/useUserLoans';
import { UserLoan } from '../../types/StackLoan/UserLoansResponse';
import color from '../../theme/pallets/pallet';

interface SceneContainerProps {
  refreshing: boolean;
  onRefresh: () => void;
  children: React.ReactNode;
}

const SceneContainer: React.FC<SceneContainerProps> = ({
  refreshing,
  onRefresh,
  children,
}) => {
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          colors={[color.WHITE]}
          onRefresh={onRefresh}
          progressBackgroundColor={color.PRIMARY_500}
        />
      }
    >
      <View style={styles.loanScene}>{children}</View>
    </ScrollView>
  );
};

interface LoansListProps {
  loading: boolean;
  fetchLoans: () => void;
  loans: UserLoan[] | undefined;
  navigateToLoanDetailedScreen: (loanId: string) => void;
  navigateToPayInstallmentConfirmation?: (installmentAmount: string) => void;
}

const CurrentLoans: FC<LoansListProps> = ({
  loading,
  fetchLoans,
  loans,
  navigateToLoanDetailedScreen,
  navigateToPayInstallmentConfirmation,
}) => {
  return (
    <SceneContainer refreshing={loading} onRefresh={fetchLoans}>
      {loans?.map(loan => (
        <LoanCard
          key={loan._id}
          loanType={loan.loanType as LoanType}
          totalInstallments={loan.totalInstallments}
          expired={loan.expired}
          duesPaid={loan.duesPaid}
          quoteAmount={loan.cuoteAmount}
          requestAmount={loan.requestAmount}
          currentInstallment={loan.nextPayment.index + 1}
          nextPaymentDate={loan.nextPayment.date}
          navigateToLoanDetailedScreen={() =>
            navigateToLoanDetailedScreen(loan.loanId)
          }
          installments={loan.installments}
          navigateToPayInstallmentConfirmation={
            navigateToPayInstallmentConfirmation
          }
        />
      ))}
    </SceneContainer>
  );
};

const FinishedLoans: FC<LoansListProps> = ({
  loading,
  fetchLoans,
  loans,
  navigateToLoanDetailedScreen,
}) => {
  return (
    <SceneContainer refreshing={loading} onRefresh={fetchLoans}>
      {loans?.map(loan => (
        <LoanCard
          key={loan._id}
          loanType={loan.loanType as LoanType}
          finished={!!loan.finishDate}
          finishDate={loan.finishDate}
          requestAmount={loan.requestAmount}
          navigateToLoanDetailedScreen={() =>
            navigateToLoanDetailedScreen(loan.loanId)
          }
        />
      ))}
    </SceneContainer>
  );
};

const CurrentLoansRoute = { key: 'currentLoans', title: 'Vigentes' };
const FinishedLoansRoute = { key: 'finishedLoans', title: 'Finalizados' };

export const LoansHomeScreen = () => {
  const layout = useWindowDimensions();
  const navigation: any = useNavigation();
  const [index, setIndex] = useState(0);
  const [routes] = useState([CurrentLoansRoute, FinishedLoansRoute]);
  const { getUserLoansFromApi, userLoans, loading } = useUserLoans();

  const navigateToLoanDetailedScreen = (loanId: string) => {
    navigation.navigate('StackLoan', {
      screen: 'LoanDetailedScreen',
      params: { loanId },
    });
  };

  const navigateToPayInstallmentConfirmation = (installmentAmount: string) => {
    navigation.navigate('StackLoan', {
      screen: 'PayInstallmentConfirmation',
      params: { installmentAmount },
    });
  };

  const renderScene = ({ route }: any) => {
    switch (route.key) {
      case CurrentLoansRoute.key:
        return (
          <CurrentLoans
            loans={userLoans?.current}
            loading={loading}
            fetchLoans={getUserLoansFromApi}
            navigateToLoanDetailedScreen={navigateToLoanDetailedScreen}
            navigateToPayInstallmentConfirmation={
              navigateToPayInstallmentConfirmation
            }
          />
        );
      case FinishedLoansRoute.key:
        return (
          <FinishedLoans
            loans={userLoans?.finished}
            loading={loading}
            fetchLoans={getUserLoansFromApi}
            navigateToLoanDetailedScreen={navigateToLoanDetailedScreen}
          />
        );
      default:
        return null;
    }
  };

  const renderTabBar = (props: any) => (
    <TabBar
      {...props}
      indicatorStyle={{ backgroundColor: '#FF0033' }}
      style={{
        backgroundColor: 'white',
        shadowColor: 'transparent',
        borderBottomColor: '#E1E3ED',
        borderBottomWidth: 1,
      }}
      renderLabel={({ route, focused }) => (
        <Text
          style={{
            color: focused ? '#FF0033' : 'black',
            fontWeight: focused ? '700' : '400',
            width: 80,
          }}
        >
          {route.title}
        </Text>
      )}
    />
  );

  const navigateToNewLoan = () => {
    navigation.navigate('StackLoan', {
      screen: 'NewLoanScreen',
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Préstamos</Text>
      <TabView
        navigationState={{ index, routes }}
        renderScene={renderScene}
        onIndexChange={setIndex}
        renderTabBar={renderTabBar}
        initialLayout={{ width: layout.width }}
      />
      <View>
        <Button text="Nuevo préstamo" onPress={navigateToNewLoan} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
    marginBottom: 16,
  },
  loanScene: {
    flex: 1,
    paddingTop: 20,
    width: '100%',
    gap: 12,
    paddingBottom: 25,
  },
});
