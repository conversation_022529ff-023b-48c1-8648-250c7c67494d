import React from 'react';
import { View, Text } from 'react-native';
import { styles } from './styles';
import WatchIcon from '../../../atoms/Icons/WatchIcon';

const HoldOnboarding = () => {
  return (
    <View style={styles.container}>
      <View>
        <WatchIcon />
      </View>
      <Text style={styles.headerText}>
        ¡Falta poco para activar tu billetera!
      </Text>
      <Text style={styles.descriptionText}>
        Estamos validando los datos que nos has proporcionado. En breve te
        avisaremos por correo electrónico sobre el estado.
      </Text>
    </View>
  );
};

export default HoldOnboarding;
