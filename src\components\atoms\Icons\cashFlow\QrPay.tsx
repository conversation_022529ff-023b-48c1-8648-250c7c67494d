import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from '../types';

const QrPay: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width="20" height="20" viewBox="0 0 20 20" fill="none">
      <Path
        d="M2.5 9.16667H9.16667V2.5H2.5V9.16667ZM4.16667 4.16667H7.5V7.5H4.16667V4.16667Z"
        fill="#FF583F"
      />
      <Path
        d="M2.5 17.5H9.16667V10.8333H2.5V17.5ZM4.16667 12.5H7.5V15.8333H4.16667V12.5Z"
        fill="#FF583F"
      />
      <Path
        d="M10.8333 2.5V9.16667H17.5V2.5H10.8333ZM15.8333 7.5H12.5V4.16667H15.8333V7.5Z"
        fill="#FF583F"
      />
      <Path d="M17.5 15.8333H15.8333V17.5H17.5V15.8333Z" fill="#FF583F" />
      <Path d="M12.5 10.8333H10.8333V12.5H12.5V10.8333Z" fill="#FF583F" />
      <Path d="M14.1667 12.5H12.5V14.1667H14.1667V12.5Z" fill="#FF583F" />
      <Path d="M12.5 14.1667H10.8333V15.8333H12.5V14.1667Z" fill="#FF583F" />
      <Path d="M14.1667 15.8333H12.5V17.5H14.1667V15.8333Z" fill="#FF583F" />
      <Path
        d="M15.8333 14.1667H14.1667V15.8333H15.8333V14.1667Z"
        fill="#FF583F"
      />
      <Path d="M15.8333 10.8333H14.1667V12.5H15.8333V10.8333Z" fill="#FF583F" />
      <Path d="M17.5 12.5H15.8333V14.1667H17.5V12.5Z" fill="#FF583F" />
    </Svg>
  );
};

export default QrPay;
