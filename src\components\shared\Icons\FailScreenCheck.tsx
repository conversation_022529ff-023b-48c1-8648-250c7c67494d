import React from 'react';
import Svg, { Circle, Path, Rect } from 'react-native-svg';

const FailScreenCheck = () => {
  return (
    <Svg width="164" height="127" viewBox="0 0 164 127" fill="none">
      <Circle cx="82.5" cy="63.5" r="63" fill="#E05555" />
      <Rect x="46.5" y="40.5" width="72" height="86" rx="8" fill="white" />
      <Path d="M82.5002 68.1433L90.7502 59.8933L93.1069 62.25L84.8569 70.5L93.1069 78.75L90.7502 81.1066L82.5002 72.8566L74.2502 81.1066L71.8936 78.75L80.1436 70.5L71.8936 62.25L74.2502 59.8933L82.5002 68.1433Z" fill="#E05555" />
      <Path d="M66.5 98.5H98.5" stroke="#BAC2C7" stroke-width="2" stroke-linecap="round" />
      <Path d="M63.5 107.5H101.5" stroke="#BAC2C7" stroke-width="2" stroke-linecap="round" />
      <Path d="M66.5 116.5H98.5" stroke="#BAC2C7" stroke-width="2" stroke-linecap="round" />
      <Circle cx="13.5" cy="8.5" r="8" fill="#FFD6D6" />
      <Circle cx="155.5" cy="100.5" r="8" fill="#FFD6D6" />
      <Circle cx="5" cy="100" r="4.5" fill="#FFD6D6" />
      <Circle cx="155" cy="13" r="4.5" fill="#FFD6D6" />
    </Svg>
  );
};

export default FailScreenCheck;
