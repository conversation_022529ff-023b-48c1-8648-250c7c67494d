import React, { useMemo, useState } from 'react';
import { View, TextInput } from 'react-native';
import TextBase from '../TextBase';
import { styles, getDynamicStyles } from './styles';

type InputWithLabelProps = {
  label: string;
  value: string;
  name?: string;
  disabled?: boolean;
  onChange?: (key: string, value: string) => void;
  onFocus?: () => void;
  onBlur?: (e: any) => void;
  placeholder?: string;
  maxLength?: number;
  numberOfLines?: number;
};

export const InputWithLabel = ({
  label = '',
  value,
  name,
  disabled,
  onChange,
  onFocus,
  onBlur,
  placeholder,
  maxLength,
  numberOfLines
}: InputWithLabelProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };
  const dynamicStyles = useMemo(
    () => getDynamicStyles(isFocused, disabled, numberOfLines),
    [isFocused, disabled, numberOfLines],
  );
  return (
    <View style={styles.container}>
      <TextBase size="s">{label}</TextBase>
      <TextInput
        style={[styles.input, dynamicStyles.input]}
        maxLength={maxLength}
        numberOfLines={numberOfLines}
        onFocus={() => {
          handleFocus();
          onFocus && onFocus();
        }}
        value={value}
        editable={!disabled}
        onChangeText={text => onChange && onChange(name!, text)}
        onBlur={e => {
          handleBlur();
          onBlur && onBlur(e);
        }}
        placeholder={placeholder}
        placeholderTextColor="#BAC2C7"
        multiline={numberOfLines !== undefined && numberOfLines > 0}
      />
    </View>
  );
};
