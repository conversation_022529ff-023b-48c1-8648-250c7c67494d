import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const WalletCardsIcon: FC<IconProps> = () => {
  return (
    <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <Rect width="24" height="24" rx="4" fill="#F0F6FF" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.2559 13.999C10.2559 13.7919 10.4238 13.624 10.6309 13.624H13.1309C13.338 13.624 13.5059 13.7919 13.5059 13.999C13.5059 14.2061 13.338 14.374 13.1309 14.374H10.6309C10.4238 14.374 10.2559 14.2061 10.2559 13.999Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.7286 7.46404C10.4081 7.60856 10.1075 7.96171 9.78921 8.69789C9.78448 8.70882 9.77924 8.71952 9.7735 8.72994C9.77304 8.7312 9.77249 8.73277 9.77181 8.7347L9.76576 8.75266C9.76217 8.76341 9.7581 8.774 9.75356 8.78438L9.72096 8.8589L9.70095 8.90891L9.11749 10.3024C9.22342 10.2903 9.33105 10.2841 9.44 10.2841H10.6183L11.8252 7.4831C11.3219 7.33587 10.9844 7.34865 10.7286 7.46404ZM10.4202 6.78036C11.0123 6.51328 11.6901 6.61278 12.464 6.90883C12.5593 6.94528 12.6356 7.01899 12.6754 7.11295C12.7151 7.20691 12.7149 7.31302 12.6747 7.40679L12.6597 7.44179L11.2094 10.8075C11.1501 10.945 11.0147 11.0341 10.865 11.0341H9.44C9.15178 11.0341 8.87643 11.0934 8.62631 11.1994C8.48591 11.2588 8.32346 11.2274 8.21541 11.1198C8.10735 11.0122 8.0752 10.8499 8.13409 10.7093L9.00793 8.62202L9.02682 8.5748C9.0283 8.5711 9.02984 8.56742 9.03144 8.56377L9.05943 8.4998C9.06796 8.47432 9.08382 8.42973 9.10794 8.38377C9.44313 7.6133 9.83851 7.04272 10.4202 6.78036Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.9853 7.11136C12.0621 6.93209 12.2636 6.84164 12.4486 6.90332C12.5104 6.92391 12.5957 6.96077 12.6485 6.98353C12.6668 6.99145 12.6812 6.99767 12.6893 7.0009L12.6955 7.00338L13.7996 7.46805C14.4305 7.72973 14.9299 8.0234 15.2496 8.41577C15.3061 8.48373 15.3596 8.55569 15.4081 8.64328C15.4589 8.72388 15.5069 8.81658 15.539 8.92316C15.5607 8.97523 15.5778 9.02697 15.5906 9.07563C15.7611 9.61951 15.6413 10.2331 15.3728 10.8993C15.2998 11.0804 15.1 11.1752 14.9136 11.1171C14.7312 11.0604 14.5297 11.0341 14.32 11.0341H10.865C10.7388 11.0341 10.6211 10.9706 10.5517 10.8652C10.4824 10.7598 10.4707 10.6266 10.5206 10.5107L11.9706 7.14568L11.9853 7.11136ZM12.5283 7.74678L11.4349 10.2841H14.32C14.4742 10.2841 14.631 10.295 14.7869 10.3199C14.9367 9.86254 14.9512 9.53717 14.873 9.29383C14.8705 9.28597 14.8682 9.27803 14.8662 9.27003C14.8613 9.25041 14.8533 9.22598 14.8423 9.20138C14.8343 9.18336 14.8277 9.16475 14.8227 9.1457C14.8158 9.12013 14.8013 9.08631 14.7696 9.03686C14.7643 9.02872 14.7594 9.02038 14.7548 9.01186C14.7356 8.97616 14.7121 8.94233 14.6719 8.89414L14.6689 8.89056C14.4686 8.64404 14.109 8.40829 13.5114 8.16048L13.5095 8.15972L12.5283 7.74678Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.63221 11.1995C7.8954 11.518 7.38086 12.2505 7.38086 13.0992V14.0742C7.38086 14.1739 7.38921 14.2772 7.40271 14.3807C7.40374 14.3886 7.40452 14.3965 7.40504 14.4045C7.45354 15.1388 7.67008 15.6518 8.01165 15.9934C8.35322 16.335 8.86622 16.5515 9.60057 16.6C9.60852 16.6005 9.61646 16.6013 9.62436 16.6023C9.72789 16.6158 9.83114 16.6242 9.93086 16.6242H13.8309C14.7009 16.6242 15.3038 16.4172 15.6981 16.0623C16.0891 15.7105 16.3323 15.1617 16.3764 14.3544C16.3814 14.2533 16.3859 14.163 16.3859 14.0742V13.0992C16.3859 12.1654 15.7649 11.3773 14.9228 11.1183C14.7386 11.0641 14.5351 11.0342 14.3259 11.0342H9.44586C9.15954 11.0342 8.88911 11.0927 8.63221 11.1995ZM8.34163 10.508C8.6841 10.3653 9.05296 10.2842 9.44586 10.2842H14.3259C14.6059 10.2842 14.8818 10.3241 15.1371 10.3996L15.1408 10.4006C16.2878 10.7522 17.1359 11.8237 17.1359 13.0992V14.0742C17.1359 14.1836 17.1305 14.2915 17.1256 14.3889L17.1254 14.3929L17.1253 14.3946C17.0743 15.332 16.7825 16.0955 16.1998 16.6198C15.6204 17.1412 14.8108 17.3742 13.8309 17.3742H9.93086C9.79498 17.3742 9.66241 17.3633 9.53929 17.3476C8.68897 17.2894 7.98615 17.0286 7.48132 16.5237C6.97649 16.0189 6.7156 15.3161 6.65747 14.4658C6.64179 14.3426 6.63086 14.2101 6.63086 14.0742V13.0992C6.63086 11.9386 7.33535 10.9417 8.33742 10.5098L8.34163 10.508Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.64554 8.52699C9.73311 8.6343 9.75444 8.78115 9.70095 8.90891L8.82591 10.9989C8.78841 11.0885 8.71762 11.16 8.62844 11.1984C7.89051 11.5165 7.375 12.2496 7.375 13.0991C7.375 13.3062 7.20711 13.4741 7 13.4741C6.79289 13.4741 6.625 13.3062 6.625 13.0991V11.6341C6.625 10.032 7.76462 8.69521 9.28251 8.39615C9.4184 8.36937 9.55798 8.41968 9.64554 8.52699ZM7.4236 11.1356C7.64469 10.9084 7.90367 10.7182 8.19014 10.5754L8.67907 9.40753C8.03905 9.76624 7.57044 10.3931 7.4236 11.1356Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.9308 8.78198C15.0463 8.66795 15.2221 8.64117 15.3663 8.7156C16.4183 9.25857 17.1343 10.3705 17.1343 11.6338V13.0988C17.1343 13.3059 16.9664 13.4738 16.7593 13.4738C16.5522 13.4738 16.3843 13.3059 16.3843 13.0988C16.3843 12.1677 15.7661 11.3764 14.9215 11.123C14.8207 11.0928 14.7373 11.0216 14.6915 10.9267C14.6458 10.8319 14.642 10.7223 14.6811 10.6246C14.9293 10.004 14.9694 9.59003 14.8795 9.28465C14.8772 9.27657 14.8751 9.26841 14.8732 9.26018C14.8686 9.23954 14.8628 9.22444 14.8589 9.21654C14.7863 9.07138 14.8153 8.896 14.9308 8.78198ZM16.3348 11.1328C16.2365 10.6412 15.9966 10.1991 15.6602 9.85477C15.6371 10.0758 15.5843 10.3058 15.5072 10.5453C15.8181 10.6897 16.0982 10.8898 16.3348 11.1328Z"
        fill="#0068FF"
      />
    </Svg>
  );
};

export default WalletCardsIcon;
