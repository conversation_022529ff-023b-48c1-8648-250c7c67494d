/* eslint-disable react/no-unstable-nested-components */
import React, { useEffect, useState } from 'react';
import {
  Keyboard,
  ScrollView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  FlatList,
  AppState,
} from 'react-native';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { TextBase } from '../../components/atoms';
import EditContact from '../../components/atoms/Icons/EditContact';
import DeleteIcon from '../../components/atoms/Icons/DeleteIcon';
import DeleteAccount from '../../components/atoms/Icons/DeleteAccount';
import BankGo from '../../components/atoms/Icons/BankGo';
import { useDeleteContact } from '../../hooks/User/deleteContact';
import { DeleteContactModal } from '../../components/organisms/Modals/DeleteContact';
import { useNavigation } from '@react-navigation/native';
import { SuccessModal } from '../../components/organisms/Modals/SuccessModal';
import { DeletecontactAccount } from '../../components/organisms/Modals/DeleteAccountContact';
import { useDeleteAccountContact } from '../../hooks/User/deleteAccountContact';
import { ModalEditContact } from '../../components/organisms/Modals/EditContact';
import useContactStore from '../../context/UserZustand';
import color from '../../theme/pallets/pallet';

type ItemProps = {
  label: string;
  value: string;
  icon?: JSX.Element;
  onDeletePress?: () => void;
  accountId?: string;
};

const Item = ({ label, value, onDeletePress, accountId }: ItemProps) => {
  return (
    <>
      {label && value && (
        <View style={styles.accountCard}>
          <View style={styles.leftContent}>
            <View style={styles.accountImage}>
              <BankGo />
            </View>
            <View style={styles.accountInfo}>
              <TextBase style={styles.accountTitle}>Toshify</TextBase>
              <TextBase style={styles.accountSubtitle}>{value}</TextBase>
            </View>
          </View>

          <View style={styles.iconDelete}>
            <TouchableOpacity onPress={() => onDeletePress(accountId)}>
              <View style={styles.icon} />
              <DeleteAccount />
            </TouchableOpacity>
          </View>
        </View>
      )}
    </>
  );
};

export const ProfileContactScreen = ({ route }: any) => {
  const { accountData } = route.params;
  const navigation: any = useNavigation();

  const [modalVisible, setModalVisible] = useState(false);
  const [loaderModalVisible, setLoaderModalVisible] = useState(false);
  const [nameChanged, setNameChanged] = useState('');
  // const [error, setError] = useState(false);
  const [keyboardOpen, setKeyboardOpen] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [modalSuccess, setModalSuccess] = useState(false);
  const [modalAccount, setModalAccount] = useState(false);
  const [modalEdit, setModalEdit] = useState(false);
  const [accountId, setAccountId] = useState();

  const newName = useContactStore(state => state.contactName);
  const setDeleteAccount = useContactStore(state => state.setDeleteAccount);
  const { deleteContact, error } = useDeleteContact();

  const { deleteAccountContact } = useDeleteAccountContact();

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardOpen(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardOpen(false);
      },
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);
  const accounts = Array.isArray(accountData) ? accountData : [accountData];

  useEffect(() => {
    const handleAppStateChange = (nextAppState: any) => {
      if (nextAppState === 'background') {
        setModalVisible(false);
        setModalSuccess(false);
        setModalAccount(false);
        setModalEdit(false);
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  const firstLetter = newName
    ? newName.charAt(0).toUpperCase()
    : accountData && accountData?.contactName.charAt(0).toUpperCase();
  const secondLetter = newName
    ? newName.split(' ')[1]?.charAt(0).toUpperCase()
    : (accountData &&
        accountData?.contactName.split(' ')[1]?.charAt(0).toUpperCase()) ||
      '';

  const ActionButton = ({ text, iconStyle, icon, onPress }: any) => (
    <TouchableOpacity style={styles.actionButton} onPress={onPress}>
      <View style={styles.iconContainer}>
        <View style={[styles.icon, iconStyle]} />
        {icon}
      </View>
      <TextBase style={styles.actionText}>{text}</TextBase>
    </TouchableOpacity>
  );

  const openDeleteAccountModal = (accountIdItem: any) => {
    setModalAccount(true);
    setAccountId(accountIdItem);
  };

  const DeleteAccountContact = async () => {
    setLoaderModalVisible(true);
    await deleteAccountContact({ contactId: accounts[0]._id, accountId });
    setDeleteAccount(true);
    setLoaderModalVisible(false);
    setModalSuccess(true);
  };

  const openDeleteModal = () => {
    setModalVisible(true);
  };

  const DeleteContact = async () => {
    setLoaderModalVisible(true);
    await deleteContact({ contactId: accounts[0]._id });
    setDeleteAccount(true);
    setLoaderModalVisible(false);
    setModalSuccess(true);
  };

  const openEditContact = () => {
    setModalEdit(true);
  };

  return (
    <KeyboardAvoidingComponent scrollEnabled={!showDropdown}>
      <ScrollView style={styles.scrollview}>
        <View style={styles.profileContainer}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {firstLetter}
                {secondLetter}
              </Text>
            </View>
          </View>
          <View style={styles.infoContainer}>
            <TextBase type="Bold" style={styles.name}>
              {newName ? newName : accountData && accountData.contactName}
            </TextBase>
            <TextBase type="Light">
              {accountData && accountData.contactDocument}
            </TextBase>
          </View>
          <View style={styles.actionsContainer}>
            <ActionButton
              text="Editar"
              icon={<EditContact />}
              onPress={openEditContact}
            />
            {/* <View style={styles.divider} />
            <ActionButton text="Favorito" icon={<FavoriteContact />} /> */}
            <View style={styles.divider} />
            <ActionButton
              text="Eliminar"
              icon={<DeleteIcon />}
              onPress={openDeleteModal}
            />
          </View>
        </View>
        <View style={styles.accountsContainer}>
          <TextBase type="Bold">Cuentas</TextBase>
          <FlatList
            data={accounts[0].accounts}
            renderItem={({ item, index }) => (
              <Item
                label={item?.name}
                value={item?.cbu}
                key={index}
                onDeletePress={() => openDeleteAccountModal(item?.accountId)}
                accountId={item?.accountId}
              />
            )}
            keyExtractor={(item, index) => index.toString()}
          />
        </View>
        {/* <LoaderModal modalVisible={loaderModalVisible} /> */}
        <SuccessModal
          modalVisible={modalSuccess}
          onPress={() => navigation.goBack()}
          error={error}
          setModalVisible={setModalSuccess}
        />
        <DeletecontactAccount
          modalView={modalAccount}
          setModalView={setModalAccount}
          deleteAccount={DeleteAccountContact}
          loaderModal={loaderModalVisible}
        />
        <DeleteContactModal
          modalView={modalVisible}
          setModalView={setModalVisible}
          deleteContact={DeleteContact}
          name={accountData.contactName}
          loaderModal={loaderModalVisible}
        />
        <ModalEditContact
          modalVisible={modalEdit}
          setModalVisible={setModalEdit}
          accountData={accountData}
          setNameChanged={setNameChanged}
        />
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
};

const styles = StyleSheet.create({
  scrollview: {
    backgroundColor: color.WHITE,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileContainer: {
    width: '100%',
    padding: 24,
    backgroundColor: '#F7F8FE',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flexDirection: 'column',
    gap: 16,
  },
  avatarContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  avatar: {
    width: 48,
    height: 48,
    backgroundColor: color.NEUTRALS_100,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: color.NEUTRALS_800,
    fontSize: 20,
    fontFamily: 'Sora',
    fontWeight: '400',
    lineHeight: 20,
  },
  infoContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 4,
    marginBottom: 16,
  },
  nameText: {
    color: '#191919',
    fontSize: 14,
    fontFamily: 'Sora',
    fontWeight: '600',
    lineHeight: 21,
  },
  idText: {
    textAlign: 'center',
    color: '#78838D',
    fontSize: 14,
    fontFamily: 'Sora',
    fontWeight: '400',
    lineHeight: 21,
  },
  actionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  actionButton: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  iconContainer: {
    width: 16,
    height: 16,
    position: 'relative',
    flexDirection: 'row-reverse',
  },
  icon: {
    position: 'absolute',
    backgroundColor: '#FF0033',
  },
  name: {
    textTransform: 'capitalize',
  },
  editIcon: {
    width: 12,
    height: 12,
    left: 2,
    top: 2,
  },
  favoriteIcon: {
    width: 13.33,
    height: 12.67,
    left: 1.33,
    top: 1.67,
  },
  deleteIcon: {
    width: 9.33,
    height: 12,
    left: 3.33,
    top: 2,
  },
  actionText: {
    textAlign: 'center',
    color: '#FF0033',
    fontSize: 12,
    fontFamily: 'Sora',
    fontWeight: '400',
    lineHeight: 18,
  },
  divider: {
    width: 1,
    height: 24,
    backgroundColor: '#E1E3ED',
  },
  accountsContainer: {
    width: '100%',
    padding: 24,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    gap: 12,
    backgroundColor: color.WHITE,
  },
  accountCard: {
    borderColor: color.NEUTRALS_100,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#EAEAEA',
    width: '91%',
  },
  accountImage: {
    width: 32,
    height: 32,
    borderRadius: 32,
    marginRight: 10,
  },
  accountInfo: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    marginRight: 40,
  },
  accountTitle: {
    alignSelf: 'stretch',
    color: color.NEUTRALS_800,
    fontSize: 12,
    fontFamily: 'Sora',
    fontWeight: '600',
    lineHeight: 18,
  },
  accountSubtitle: {
    alignSelf: 'stretch',
    color: color.NEUTRALS_800,
    fontSize: 12,
    fontFamily: 'Sora',
    fontWeight: '400',
    lineHeight: 18,
  },
  accountIcon: {
    width: 9.33,
    height: 12,
    left: 3.33,
    top: 2,
    backgroundColor: color.NEUTRALS_800,
  },
  iconDelete: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
});
