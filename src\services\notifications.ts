import { PermissionsAndroid, Platform } from 'react-native';
import { api } from './apiService';
import messaging from '@react-native-firebase/messaging';
import { toNumber } from 'lodash';
import { getData, setData } from './asyncStorageService';
import { FCM_TOKEN, USER_ID } from '../constants/asyncStorageKeys';
import DeviceInfo from 'react-native-device-info';
import { NotificationsResponse } from '../types/Notifications';

export const postFcmToken = async () => {
  try {
    const token = await getData(FCM_TOKEN);
    const userId = await getData(USER_ID);
    const deviceId = await DeviceInfo.getUniqueId();
    if (token && userId) {
      const response = await api.put(`/digital/users/${userId}/push-token`, {
        deviceId,
        token,
      });

      return response.data;
    }
  } catch (error) {
    console.error('🚀 ~ postFcmToken ~ error:', error);
  }
};

export async function requestUserPermission() {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (enabled) {
    getFcmToken();
  }
}

const getFcmToken = async () => {
  try {
    if (
      Platform.OS === 'android' &&
      toNumber(DeviceInfo.getSystemVersion()) >= 12
    ) {
      try {
        await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );
      } catch (error) { }
    }
    const fcmToken = await messaging().getToken();
    await setData(FCM_TOKEN, fcmToken);
  } catch (err) {
    console.error('Error al obtener el token de fcm: ', err);
  }
};

export const getNotificationList = async () => {
  try {
    const userId = await getData(USER_ID);
    const response = await api.get<NotificationsResponse>(
      `/notifications/${userId}`,
    );
    return response.data;
  } catch (error) {
    console.error('🚀 ~ getNotificationList ~ error:', error);
  }
};

export const deleteNotification = async () => {
  try {
    const userId = await getData(USER_ID);
    await api.delete(`/notifications/${userId}`);
  } catch (error) {
    console.error('🚀 ~ deleteNotification ~ error:', error);
  }
};

export const subscribeToTopic = async () => {
  const userId = await getData(USER_ID);

  try {
    await messaging().subscribeToTopic(userId?.toString()!);
  } catch (error) {
    console.error('🚀 ~ subscribeToTopic ~ error:', error);
  }
};

export const unsubscribeFromTopic = async () => {
  const userId = await getData(USER_ID);
  if (!userId) {
    return;
  }

  try {
    await messaging().unsubscribeFromTopic(userId?.toString()!);
  } catch (error) {
    console.error('🚀 ~ unsubscribeFromTopic ~ error:', error);
  }
};
