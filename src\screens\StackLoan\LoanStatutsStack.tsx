import { FC } from "react";
import { TextBase } from "../../components/atoms";

interface Props {
  finished: boolean;
  expiredInstallments?: number;
}

const LoanStatusStack: FC<Props> = ({ finished, expiredInstallments }) => {
  let text;
  let backgroundColor;
  let textColor;

  if (finished) {
    text = "Finalizado";
    backgroundColor = "#F4FFF8";
    textColor = "#289B4F";
  } else if (expiredInstallments && expiredInstallments > 0) {
    text = expiredInstallments > 1 ? "Cuotas vencidas" : "Cuota vencida"; 
    backgroundColor = "#FFF6F6";
    textColor = "#B83232";
  }

  return !finished && expiredInstallments && expiredInstallments === 0 ? <></> : (
  <TextBase 
    style={{ 
      backgroundColor: backgroundColor, 
      color: textColor, 
      borderRadius: 8,
      padding: 3
    }}
  >
    {text}
  </TextBase>);
};

export default LoanStatusStack;