import React, { FC, useCallback, useContext, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
  View,
} from 'react-native';
import { TransferItem } from '../../components/molecules';
import { api } from '../../services/apiService';
import { AuthContext } from '../../context/AuthContext';
import HeaderActivity from '../../components/organisms/Headers/Activity';
import { useFocusEffect } from '@react-navigation/native';

import { TextBase } from '../../components/atoms';
import { getFormattedDate } from '../../helpers/transfer';
import { Transactions, TransactionsResponse } from '../../types/Transfer';
import { FlashList } from '@shopify/flash-list';
import moment from 'moment';
import 'moment/locale/es';
import _ from 'lodash';
import color from '../../theme/pallets/pallet';

type GroupedTransfers = {
  date: string;
  transfers: Transactions[];
};

const ActivityScreen: FC<any> = () => {
  const { userData } = useContext(AuthContext);
  const [transfers, setTransfers] = useState<Transactions[]>([]);
  const [groupedTransfers, setGroupedTransfers] =
    useState<GroupedTransfers[]>();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [firstRender, setFirstRender] = useState(true);

  const loadTransfer = useCallback(
    async (page?: number, query?: string) => {
      try {
        setLoading(true);
        const response = await api.get<TransactionsResponse>(
          `/transactions?size=15&userId=${userData}`,
          {
            params: { page: page, searchValue: query },
          },
        );

        if (query) {
          setTransfers(response?.data?.data?.list);
          setCurrentPage(1);
          return;
        }

        setTransfers(transactions => [
          ...transactions,
          ...response?.data?.data?.list.filter(
            (transfer: Transactions) =>
              !transactions.some(t => t.id === transfer.id),
          ),
        ]);
        setTotalPages(response.data.data.totalPages);
      } catch (error) {
        console.error('Error fetching more data:', error);
      } finally {
        setLoading(false);
        setFirstRender(false);
      }
    },
    [userData],
  );

  useFocusEffect(
    useCallback(() => {
      setSearchQuery('');
      loadTransfer(1);
    }, [loadTransfer]),
  );

  const debouncedSearch = _.debounce(loadTransfer, 500);

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(1, text);
  };

  const handleLoadMore = () => {
    if (!loading && currentPage < totalPages) {
      setCurrentPage(prevPage => prevPage + 1);
      loadTransfer(currentPage + 1);
    }
  };

  function groupByDate(array: Transactions[]) {
    const transferByDate: { [key: string]: Transactions[] } = {};

    array.sort(
      (a, b) =>
        new Date(b.smImpactedOn).getTime() - new Date(a.smImpactedOn).getTime(),
    );

    array.forEach(transferencia => {
      const dateFormated = moment(transferencia.smImpactedOn)
        .locale('es')
        .format('MMMM D, YYYY');

      if (transferByDate[dateFormated]) {
        transferByDate[dateFormated].push(transferencia);
      } else {
        transferByDate[dateFormated] = [transferencia];
      }
    });

    const arrayBydate: GroupedTransfers[] = Object.keys(transferByDate).map(
      date => ({
        date,
        transfers: transferByDate[date],
      }),
    );

    return arrayBydate;
  }

  useEffect(() => {
    const transferByDate = groupByDate(transfers);
    setGroupedTransfers(transferByDate);
  }, [transfers]);

  const renderItem = ({ item }: { item: GroupedTransfers }) => {
    return (
      <View>
        <View style={styles.date}>
          <TextBase size="l" color={color.NEUTRALS_800}>
            {getFormattedDate(item.date)}
          </TextBase>
          {item.transfers.map((transfer: Transactions, i) => (
            <TransferItem key={transfer.id + i} transfer={transfer} />
          ))}
        </View>
        <View style={styles.separator} />
      </View>
    );
  };

  return (
    <View style={styles.containter}>
      <HeaderActivity
        handleSearchChange={handleSearchChange}
        searchQuery={searchQuery}
      />
      {firstRender ? (
        <View style={styles.loader}>
          <ActivityIndicator size="large" color={color.PRIMARY_500} />
        </View>
      ) : (
        <FlashList
          data={groupedTransfers}
          keyExtractor={(item, index) => item.date + index}
          renderItem={renderItem}
          estimatedItemSize={300}
          onEndReached={searchQuery.length !== 0 ? () => {} : handleLoadMore}
          onEndReachedThreshold={0.1}
          ListFooterComponent={
            loading ? (
              <View style={styles.loader}>
                <ActivityIndicator size="large" color={color.PRIMARY_500} />
              </View>
            ) : null
          }
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              colors={[color.WHITE]}
              progressBackgroundColor={color.PRIMARY_500}
              onRefresh={() => {
                setIsRefreshing(true);
                setSearchQuery('');
                loadTransfer(1).then(() => setIsRefreshing(false));
              }}
            />
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  containter: {
    flex: 1,
    backgroundColor: color.WHITE,
  },
  loader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  separator: {
    height: 8,
    backgroundColor: color.NEUTRALS_100,
  },
  date: {
    paddingHorizontal: 16,
    marginVertical: 24,
    gap: 12,
  },
});

export default ActivityScreen;
