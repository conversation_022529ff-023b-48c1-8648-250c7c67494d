export interface CreatePhyisicalCardRequest  {
  country?: string;
  address: Address;
  receiver: Receiver;
}

export interface CreateVirtualCardRequest {
  legalAddress: Address
}

interface Address {
  country?: string;
  streetName: string;
  streetNumber: string;
  floor?: string | null;
  apartment?: string | null;
  zipCode: number;
  neighborhood: string;
  city: string;
  region: string;
  additionalInfo?: string | null;
}

interface Receiver {
  fullName?: string;
  email?: string;
  documentType?: string;
  documentNumber?: string;
  taxIdentificationNumber?: string;
  telephoneNumber?: string;
}