import React, { FC } from 'react';
import { ActivityIndicator, Text, TouchableOpacity, View } from 'react-native';
import { styles } from './styles';

type Props = {
  text: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  outline?: boolean;
  icon?: JSX.Element;
};

const ButtonOutlineWhite: FC<Props> = ({
  text,
  onPress,
  disabled,
  loading,
  outline,
  icon,
}) => {
  const backgroundStyle = outline
    ? styles.outlineBackground
    : styles.background;

  return (
    <TouchableOpacity
      style={
        disabled || loading
          ? outline
            ? styles.outlineBackground
            : styles.disabledBackground
          : backgroundStyle
      }
      disabled={disabled || loading}
      onPress={onPress}
    >
      {loading && (
        <ActivityIndicator size="large" color="black" style={{ padding: 5 }} />
      )}

      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {text && (
          <Text style={disabled ? styles.disabledText : styles.text}>
            {text}
          </Text>
        )}

        {icon && icon}
      </View>
    </TouchableOpacity>
  );
};

export default ButtonOutlineWhite;
