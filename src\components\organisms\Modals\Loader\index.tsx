import React from 'react';
import { ActivityIndicator, Modal, View } from 'react-native';
import { styles } from './styles';
import { TextBase } from '../../../atoms';
import color from '../../../../theme/pallets/pallet';

type Props = {
  modalVisible: boolean;
  textVisible?: boolean;
  text?: string;
};

export const LoaderModal = ({ modalVisible, text, textVisible }: Props) => {
  return (
    <Modal animationType="slide" transparent={true} visible={modalVisible}>
      <View style={styles.modal}>
        <View style={styles.indicator}>
          <ActivityIndicator size="large" color={color.PRIMARY_700} />
          {textVisible && (
            <TextBase color={color.NEUTRALS_800} style={styles.text}>
              {text}
            </TextBase>
          )}
        </View>
      </View>
    </Modal>
  );
};
