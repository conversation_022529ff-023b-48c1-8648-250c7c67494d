import React from 'react';
import { Alert, Image, Linking, Modal, Pressable, View } from 'react-native';
import { TextBase } from '../../../atoms';
import { styles } from './styles';
import { Account } from '../../../../types/Account';
import ButtonOutline from '../../../atoms/ButtonOutline';
import LineWithText from '../../../../helpers/LineWhitText';
import LINKIcon from '../../../atoms/Icons/Criptos/LINKIcon';
import color from '../../../../theme/pallets/pallet';

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  accountData?: Account;
};

export const InfoCuitCuil = ({ modalVisible, setModalVisible }: Props) => {
  const openAnses = async () => {
    const url = 'https://www.anses.gob.ar/consultas/constancia-de-cuil';
    try {
      await Linking.openURL(url);
    } catch (error) {
      Alert.alert('Error', 'No se pudo abrir la página.');
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <View style={styles.overlay}>
        <View style={styles.modalView}>
          <View style={{ gap: 16 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <TextBase type="Bold" size="l">
                ¿Cómo averiguo mi CUIL o CUIT?
              </TextBase>
              <Pressable onPress={() => setModalVisible(false)}>
                <TextBase type="Bold" color={color.PRIMARY_700}>
                  Listo
                </TextBase>
              </Pressable>
            </View>

            <TextBase color={color.NEUTRALS_800}>
              Puedes encontrarlo en el dorso de algunos ejemplares del documento
              de identidad:
            </TextBase>
            <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
              <Image
                source={require('../../../../assets/ilustrations/DorsoDni/DorsoDni.png')}
              />
            </View>

            <LineWithText text={'o'} />

            <TextBase color={color.NEUTRALS_800}>
              Puedes visitar la web de Anses y consultarlo completando algunos
              datos:
            </TextBase>
          </View>
          <View style={styles.mt24}>
            <ButtonOutline
              onPress={openAnses}
              text="Ingresar a Anses"
              icon={<LINKIcon />}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};
