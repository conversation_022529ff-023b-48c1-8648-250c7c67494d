import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const BankGo: FC<IconProps> = () => {
  return (
    <Svg width="32" height="32" viewBox="0 0 32 32" fill="none">
      <Rect width="32" height="32" rx="16" fill="#EDEFF7" />
      <Path
        d="M11.8333 15.1665H10.1667V20.9998H11.8333V15.1665ZM16.8333 15.1665H15.1667V20.9998H16.8333V15.1665ZM23.9167 22.6665H8.08334V24.3332H23.9167V22.6665ZM21.8333 15.1665H20.1667V20.9998H21.8333V15.1665ZM16 9.54984L20.3417 11.8332H11.6583L16 9.54984ZM16 7.6665L8.08334 11.8332V13.4998H23.9167V11.8332L16 7.6665Z"
        fill="#535D66"
      />
    </Svg>
  );
};

export default BankGo;
