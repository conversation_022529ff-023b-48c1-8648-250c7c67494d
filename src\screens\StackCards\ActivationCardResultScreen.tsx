import React, { FC } from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '../../constants/colors';
import TextBase from '../../components/atoms/TextBase';
import { Button } from '../../components/atoms';
import SuccessScreenCheck from '../../components/shared/Icons/SuccessScreenCheck';
import FailScreenCheck from '../../components/shared/Icons/FailScreenCheck';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackCardsParams } from '../../types/StackCards/StackCardsParams';
import { useScreenLoaderContext } from '../../context/screen-loader-context';
import { getData } from '../../services/asyncStorageService';
import { api } from '../../services/apiService';
import { USER_ID } from '../../constants/asyncStorageKeys';
type Props = NativeStackScreenProps<
  StackCardsParams,
  'ActivationCardResultScreen'
>;
export const ActivationCardResultScreen: FC<Props> = ({
  navigation,
  route,
}) => {
  const { result } = route.params;
  const setGlobalLoading = useScreenLoaderContext();

  const error = result === 'error';
  const navigateToCardsScreen = () => {
    navigation.navigate('Cards');
  };

  const handleNavigateToRequestCardScreen = async () => {
    setGlobalLoading(true);
    try {
      const userId = await getData(USER_ID);
      const response = await api.post(`cards/activation-token/${userId}`);

      const end_user_token = response.data.data.access_token;

      const uri = `https://secure-data-web-stage.pomelo.la/v1/activate-card?auth=${end_user_token}&styles=https://my-style-page.com/styles.css&field_list=name,code,pan,expiration&layout=list`;

      navigation.replace('ActivationWebViewScreen', { link: uri });
    } catch (error: any) {
      console.error('Error creating physical card:', error.response);
      throw error;
    } finally {
      setGlobalLoading(false);
    }
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: error ? colors.backgroundError : colors.light },
      ]}
    >
      {!error && (
        <View style={styles.centeredView}>
          <SuccessScreenCheck />
          <TextBase type="Bold" size="l" style={styles.textCenter}>
            ¡Listo, ya activamos tu tarjeta!
          </TextBase>
        </View>
      )}
      {error && (
        <View style={styles.centeredView}>
          <FailScreenCheck />
          <TextBase type="Bold" size="l" style={styles.textCenter}>
            No pudimos activar tu tarjeta
          </TextBase>
          <TextBase style={styles.textCenter}>
            Intenta nuevamente o contacta a Atención al cliente para recibir más
            información.
          </TextBase>
        </View>
      )}
      <View style={styles.buttonsContainer}>
        <Button
          text={error ? 'Volver a intentar' : 'Volver al inicio'}
          onPress={
            error ? handleNavigateToRequestCardScreen : navigateToCardsScreen
          }
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 20,
  },
  buttonsContainer: {
    paddingVertical: 20,
  },
  textCenter: {
    textAlign: 'center',
  },
});
