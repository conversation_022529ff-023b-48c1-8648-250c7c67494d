import React, { useState, useEffect, useContext } from 'react';
import { View, Switch, StyleSheet, TouchableOpacity } from 'react-native';
import { TextBase } from '../components/atoms';
import { checkSensorAvailability, promptBiometrics } from '../lib/biometrics';
import { storage } from '../lib/mmkv';
import { isNil } from 'lodash';
import { DesactivateSecurityModal } from '../components/organisms/Modals/DisableSecurity';
import { AuthContext } from '../context/AuthContext';
import { DesactivateAccount } from '../components/organisms/Modals/DisableAccount';
import { LoaderModal } from '../components/organisms/Modals/Loader';
import { InfoModal } from '../components/organisms/Modals/InfoModal';
import { useDeleteAccount } from '../hooks/CancelAccount/CancelAccount';
import { useNavigation } from '@react-navigation/native';
import color from '../theme/pallets/pallet';

export const SecurityScreen = () => {
  const unlockMethodType = storage.getString('unlockMethodType');
  const [isEnabledSwitch, setIsEnabledSwitch] = useState(
    !isNil(unlockMethodType),
  );
  const [modalVisible, setModalVisible] = useState(false);
  const [modalView, setModalView] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);
  const [modalError, setModalError] = useState(false);
  const [errorCancel, setErrorCancel] = useState(false);

  const navigation: any = useNavigation();

  const {
    deleteAccount,
    loading,
    success: cancelSuccess,
  } = useDeleteAccount({ setErrorCancel, errorCancel });

  useEffect(() => {
    setIsEnabledSwitch(!isNil(unlockMethodType));
  }, [unlockMethodType]);

  const disabledSecurity = async () => {
    const { available, biometryType } = await checkSensorAvailability();
    if (!available) {
      return;
    }
    const success = await promptBiometrics(biometryType!);
    if (success) {
      storage.delete('unlockMethodType');
      setIsEnabledSwitch(false);
      setModalVisible(false);
    }
  };

  const toggleSwitch = async () => {
    const { available, biometryType } = await checkSensorAvailability();
    if (!available) {
      return;
    }

    if (isEnabledSwitch) {
      setModalVisible(true);
    } else {
      const success = await promptBiometrics(biometryType!);
      if (success) {
        storage.set('unlockMethodType', biometryType!);
        setIsEnabledSwitch(true);
      }
    }
  };
  const { isInBackground, userData } = useContext(AuthContext);
  useEffect(() => {
    isInBackground && setModalVisible(false);
  }, [isInBackground]);

  const handleOpenModal = () => {
    setModalView(true);
  };

  const desactivateAccount = () => {
    setModalLoading(true);
    setModalView(false);
    try {
      setModalLoading(true);
      deleteAccount({ userId: userData });
    } catch (error) {
      setModalError(true);
      console.error('Error:', error);
      setModalLoading(false);
    } finally {
      setModalLoading(false);
    }
  };
  if (cancelSuccess) {
    navigation.navigate('CancelAccountScreen');
  }

  return (
    <View style={styles.container}>
      <LoaderModal
        modalVisible={loading || modalLoading}
        textVisible={true}
        text="Aguarda, estamos desactivando tu cuenta..."
      />
      <InfoModal
        modalVisible={modalError || errorCancel}
        setErrorCancel={setErrorCancel}
      />
      <View style={styles.topSection}>
        <TextBase color={color.BLACK} size="xxl">
          Seguridad
        </TextBase>
        <View style={styles.biometria}>
          <View style={styles.txtBiometria}>
            <TextBase type="Bold">Iniciar sesión con biometría</TextBase>
            <TextBase size="s" color={color.NEUTRALS_800}>
              Utiliza tu huella digital para iniciar sesión en la app. Si
              pierdes o te roban el teléfono, nadie podrá acceder a tu cuenta.
            </TextBase>
          </View>
          <Switch
            trackColor={{ false: '#767577', true: color.PRIMARY_500 }}
            thumbColor={'#f4f3f4'}
            ios_backgroundColor={
              isEnabledSwitch ? color.PRIMARY_500 : '#3e3e3e'
            }
            onValueChange={toggleSwitch}
            value={isEnabledSwitch}
          />
        </View>
      </View>
      <View style={styles.bottomSection}>
        <TouchableOpacity onPress={handleOpenModal}>
          <TextBase color={color.PRIMARY_700} type="Bold">
            Cancelar cuenta
          </TextBase>
        </TouchableOpacity>
      </View>
      <DesactivateSecurityModal
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        DisabledSecurity={disabledSecurity}
      />
      <DesactivateAccount
        modalView={modalView}
        setModalView={setModalView}
        desactivateAccount={desactivateAccount}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.WHITE,
  },
  topSection: {
    padding: 16,
    alignItems: 'flex-start',
    gap: 16,
  },
  biometria: {
    flexDirection: 'row',
  },
  txtBiometria: {
    gap: 4,
    flex: 2,
  },
  bottomSection: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginBottom: 32,
  },
});
