import { StyleSheet } from 'react-native';
import color from '../../theme/pallets/pallet';

export const LoanDetailedScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    flexDirection: 'column',
  },
  containerRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.TEXT_PRIMARY,
    marginBottom: 16,
  },
  divider: {
    borderBottomColor: 'grey',
    borderBottomWidth: 0.3,
    marginVertical: 12,
  },
  loanType: {
    fontSize: 16,
    fontWeight: '700',
    color: color.TEXT_PRIMARY,
  },
  loanTypeContainer: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
    marginVertical: 20,
  },
  cuotaContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cuotaDivider: {
    borderBottomColor: 'grey',
    borderBottomWidth: 0.5,
    marginVertical: 10,
  },
  loadingIndicator: {
    height: 100,
    justifyContent: 'center',
  },
});
