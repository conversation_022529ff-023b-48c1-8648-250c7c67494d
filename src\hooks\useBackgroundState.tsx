import { useEffect, useRef, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { storage } from '../lib/mmkv';
import { isEmpty } from 'lodash';

export const useBackgroundState = (isAuthenticated: boolean) => {
  const isScreenLockDisabled = useRef(false);
  const [isInBackground, setIsInBackground] = useState(false);
  const appState = useRef(AppState.currentState);
  const navigation = useNavigation<StackNavigationProp<any, any>>();
  const backgroundTimestamp = useRef<number | null>(null);
  useEffect(() => {
    if (!isAuthenticated) {
      return;
    }

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      const unlockMethodType = storage.getString('unlockMethodType');
      if (isEmpty(unlockMethodType)) {
        return;
      }

      if (isScreenLockDisabled.current) {
        isScreenLockDisabled.current = false;
        return;
      }

      if (appState.current.match('active') && nextAppState === 'background') {
        backgroundTimestamp.current = Date.now();
        setIsInBackground(true);
      } else if (
        appState.current.match('background') &&
        nextAppState === 'active'
      ) {
        setIsInBackground(false);
        const currentTime = Date.now();
        if (
          backgroundTimestamp.current &&
          currentTime - backgroundTimestamp.current < 10000
        ) {
          backgroundTimestamp.current = null; // Reiniciar el timestamp
        } else {
          navigation.navigate('BlockingBiometricsScreen');
        }
      }

      appState.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => subscription.remove();
  }, [isAuthenticated, navigation]);

  const disableScreenLock = () => {
    isScreenLockDisabled.current = true;
  };

  const enableScreenLock = () => {
    isScreenLockDisabled.current = false;
  };

  return { isInBackground, disableScreenLock, enableScreenLock };
};
