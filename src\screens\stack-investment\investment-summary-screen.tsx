import React, { useState } from 'react';
import { View, StyleSheet, Pressable, ScrollView } from 'react-native';
import { Button, Text } from '../../components/atoms';
import color from '../../theme/pallets/pallet';
import CalendarIcon from '../../components/atoms/Icons/CalendarIcon';

export default function InvestmentSummaryScreen({ navigation }) {
  const [paymentMethod, setPaymentMethod] = useState<
    'Dinero' | 'Transferencia' | 'Efectivo' | null
  >(null);

  const handleNavigateToConfirm = () => {
    navigation.navigate('InvestmentConfirmScreen');
  };

  return (
    <ScrollView contentContainerStyle={styles.container}>
      <Text variant="B2">Resumen de la inversión</Text>

      <View style={styles.earningsContainer}>
        <View
          style={{
            backgroundColor: color.PRIMARY_700,
            borderTopLeftRadius: 16,
            borderTopRightRadius: 16,
            paddingHorizontal: 16,
            paddingVertical: 8,
          }}
        >
          <Text variant="B6" color="WHITE">
            Ganancia mensual estimada
          </Text>
          <Text variant="R7" color="WHITE">
            Cobras ARS 90.000 - 98.000 cada mes
          </Text>
        </View>
        <View style={styles.earningsDetails}>
          <View style={styles.detailItem}>
            <Text variant="R7">Inversión total</Text>
            <Text variant="B4" color="PRIMARY_500">
              4 tokens
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Text variant="R7">Rendimiento mensual</Text>
            <Text variant="B4" color="PRIMARY_500">
              4,2 - 4,9 %
            </Text>
          </View>
        </View>
        <View
          style={{
            flexDirection: 'row',
            gap: 8,
            marginHorizontal: 16,
            marginBottom: 16,
          }}
        >
          <View style={{ marginTop: 8 }}>
            <CalendarIcon size={16} color={color.NEUTRALS_400} />
          </View>
          <View style={{ paddingRight: 20 }}>
            <Text variant="B6">Ganancias mensuales:</Text>
            <Text variant="R7">
              Los pagos de las ganancias se hacen los días 5 de cada mes.
            </Text>
          </View>
        </View>
      </View>

      {/* Payment Method Section */}
      <Text variant="B6">¿Cómo quieres hacer el pago?</Text>
      <View style={styles.paymentMethods}>
        <Pressable
          style={[
            styles.paymentOption,
            paymentMethod === 'Dinero' && styles.paymentOptionSelected,
          ]}
          onPress={() => setPaymentMethod('Dinero')}
        >
          <Text
            variant="B5"
            color={paymentMethod === 'Dinero' ? 'PRIMARY_500' : 'BLACK'}
          >
            Dinero en cuenta
          </Text>
          <View
            style={{
              borderRadius: 16,
              padding: 6,
              backgroundColor: color.PRIMARY_500,
            }}
          >
            <Text variant="B7" color="NEUTRALS_50">
              Inmediata
            </Text>
          </View>
        </Pressable>

        <Pressable
          style={[
            styles.paymentOption,
            paymentMethod === 'Transferencia' && styles.paymentOptionSelected,
          ]}
          onPress={() => setPaymentMethod('Transferencia')}
        >
          <Text
            variant="B5"
            color={paymentMethod === 'Transferencia' ? 'PRIMARY_500' : 'BLACK'}
          >
            Transferencia
          </Text>
          <View
            style={{
              borderRadius: 16,
              padding: 6,
              backgroundColor: color.PRIMARY_500,
            }}
          >
            <Text variant="B7" color="NEUTRALS_50">
              Inmediata
            </Text>
          </View>
        </Pressable>

        <Pressable
          style={[
            styles.paymentOption,
            paymentMethod === 'Efectivo' && styles.paymentOptionSelected,
          ]}
          onPress={() => setPaymentMethod('Efectivo')}
        >
          <Text
            variant="B5"
            color={paymentMethod === 'Efectivo' ? 'PRIMARY_500' : 'BLACK'}
          >
            Efectivo
          </Text>
          <View
            style={{
              borderRadius: 16,
              padding: 6,
              backgroundColor: color.NEUTRALS_100,
            }}
          >
            <Text variant="B7" color="NEUTRALS_600">
              48hs hábiles
            </Text>
          </View>
        </Pressable>

        <Pressable
          style={[styles.paymentOption, styles.disabledOption]}
          disabled
        >
          <Text variant="B5">Mercado Pago</Text>
          <View
            style={{
              borderRadius: 16,
              paddingHorizontal: 6,
              backgroundColor: color.NEUTRALS_100,
            }}
          >
            <Text variant="B7" color="NEUTRALS_600">
              Próximamente
            </Text>
          </View>
        </Pressable>
      </View>

      <View
        style={{
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderWidth: 1,
          borderColor: color.NEUTRALS_200,
          padding: 16,
          marginHorizontal: -16,
          gap: 16,
        }}
      >
        <View>
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <Text variant="R5">Vas a invertir: </Text>
            <Text variant="B2" color="PRIMARY_500">
              $ 5.500.000
            </Text>
          </View>
        </View>
        <Button
          text="Continuar"
          onPress={handleNavigateToConfirm}
          disabled={!paymentMethod}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    gap: 24,
  },

  earningsContainer: {
    borderRadius: 16,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
  },
  earningsDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    margin: 16,
  },
  detailItem: {
    alignItems: 'center',
    borderLeftWidth: 2,
    borderColor: color.PRIMARY_500,
    paddingLeft: 8,
  },
  paymentMethods: {
    marginBottom: 20,
  },
  paymentOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#FFF',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#CCC',
    marginBottom: 10,
  },
  paymentOptionSelected: {
    borderColor: color.PRIMARY_500,
    borderWidth: 2,
  },
  disabledOption: {
    opacity: 0.5,
  },
  investmentContainer: {
    backgroundColor: '#FFF',
    padding: 20,
    borderRadius: 8,
    marginBottom: 20,
    alignItems: 'center',
  },
  investmentText: {
    fontSize: 14,
    color: '#000',
    marginBottom: 10,
  },
  investmentAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#E30000',
  },
  continueButton: {
    backgroundColor: '#E30000',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  continueText: {
    fontSize: 16,
    color: '#FFF',
    fontWeight: 'bold',
  },
});
