import React from 'react';
import { StatusBar, View } from 'react-native';
import { AuthProvider } from '../context/AuthContext';
import AxiosInterceptor from '../context/AxiosInterceptor';
import { UserProvider } from '../context/UserContext';
import { NavigationContainer } from '@react-navigation/native';
import StackNavigator from '../navigation/StackNavigator';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import color from '../theme/pallets/pallet';
import { ScreenLoaderProvider } from '../context/screen-loader-context';

export const Layout = () => {
  const insets = useSafeAreaInsets();
  return (
    <View
      style={{
        flex: 1,
        paddingTop: insets.top,
        paddingLeft: insets.left,
        paddingRight: insets.right,
      }}
    >
      <StatusBar backgroundColor={color.WHITE} barStyle="dark-content" />
      <NavigationContainer>
        <AppState />
      </NavigationContainer>
    </View>
  );
};

const AppState = () => {
  return (
    <ScreenLoaderProvider>
      <AuthProvider>
        <AxiosInterceptor>
          <UserProvider>
            <StackNavigator />
          </UserProvider>
        </AxiosInterceptor>
      </AuthProvider>
    </ScreenLoaderProvider>
  );
};
