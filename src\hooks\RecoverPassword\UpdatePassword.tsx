import { useState } from 'react';
import { api } from '../../services/apiService';

export const useUpdatePassword = ({ setLoading }: any) => {
  const [successData, setSuccessData] = useState<any>(null);
  const [error, setError] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [errorCorde, setErrorCorde] = useState(false);

  const updatePassword = async (body: any) => {
    try {
      setLoading(true);
      const response = await api.put('/users/password-update', body);
      console.log('response', JSON.stringify(response, null, 2));
      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
        setLoading(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
      }
    } catch (err: any) {
      console.error('Error:', err.response);
      if (err.response.data.message.toLowerCase().includes('code provided')) {
        setErrorCorde(true);
      }
      setError(true);
      setSuccess(false);
      setLoading(false);
    }
  };

  return {
    updatePassword,
    successData,
    error,
    success,
    errorCorde,
    setErrorCorde,
  };
};
