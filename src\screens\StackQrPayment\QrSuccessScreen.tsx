import React, { useContext } from 'react';
import { View, Text, StyleSheet, Pressable, ScrollView } from 'react-native';
import { Button } from '../../components/atoms';
import { colors } from '../../constants/colors';
import SuccessImage from '../../components/atoms/Icons/SuccessImage';
import FlagError from '../../components/atoms/Icons/FlagError';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { formatCurrency } from '../../helpers/formatCurrency';
import CopyIcon from '../../components/atoms/Icons/CopyIcon';
import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import { useUserContext } from '../../context/UserContext';
import { AuthContext } from '../../context/AuthContext';
import { useCopyCustomToast } from '../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';
import color from '../../theme/pallets/pallet';

type DetailProps = {
  title: string;
  value: string;
  icon?: JSX.Element;
};

const Detail = ({ title, value, icon }: DetailProps) => {
  const { showToast, toastConfig } = useCopyCustomToast();

  return (
    <View style={styles.containerDetail}>
      <Text
        style={{
          fontFamily: 'Satoshi-Regular',
          fontSize: 12,
          color: '#78838D',
        }}
      >
        {title}
      </Text>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Text
          style={{
            fontFamily: 'Satoshi-Bold',
            fontSize: 12,
            color: color.NEUTRALS_800,
          }}
        >
          {value}
        </Text>
        {icon && (
          <Pressable onPress={() => showToast(value.toString())}>
            {icon}
          </Pressable>
        )}
      </View>
      <Toast config={toastConfig} />
    </View>
  );
};
type Props = NativeStackScreenProps<any>;

export const QrSuccessScreen = ({ navigation, route }: Props) => {
  const { enableScreenLock } = useContext(AuthContext);
  enableScreenLock();
  const { amount, nombre, numTransaccion, cuit, cvu, trxSubject }: any =
    route.params;
  const { user } = useUserContext();
  const detalles = [
    {
      title: 'Empresa',
      value: nombre,
    },
    {
      title: 'Importe',
      value: '$' + formatCurrency(Number(amount)),
    },
    {
      title: 'Nº transacción',
      value: numTransaccion,
      icon: <CopyIcon size={24} color={color.PRIMARY_500} />,
    },
  ];

  const handleViewReceipt = () => {
    navigation.navigate('ReceiptScreen', {
      operationId: numTransaccion,
      // date: new Date(),
      amount: Number(amount),
      motive: trxSubject,
      from: user.nombre + ' ' + user.apellido,
      fromCuit: user.cuit,
      fromCVU: user.cvu[0].cvu,
      toName: nombre,
      toCuit: cuit,
      toCvu: cvu,
    });
  };

  useDisableBackButton();

  return (
    <ScrollView contentContainerStyle={styles.scrollViewContent}>
      <View style={styles.container}>
        <View>
          <View style={styles.itemsCenter}>
            <SuccessImage width={135} height={104} />
            <Text style={styles.title}>¡Pago exitoso!</Text>
            <Text style={styles.subTxt}>
              El pago se ha realizado correctamente.
            </Text>
          </View>
          <View style={[styles.gap8, styles.mt35]}>
            <Text style={styles.detalleTxt}>Detalle del pago</Text>

            {detalles.map(detalle => (
              <Detail
                title={detalle.title}
                value={detalle.value}
                key={detalle.value}
                icon={detalle.icon}
              />
            ))}
            <View style={styles.errorContainer}>
              <FlagError width={20} height={20} color={colors.negative} />
              <Text style={styles.error}>Reportar un problema</Text>
            </View>
          </View>
        </View>
        <View style={styles.gap8}>
          <ButtonOutline text="Ver comprobante" onPress={handleViewReceipt} />
          <Button
            text="Ver actividad"
            onPress={() => {
              navigation.navigate('Activity');
            }}
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollViewContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    padding: 26,
    justifyContent: 'space-between',
    marginTop: 27,
  },
  itemsCenter: {
    alignItems: 'center',
  },
  mt35: {
    marginTop: 55,
  },
  gap8: {
    gap: 8,
    mnarginTop: '10%',
  },
  error: {
    color: colors.negative,
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    marginLeft: 2,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  detalleTxt: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
  subTxt: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.NEUTRALS_800,
    width: '80%',
    textAlign: 'center',
  },
  title: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 18,
    color: color.TEXT_PRIMARY,
    marginTop: 16,
    marginBottom: 6,
  },
  containerDetail: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    borderRadius: 8,
    marginBottom: 15,
  },
});
