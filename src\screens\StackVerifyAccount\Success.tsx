import React, { useContext } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import SuccessImage from '../../components/atoms/Icons/SuccessImage';
import { Button } from '../../components/atoms';
import { colors } from '../../constants/colors';
import { AuthContext } from '../../context/AuthContext';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const Success = ({ navigation }: Props) => {
  const { signOut } = useContext(AuthContext);
  useDisableBackButton();

  return (
    <KeyboardAvoidingComponent>
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.contentContainer}>
          <View style={styles.itemsCenter}>
            <SuccessImage width={135} height={104} />
            <Text style={styles.title}>¡Contraseña actualizada!</Text>
            <Text style={styles.subTxt}>
              La actualización de la contraseña se ha realizado correctamente.
            </Text>
          </View>
        </View>
        <View style={styles.gap8}>
          <Button
            text="Iniciar sesión"
            onPress={() => {
              signOut();
              navigation.navigate('Login');
            }}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemsCenter: {
    alignItems: 'center',
  },
  gap8: {
    marginVertical: 16,
  },
  subTxt: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.NEUTRALS_800,
    textAlign: 'center',
  },
  title: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 18,
    color: color.TEXT_PRIMARY,
    marginTop: 16,
    marginBottom: 6,
  },
});
