import React from 'react';
import { StyleSheet, View } from 'react-native';
import { getLoanTypeTranslation } from '../../types/StackLoan/LoanTypes';
import LoanTypeIcon from '../../components/atoms/Icons/LoanTypeIcon';
import { formatCurrency } from '../../helpers/formatCurrency';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { Button, TextBase } from '../../components/atoms';
import BackgroundWrapper from '../../components/atoms/Icons/BackgroundWrapper';
import Thunder from '../../components/atoms/Icons/Thunder';
import Wallet from '../../components/atoms/Icons/Wallet';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const SimulateLoanIntroScreen = ({ navigation, route }: Props) => {
  const { loanTypeId, loanType, limitAmount } = route.params;

  const navigateToSimulateLoan = () => {
    navigation.navigate('StackLoan', {
      screen: 'SimulateLoanScreen',
      params: { loanTypeId, loanType, limitAmount },
    });
  };

  return (
    <View style={styles.container}>
      <TextBase style={styles.title}>
        Préstamo {getLoanTypeTranslation(loanType)}
      </TextBase>
      <View style={styles.cardsContainer}>
        <View style={styles.cardContainer}>
          <LoanTypeIcon loanType={loanType} />
          <TextBase color={color.NEUTRALS_800} type="Bold">
            ¡Tienes hasta ${formatCurrency(limitAmount)}!
          </TextBase>
          <TextBase color={color.NEUTRALS_800} style={styles.txtCenter}>
            Puedes devolverlo hasta en 12 cuotas fijas.
          </TextBase>
        </View>

        <View style={styles.cardContainer}>
          <BackgroundWrapper>
            <Thunder size={20} iconColor={color.PRIMARY_500} />
          </BackgroundWrapper>
          <TextBase color={color.NEUTRALS_800} type="Bold">
            ¡Recibelo al instante!
          </TextBase>
          <TextBase color={color.NEUTRALS_800} style={styles.txtCenter}>
            Obten tu préstamo de manera facíl y sencilla en el momento.
          </TextBase>
        </View>

        <View style={styles.cardContainer}>
          <BackgroundWrapper>
            <Wallet size={20} iconColor={color.PRIMARY_500} />
          </BackgroundWrapper>
          <TextBase color={color.NEUTRALS_800} type="Bold">
            En tu cuenta de Toshify
          </TextBase>
          <TextBase color={color.NEUTRALS_800} style={styles.txtCenter}>
            Una vez que tengas el dinero, tú eliges como usarlo.
          </TextBase>
        </View>
      </View>
      <Button text="Simular" onPress={navigateToSimulateLoan} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
    marginBottom: 16,
  },
  cardContainer: {
    borderColor: color.NEUTRALS_100,
    borderStyle: 'solid',
    width: '100%',
    borderWidth: 1,
    borderRadius: 8,
    flexDirection: 'column',
    padding: 14,
    gap: 3,
    alignItems: 'center',
    textAlign: 'center',
  },
  cardsContainer: {
    flex: 1,
    paddingTop: 20,
    width: '100%',
    gap: 12,
    paddingBottom: 25,
  },
  txtCenter: {
    textAlign: 'center',
  },
});
