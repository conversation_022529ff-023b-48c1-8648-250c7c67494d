import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from '../../components/atoms';
import { colors } from '../../constants/colors';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import ErrorImage from '../../components/atoms/Icons/ErrorImage';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import { CommonActions } from '@react-navigation/native';
import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import color from '../../theme/pallets/pallet';
type Props = NativeStackScreenProps<any>;

export const QrErrorScreen = ({ navigation }: Props) => {
  const handleTryAgain = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: 'QrScreen' }],
      }),
    );
  };
  useDisableBackButton();
  return (
    <>
      <View style={styles.errorContainer}>
        <View style={styles.container}>
          <View style={styles.itemsCenter}>
            <ErrorImage width={135} height={104} />
            <Text style={styles.title}>Pago fallido :(</Text>
            <Text style={styles.subTxt}>
              Su pago ha sido rechazada debido a un problema técnico
            </Text>
          </View>
        </View>
      </View>
      <View style={styles.gap8}>
        <Button text="Volver a intentar" onPress={handleTryAgain} />
        <ButtonOutline
          text="Ver actividad"
          onPress={() => {
            navigation.navigate('Activity');
          }}
          outline={true}
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  subTxt: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.NEUTRALS_800,
    textAlign: 'center',
  },
  title: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 18,
    color: color.TEXT_PRIMARY,
    marginTop: 16,
    marginBottom: 6,
  },
  errorContainer: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFF6F6',
  },
  container: {
    marginTop: 'auto',
    marginBottom: 'auto',
    paddingHorizontal: 54,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemsCenter: {
    alignItems: 'center',
  },
  gap8: {
    gap: 8,
    marginHorizontal: 20,
    marginBottom: 30,
  },
});
