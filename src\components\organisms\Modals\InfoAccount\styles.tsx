import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  modalView: {
    backgroundColor: 'white',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    padding: 16,
    marginTop: 'auto',
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
  },
  containerAccount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  account: {
    flex: 1,
    marginHorizontal: 12,
    marginVertical: 6,
  },
  name: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 16,
    color: color.TEXT_PRIMARY,
  },
  data: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 16,
    color: '#78838D',
  },
  close: {
    alignSelf: 'flex-start',
    marginLeft: 'auto',
  },
  textClose: {
    color: color.PRIMARY_700,
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
  },
  mt24: {
    marginVertical: 24,
  },
  containerItem: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    paddingHorizontal: 12,
    paddingVertical: 16,
    marginBottom: 8,
  },
  label: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 12,
    color: '#78838D',
  },
  value: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 12,
    color: color.NEUTRALS_800,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});
