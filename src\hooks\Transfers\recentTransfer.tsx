import { AuthContext } from '../../context/AuthContext';
import { api } from '../../services/apiService';
import { useContext, useState } from 'react';

export const useRecentTransfer = () => {
  const [successData, setSuccessData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  const { userData } = useContext(AuthContext);

  const recentTransfer = async () => {
    try {
      setLoading(true);
      const response = await api.get(
        `/digital/contacts/list/${userData}?page=1&size=10&only=latest`,
      );
      if (response.status === 200) {
        setSuccessData(response.data.data);
        setSuccess(true);
        setLoading(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
      }
      setLoading(false);
    } catch (err: any) {
      console.error('Error:', err.response);
      setError(true);
      setErrorMessage(err.response.data.message);
      setLoading(false);
    }
  };

  return { recentTransfer, successData, errorMessage, loading, success, error };
};

export const useFavoriteContact = () => {
  const [successData, setSuccessData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  const { userData } = useContext(AuthContext);

  const GetFavorites = async () => {
    try {
      setLoading(true);
      const response = await api.get(
        `/contacts/list/${userData}?page=1&size=10&only=favorites`,
      );
      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
        setLoading(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
      }
      setLoading(false);
    } catch (err: any) {
      console.error('Error:', err.response);
      setError(true);
      setErrorMessage(err.response.data.message);
      setLoading(false);
    }
  };

  return { GetFavorites, successData, errorMessage, loading, success, error };
};
