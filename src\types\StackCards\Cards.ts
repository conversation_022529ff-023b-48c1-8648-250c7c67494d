interface ProductTypes {
  [key: string]: string;
}

const productTypes: ProductTypes = {
  PREPAID: "PREPAGA",
  CREDIT: "CREDIT"
};

export interface Card {
  id: string;
  affinity_group_id: string;
  card_type: string;
  status: string;
  user_id: string;
  start_date: Date;
  affinity_group_name: string;
  last_four: string;
  provider: string;
  product_type: string;
}

interface ShippingCard { 
  status: string
} 

export interface Cards {
  user_id: string;
  card_list: Card[];
  shipping: ShippingCard[];
}

export const CardProviders = {
  MASTERCARD: "MASTERCARD",
  VISA: "VISA"
}

export const getProductTypeTranslate = (type: string) => productTypes[type] || "";