import React, { FC, ReactNode, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { colors } from '../../constants/colors';
import TextBase from '../../components/atoms/TextBase';
import { Button } from '../../components/atoms';
import PrepaidCardFloating from '../../components/atoms/Icons/Cards/PrepaidCardFloating';
import CheckBox from '@react-native-community/checkbox';
import MiniCardIcon from '../../components/atoms/Icons/Cards/MiniCardIcon';
import MiniCardPlusIcon from '../../components/atoms/Icons/Cards/MiniCardPlusIcon';
import color from '../../theme/pallets/pallet';

const RequestCardScreen: FC<any> = ({ navigation }) => {
  const virtualCardSelected = true;
  const [physicalCardSelected, setPhysicalCardSelected] = useState(false);

  const navigateToRequestCardScreen = () => {
    navigation.navigate('StackCards', {
      screen: 'RequestCardFormScreen',
      params: {
        virtualCardSelected,
        physicalCardSelected,
      },
    });
  };

  const CheckButton: FC<{
    title: string;
    subtitle: string;
    icon: ReactNode;
    check: boolean;
    onChange: () => void;
    checkColor: string;
    agreggateTitle?: ReactNode;
  }> = ({
    title,
    subtitle,
    icon,
    check,
    onChange,
    checkColor,
    agreggateTitle,
  }) => {
      return (
        <TouchableOpacity
          style={[
            {
              borderColor: check ? color.PRIMARY_700 : '#E1E3ED',
              backgroundColor: check ? colors.backgroundLightPrimary : '#F7F8FE',
            },
            styles.checkButton,
          ]}
          onPress={onChange}
        >
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 12 }}>
            {icon}
            <View style={{ flexDirection: 'column', gap: 4 }}>
              <TextBase type="Bold" size="l">
                {title}
              </TextBase>
              {agreggateTitle}
              <TextBase size="s" color="#00000099">
                {subtitle}
              </TextBase>
            </View>
          </View>
          <CheckBox
            value={check}
            tintColors={{ true: checkColor, false: color.NEUTRALS_800 }}
            onFillColor={checkColor}
            tintColor="#535D66"
            onChange={onChange}
          />
        </TouchableOpacity>
      );
    };

  const PriceDisplay: FC = () => {
    return (
      <View>
        <TextBase size="l">
          <TextBase size="l" style={styles.strike}>
            $3.998
          </TextBase>{' '}
          <TextBase size="l" type="Bold">
            $ 1.999
          </TextBase>{' '}
          por única vez
        </TextBase>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View>
        <TextBase style={styles.title}>Tarjeta Prepaga</TextBase>
      </View>

      <View style={{ flex: 1, marginTop: 40, gap: 20 }}>
        <View style={{ alignItems: 'center' }}>
          <PrepaidCardFloating />
        </View>
        <TextBase style={styles.text}>
          Elige la Tarjeta Virtual sin cargo y también, podes recibirla en tu
          domicilio.
        </TextBase>

        <View style={{ gap: 14 }}>
          <CheckButton
            title="Tarjeta Virtual"
            subtitle="Sin costo de emisión"
            icon={<MiniCardIcon size={24} color={color.PRIMARY_700} />}
            check={virtualCardSelected}
            onChange={() => { }}
            checkColor={color.PRIMARY_700}
          />
          <CheckButton
            title="Tarjeta Física"
            subtitle="Costo de emisión y envío"
            agreggateTitle={<PriceDisplay />}
            icon={<MiniCardPlusIcon size={24} color={color.PRIMARY_700} />}
            check={physicalCardSelected}
            onChange={() => setPhysicalCardSelected(!physicalCardSelected)}
            checkColor={color.PRIMARY_700}
          />
        </View>
        <TextBase style={styles.text}>
          Aprovecha ahora y suma la tarjeta física con un descuento promocional.
        </TextBase>
      </View>

      <View style={styles.buttonsContainer}>
        <Button
          onPress={navigateToRequestCardScreen}
          text="Continuar"
          disabled={!virtualCardSelected && !physicalCardSelected}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    height: '100%',
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
  },
  buttonsContainer: {
    paddingVertical: 20,
    alignItems: 'center',
    display: 'flex',
  },
  noCardsBannerContainer: {
    borderColor: '#E1E3ED',
    borderRadius: 8,
    borderWidth: 1,
    backgroundColor: '#F7F8FE',
    alignItems: 'center',
    gap: 6,
    padding: 16,
  },
  strike: {
    textDecorationLine: 'line-through',
    color: 'gray',
  },
  text: {
    lineHeight: 20,
    letterSpacing: 0.17,
  },
  checkButton: {
    borderWidth: 1,
    borderRadius: 4,
    padding: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});

export default RequestCardScreen;
