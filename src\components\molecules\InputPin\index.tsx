import React from 'react';
import {
  NativeSyntheticEvent,
  Pressable,
  Text,
  TextInput,
  TextInputKeyPressEventData,
  View,
} from 'react-native';
import { styles } from '../InputAmount/styles';
import color from '../../../theme/pallets/pallet';
type Props = {
  value: number;
  handleKeyPress?: (
    e: NativeSyntheticEvent<TextInputKeyPressEventData>,
  ) => void;
  inputRef: React.RefObject<TextInput>;
  handleOnPress: () => void;
  error?: boolean;
  editable?: boolean;
  onChange: (value: string) => void;
  maxLength?: number;
};

export const InputPin = ({
  value,
  handleOnPress,
  handleKeyPress,
  inputRef,
  error,
  editable,
  onChange,
}: Props) => {
  return (
    <View
      style={[
        styles.mainContainer,
        { borderBottomColor: error ? color.RED_700 : color.PRIMARY_700 },
      ]}
    >
      <Pressable onPress={handleOnPress} style={{ flex: 1, minHeight: 64 }}>
        <View style={styles.container}>
          <View style={{ zIndex: 1 }}>
            <Text
              style={[
                styles.textInput,
                {
                  color: value
                    ? error
                      ? color.RED_700
                      : color.TEXT_PRIMARY
                    : color.NEUTRALS_400,
                },
              ]}
            >
              {Number(value) || 'XXXXXX'}
            </Text>
          </View>
          <TextInput
            ref={inputRef}
            style={styles.input}
            onKeyPress={handleKeyPress}
            keyboardType="numeric"
            editable={editable}
            onChangeText={onChange}
            maxLength={6}
          />
        </View>
      </Pressable>
    </View>
  );
};
