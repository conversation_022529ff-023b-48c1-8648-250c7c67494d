package com.toshify;

import com.facebook.react.modules.network.OkHttpClientFactory;
import com.facebook.react.modules.network.OkHttpClientProvider;
import okhttp3.CertificatePinner;
import okhttp3.OkHttpClient;

import java.net.URI;
import java.net.URISyntaxException;

public class SSLPinningFactory implements OkHttpClientFactory {

   public OkHttpClient createNewNetworkModuleClient() {
      String host;
      try {
         URI uri = new URI(BuildConfig.API_URL);
         host = uri.getHost();
      } catch (URISyntaxException e) {
         throw new RuntimeException("Invalid API_URL in BuildConfig: " + BuildConfig.API_URL, e);
      }

      if (host == null || host.isEmpty()) {
         throw new RuntimeException("Host is missing from API_URL: " + BuildConfig.API_URL);
      }

      CertificatePinner certificatePinner = new CertificatePinner.Builder()
        .add(host, "sha256/K73EE8c6WA5du0IvbiLS+aV02nyapK3cMSbg73kd8Pw=")
        .build();

      OkHttpClient.Builder clientBuilder = OkHttpClientProvider.createClientBuilder();
      return clientBuilder.certificatePinner(certificatePinner).build();
  }
}
