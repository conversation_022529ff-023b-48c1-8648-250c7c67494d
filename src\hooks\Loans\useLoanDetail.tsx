import { useEffect, useState } from 'react';
import { getLoanDetailed } from '../../services/loanServices';
import { UserLoan } from '../../types/StackLoan/UserLoansResponse';

export const useLoanDetail = (loanId: string) => {
  const [loan, setLoan] = useState<UserLoan | undefined>();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  const getLoanDetailedFromApi = async () => {
    setLoading(true);
    try {
      const loansResponse = await getLoanDetailed(loanId);
      setLoan(loansResponse);

    } catch (err: any) {
      setError(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getLoanDetailedFromApi();
  }, []);

  return { getLoanDetailedFromApi, loan, loading, error };
};
