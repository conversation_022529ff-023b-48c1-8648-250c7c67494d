import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const StarIcon: FC<IconProps> = () => {
  return (
    <Svg width="18" height="16" viewBox="0 0 18 16" fill="none">
      <Path
        d="M17.3333 6.11658L11.3417 5.59992L8.99999 0.083252L6.65832 5.60825L0.666656 6.11658L5.21666 10.0583L3.84999 15.9166L8.99999 12.8083L14.15 15.9166L12.7917 10.0583L17.3333 6.11658ZM8.99999 11.2499L5.86666 13.1416L6.69999 9.57492L3.93332 7.17492L7.58332 6.85825L8.99999 3.49992L10.425 6.86659L14.075 7.18325L11.3083 9.58325L12.1417 13.1499L8.99999 11.2499Z"
        fill="#535D66"
      />
    </Svg>
  );
};

export default StarIcon;
