import React, { <PERSON> } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const CheckWithBorderIcon: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 25 24" fill="none">
      <Path
        d="M12.5 22C18 22 22.5 17.5 22.5 12C22.5 6.5 18 2 12.5 2C7 2 2.5 6.5 2.5 12C2.5 17.5 7 22 12.5 22Z"
        stroke={color ? color : '#289B4F'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <Path
        d="M8.25 12L11.08 14.83L16.75 9.17"
        stroke={color ? color : '#289B4F'}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
};

export default CheckWithBorderIcon;
