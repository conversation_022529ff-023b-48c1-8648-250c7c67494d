import {
  AllInvestmentReponse,
  MyInvestmentReponse,
  InvestmentDetailReponse,
} from '../types/Investments';
import { FaqResponse } from '../types/Faq';
import apiToshi from './toshiApiService';

export const getAllInvestments = async () => {
  try {
    const { data }: { data: AllInvestmentReponse } = await apiToshi.get(
      '/Investment/all',
    );
    return data;
  } catch (error) {
    console.error('Error fetching AllInvestments:', error);
  }
};

export const getInvestmentDetail = async (id: string) => {
  try {
    const { data }: { data: InvestmentDetailReponse } = await apiToshi.get(
      `/Investment/${id}`,
    );
    return data;
  } catch (error) {
    console.error('Error fetching InvestmentDetail:', error);
  }
};

export const getFaq = async () => {
  try {
    const { data }: { data: FaqResponse } = await apiToshi.get('/Faq');
    return data;
  } catch (error) {
    console.error('Error fetching Faq:', error);
  }
};

export const getMyInvestments = async () => {
  try {
    try {
      const { data }: { data: MyInvestmentReponse } = await apiToshi.get(
        '/Investment/my-investments',
      );
      return data;
    } catch (error) {
      console.error(error);
    }
  } catch (error) {
    console.error('Error fetching MyInvestments:', error);
  }
};
