import React, { useEffect, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { styles } from './styles';
import { useNavigation } from '@react-navigation/native';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { Button } from '../../components/atoms';
import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import Logo from '../../components/atoms/Icons/Logo';
import { useUpdatePassword } from '../../hooks/RecoverPassword/UpdatePassword';
import { PasswordRepeat } from '../../components/organisms';
import { usePasswordValidation } from '../../hooks/use-password-validation';

export const ChangePassword = ({ route }: any) => {
  const { email, password: prevPass } = route.params;
  const {
    password,
    repeatPassword,
    passwordValidation,
    passwordMatch,
    passwordValid,
    handlePasswordChange,
    handleRepeatPasswordChange,
  } = usePasswordValidation();

  const navigation: any = useNavigation();
  const [loading, setLoading] = useState<boolean>(false);
  const [errorPass, setErrorPass] = useState<boolean>(false);

  const { updatePassword, success } = useUpdatePassword({ setLoading });

  const handleSubmit = async () => {
    if (!passwordValid) {
      setErrorPass(true);
      return;
    }
    const body = {
      username: email,
      newPass: password,
      prevPass,
    };
    console.log('body', body);
    updatePassword(body);
  };
  useEffect(() => {
    if (success) {
      navigation.navigate('Success');
    }
  }, [success]);

  useDisableBackButton();
  const isDisabled = () => !Object.values(passwordValidation).every(Boolean);

  return (
    <KeyboardAvoidingComponent collapse>
      <View
        style={{
          flex: 1,
          backgroundColor: 'red',
        }}
      >
        <ScrollView contentContainerStyle={styles.container}>
          <View>
            <View style={styles.logo}>
              <Logo height={28} width={120} />
            </View>
            <View>
              <Text style={styles.title}>Ingresa tu</Text>
              <Text style={styles.title}>nueva contraseña</Text>
            </View>
            <View style={styles.containerInputs}>
              <PasswordRepeat
                {...{
                  password,
                  repeatPassword,
                  passwordValidation,
                  passwordMatch,
                  errorPass,
                  handlePasswordChange,
                  handleRepeatPasswordChange,
                }}
              />
            </View>
          </View>

          <Button
            text="Actualizar contraseña"
            onPress={handleSubmit}
            loading={loading}
            disabled={isDisabled()}
          />
        </ScrollView>
      </View>
    </KeyboardAvoidingComponent>
  );
};
