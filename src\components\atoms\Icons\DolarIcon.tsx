import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const DolarIcon: FC<IconProps> = ({ color = '#FF0033', size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M9.83327 9.08333C7.9416 8.59167 7.33327 8.08333 7.33327 7.29167C7.33327 6.38333 8.17493 5.75 9.58327 5.75C11.0666 5.75 11.6166 6.45833 11.6666 7.5H13.5083C13.4499 6.06667 12.5749 4.75 10.8333 4.325V2.5H8.33327V4.3C6.7166 4.65 5.4166 5.7 5.4166 7.30833C5.4166 9.23333 7.00827 10.1917 9.33327 10.75C11.4166 11.25 11.8333 11.9833 11.8333 12.7583C11.8333 13.3333 11.4249 14.25 9.58327 14.25C7.8666 14.25 7.1916 13.4833 7.09994 12.5H5.2666C5.3666 14.325 6.73327 15.35 8.33327 15.6917V17.5H10.8333V15.7083C12.4583 15.4 13.7499 14.4583 13.7499 12.75C13.7499 10.3833 11.7249 9.575 9.83327 9.08333Z"
        fill={color}
      />
    </Svg>
  );
};

export default DolarIcon;
