import { Pressable, View } from 'react-native';
import React, { FC } from 'react';
import { styles } from './styles';
import { TextBase } from '../../../atoms';
import DeleteIcon from '../../../atoms/Icons/DeleteIcon';
import color from '../../../../theme/pallets/pallet';

type HeaderHomeProps = {
  handleDeleteNotification: () => void;
};

const HeaderNotifications: FC<HeaderHomeProps> = ({
  handleDeleteNotification,
}) => {
  return (
    <View style={styles.container}>
      <TextBase size="xxl" color={color.BLACK}>
        Notificaciones
      </TextBase>
      <Pressable style={styles.flexRow} onPress={handleDeleteNotification}>
        <DeleteIcon color={color.PRIMARY_700} size={20} />
        <TextBase size="m" color={color.PRIMARY_700} type="Bold">
          Limpiar
        </TextBase>
      </Pressable>
    </View>
  );
};

export default HeaderNotifications;
