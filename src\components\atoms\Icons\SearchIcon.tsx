import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const SearchIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 21">
      <Path
        d="M9.16666 2.16669C13.3067 2.16669 16.6667 5.52669 16.6667 9.66669C16.6667 13.8067 13.3067 17.1667 9.16666 17.1667C5.02666 17.1667 1.66666 13.8067 1.66666 9.66669C1.66666 5.52669 5.02666 2.16669 9.16666 2.16669ZM9.16666 15.5C12.3892 15.5 15 12.8892 15 9.66669C15 6.44335 12.3892 3.83335 9.16666 3.83335C5.94333 3.83335 3.33333 6.44335 3.33333 9.66669C3.33333 12.8892 5.94333 15.5 9.16666 15.5ZM16.2375 15.5592L18.595 17.9159L17.4158 19.095L15.0592 16.7375L16.2375 15.5592Z"
        fill="#191919"
      />
    </Svg>
  );
};

export default SearchIcon;
