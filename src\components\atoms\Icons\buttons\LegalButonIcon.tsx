import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const LegalButonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#E6DDFF" />

      <Path
        d="M16 24.3333C11.3975 24.3333 7.66666 20.6025 7.66666 16C7.66666 11.3975 11.3975 7.66666 16 7.66666C20.6025 7.66666 24.3333 11.3975 24.3333 16C24.3333 20.6025 20.6025 24.3333 16 24.3333ZM16 22.6667C17.7681 22.6667 19.4638 21.9643 20.714 20.714C21.9643 19.4638 22.6667 17.7681 22.6667 16C22.6667 14.2319 21.9643 12.5362 20.714 11.2859C19.4638 10.0357 17.7681 9.33332 16 9.33332C14.2319 9.33332 12.5362 10.0357 11.2859 11.2859C10.0357 12.5362 9.33332 14.2319 9.33332 16C9.33332 17.7681 10.0357 19.4638 11.2859 20.714C12.5362 21.9643 14.2319 22.6667 16 22.6667ZM15.1667 11.8333H16.8333V13.5H15.1667V11.8333ZM15.1667 15.1667H16.8333V20.1667H15.1667V15.1667Z"
        fill="#5732BF"
      />
    </Svg>
  );
};

export default LegalButonIcon;
