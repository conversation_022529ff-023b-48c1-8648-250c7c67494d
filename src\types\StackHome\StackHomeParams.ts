import { NavigatorScreenParams } from '@react-navigation/native';
import { StackTransferParams } from '../StackTransfer/StackTransferParams';
import { StackCashInParams } from '../StackCashIn/StackCashInParams';
import { StackQrPaymentParams } from '../StackQrPayment/StackQrPaymentParams';
import { StackLoanParams } from '../StackLoan/StackLoanParams';
import { StackCardsParams } from '../StackCards/StackCardsParams';

export type StackHomeParams = {
  Home: undefined;
  Cards: undefined;
  StackTransfer: NavigatorScreenParams<StackTransferParams>;
  StackLoan: NavigatorScreenParams<StackLoanParams>;
  StackCashIn: NavigatorScreenParams<StackCashInParams>;
  StackQrPayment: NavigatorScreenParams<StackQrPaymentParams>;
  StackCards: NavigatorScreenParams<StackCardsParams>;
  WebViewScreen: { link: string };
};
