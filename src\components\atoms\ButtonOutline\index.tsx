import React, { FC } from 'react';
import { ActivityIndicator, TouchableOpacity, View } from 'react-native';
import { styles } from './styles';
import Text, { variants } from '../Text';
import color from '../../../theme/pallets/pallet';

type Props = {
  text: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  outline?: boolean;
  neutralBorder?: boolean;
  variant?: variants;
  icon?: JSX.Element;
};

const ButtonOutline: FC<Props> = ({
  text,
  onPress,
  disabled,
  loading,
  outline,
  neutralBorder,
  icon,
  variant,
}) => {
  const backgroundStyle = outline
    ? styles.outlineBackground
    : styles.background;

  return (
    <TouchableOpacity
      style={[
        disabled || loading
          ? outline
            ? styles.outlineBackground
            : styles.disabledBackground
          : backgroundStyle,
        neutralBorder && { borderColor: color.NEUTRALS_200, borderWidth: 1 },
      ]}
      disabled={disabled || loading}
      onPress={onPress}
    >
      {loading && (
        <ActivityIndicator size="large" color="black" style={{ padding: 5 }} />
      )}

      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {icon && icon}
        {text && (
          <Text
            variant={variant || 'R7'}
            color="PRIMARY_500"
            style={disabled ? styles.disabledText : styles.text}
          >
            {text}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default ButtonOutline;
