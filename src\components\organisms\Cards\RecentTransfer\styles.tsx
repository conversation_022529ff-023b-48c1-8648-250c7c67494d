import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  circleContainer: {
    alignItems: 'center',
    marginHorizontal: 8,
    gap: 5,
  },
  circleImage: {
    width: 64,
    height: 64,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: color.PRIMARY_700,
  },
  circle: {
    backgroundColor: color.NEUTRALS_100,
    width: 64,
    height: 64,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  plus: {
    fontSize: 28,
    color: color.NEUTRALS_800,
  },
});
