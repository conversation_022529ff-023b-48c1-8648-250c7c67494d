export const formatDateToDDMMYYYY = (targetDate: string | Date): string  => {
  const date = new Date(targetDate);
  
  const day = String(date.getUTCDate()).padStart(2, '0');
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const year = date.getUTCFullYear();
  
  return `${day}/${month}/${year}`;
}

export const formatDateToNaturalLanguage = (targetDate: string | Date) => {
  const date = new Date(targetDate);
  const day = date.getUTCDate();

  const monthFormatter = new Intl.DateTimeFormat('es-ES', { month: 'long', timeZone: 'UTC' });
  const month = monthFormatter.format(date);

  return `${day} de ${month}`;
}


export const isDateBeyondNow = (targetDate: string | Date): boolean => {
  const dueDate = new Date(targetDate);
  const now = new Date();

  return dueDate > now;
}

export const calculateDaysUntil = (targetDate: string | Date): number => {
  const today = new Date();
  const target = new Date(targetDate);

  // Set both dates to the start of the day for accurate comparison
  today.setHours(0, 0, 0, 0);
  target.setHours(0, 0, 0, 0);

  // Calculate the difference in time (milliseconds)
  const timeDifference = target.getTime() - today.getTime();

  // Convert milliseconds to days
  const daysDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

  return daysDifference;
}

export const getCurrentMonthDays = (): number => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth(); // 0-indexed: January is 0, December is 11

  // Set date to the first day of the next month, then subtract one day
  const nextMonth = new Date(year, month + 1, 1);
  nextMonth.setHours(0, 0, 0, 0); // Ensure time is set to start of day

  // Subtract one day to get the last day of the current month
  const lastDayOfMonth = new Date(nextMonth.getTime() - 1);

  return lastDayOfMonth.getDate(); // This returns the number of days in the current month
}