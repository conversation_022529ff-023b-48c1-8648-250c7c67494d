import React from 'react';
import { Modal, View } from 'react-native';
import { Button, TextBase } from '../../../atoms';
import { styles } from './styles';
import { Account } from '../../../../types/Account';
import ButtonOutline from '../../../atoms/ButtonOutline';
import WarningIcon from '../../../atoms/Icons/Warning';
import color from '../../../../theme/pallets/pallet';

type Props = {
  modalView: boolean;
  setModalView: (value: boolean) => void;
  desactivateAccount: () => void;
  accountData?: Account;
};

export const DesactivateAccount = ({
  modalView,
  setModalView,
  desactivateAccount,
}: Props) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalView}
      onRequestClose={() => {
        setModalView(!modalView);
      }}
    >
      <View style={styles.overlay}>
        <View style={styles.modalView}>
          <View style={styles.containerAccount}>
            <WarningIcon />
            <TextBase
              size="l"
              color={color.TEXT_PRIMARY}
              type="Bold"
              style={styles.textModal}
            >
              ¿Deseas solicitar la baja de tu cuenta?
            </TextBase>
            <TextBase
              size="m"
              color={color.NEUTRALS_800}
              style={styles.textModal}
            >
              Estás a punto de eliminar permanentemente tu cuenta. Al confirmar,
              se eliminará tu CVU y no podrás volver a operar con Toshify.
            </TextBase>
            <View style={styles.warning}>
              <TextBase
                size="m"
                color={color.NEUTRALS_800}
                style={styles.textModal}
              >
                Para realizar esta acción no debes tener dinero en tu cuenta.
              </TextBase>
            </View>
          </View>
          <View style={styles.mt24}>
            <Button onPress={desactivateAccount} text="Si, desactivar cuenta" />
            <ButtonOutline
              onPress={() => setModalView(false)}
              text="No, cancelar"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};
