import React, { FC } from 'react'
import Svg, { Path } from 'react-native-svg'
import { IconProps } from './types'

const ArrowLeftLarge: FC<IconProps> = ({ size, color }) => {
    return (

        <Svg width={size} height={size} viewBox="0 0 8 8">
            <Path fill-rule="evenodd" clip-rule="evenodd" d="M8 4.00034C8 4.13295 7.94732 4.26013 7.85355 4.3539C7.75979 4.44767 7.63261 4.50034 7.5 4.50034L1.707 4.50034L3.854 6.64634C3.94789 6.74023 4.00063 6.86757 4.00063 7.00034C4.00063 7.13312 3.94789 7.26046 3.854 7.35434C3.76011 7.44823 3.63278 7.50098 3.5 7.50098C3.36722 7.50098 3.23989 7.44823 3.146 7.35434L0.146 4.35434C0.099437 4.3079 0.0624946 4.25272 0.037288 4.19198C0.0120814 4.13123 -0.000893293 4.06611 -0.000893287 4.00034C-0.000893281 3.93458 0.0120815 3.86946 0.037288 3.80871C0.0624946 3.74797 0.0994371 3.69279 0.146 3.64634L3.146 0.646344C3.23989 0.552457 3.36722 0.499713 3.5 0.499713C3.63278 0.499713 3.76011 0.552457 3.854 0.646344C3.94789 0.740231 4.00063 0.867569 4.00063 1.00034C4.00063 1.13312 3.94789 1.26046 3.854 1.35434L1.707 3.50034L7.5 3.50034C7.63261 3.50034 7.75979 3.55302 7.85355 3.64679C7.94732 3.74056 8 3.86774 8 4.00034Z" fill={color} />
        </Svg>

    )
}

export default ArrowLeftLarge