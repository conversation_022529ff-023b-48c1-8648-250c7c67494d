import React from 'react';
import HeaderTransfer from '../../components/organisms/Headers/Transfer';
import { Pressable, Text, View } from 'react-native';
import { StyleSheet } from 'react-native';
import BankRoundedButton from '../../components/atoms/Icons/buttons/BankRoundedButton';
import TransferRoundedButton from '../../components/atoms/Icons/buttons/TransferRoundedButton';
import LineWithText from '../../helpers/LineWhitText';
import ContactCard from '../../components/organisms/Cards/Contacts';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import FavoritesCard from '../../components/organisms/Cards/Favorites';
import { FavoritesProvider } from '../../context/TransferContext';
import color from '../../theme/pallets/pallet';
import TransferButtonToshi from '../../components/atoms/Icons/buttons/TransferButtonToshi';

type Props = NativeStackScreenProps<any>;

export const TransferScreen = ({ navigation }: Props) => {
  return (
    <FavoritesProvider>
      <View style={styles.containter}>
        <HeaderTransfer text="Transferir" />
        <View style={styles.buttons}>
          <Pressable
            style={styles.containerButtons}
            onPress={() => navigation.navigate('NewAccountScreen')}
          >
            <BankRoundedButton />
            <Text style={styles.txt}>Mediante CBU, CVU o Alias</Text>
          </Pressable>
          <Pressable style={styles.containerButtons}>
            <TransferButtonToshi />
            <Text style={styles.txt}>Con celular, mail, nombre o apellido</Text>
          </Pressable>
        </View>
        <LineWithText text={'o'} />
        {/* <FavoritesCard /> */}
        {/* <RecentTransferCard text={'Favoritos'} circle /> */}
        <ContactCard />
      </View>
    </FavoritesProvider>
  );
};

const styles = StyleSheet.create({
  containter: {
    flex: 1,
    backgroundColor: color.WHITE,
    paddingHorizontal: 16,
  },
  buttons: {
    marginTop: 24,
  },
  containerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  txt: {
    fontSize: 14,
    fontFamily: 'Satoshi-Regular',
    color: color.TEXT_PRIMARY,
    marginLeft: 8,
  },
});
