import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import GoBack from '../components/organisms/Buttons/GoBackButton';
import { SimulateLoanScreen } from '../screens/StackLoan/SimulateLoanScreen';
import { SelectPaymentScreen } from '../screens/StackLoan/SelectPaymentScreen';
import { LoanResumeScreen } from '../screens/StackLoan/LoanResumeScreen';
import { LoansHomeScreen } from '../screens/StackLoan/LoansHomeScreen';
import { NewLoanScreen } from '../screens/StackLoan/NewLoanScreen';
import { SimulateLoanIntroScreen } from '../screens/StackLoan/SimulateLoanIntroScreen';
import { RequestingNewLoanScreen } from '../screens/StackLoan/RequestingNewLoanScreen';
import { LoanDetailedScreen } from '../screens/StackLoan/LoanDetailedScreen';
import { NewLoanSuccessScreen } from '../screens/StackLoan/NewLoanSuccessScreen';
import { PayInstallmentConfirmation } from '../screens/StackLoan/PayInstallmentConfirmation';
import { PayInstallmentResultScreen } from '../screens/StackLoan/PayInstallmentResultScreen';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<any>();

export default function StackLoan() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'LoansHomeScreen'}
        component={LoansHomeScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'NewLoanScreen'}
        component={NewLoanScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'SimulateLoanIntroScreen'}
        component={SimulateLoanIntroScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'SimulateLoanScreen'}
        component={SimulateLoanScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'SelectPaymentScreen'}
        component={SelectPaymentScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'LoanResumeScreen'}
        component={LoanResumeScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
        })}
        name={'RequestingNewLoanScreen'}
        component={RequestingNewLoanScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'LoanDetailedScreen'}
        component={LoanDetailedScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
        })}
        name={'NewLoanSuccessScreen'}
        component={NewLoanSuccessScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
        })}
        name={'PayInstallmentConfirmation'}
        component={PayInstallmentConfirmation}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
        })}
        name={'PayInstallmentResultScreen'}
        component={PayInstallmentResultScreen}
      />
    </Stack.Navigator>
  );
}
