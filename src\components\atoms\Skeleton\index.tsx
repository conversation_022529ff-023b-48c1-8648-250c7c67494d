import React, { FC } from 'react';
import { StyleSheet, View } from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

interface Props {
  backgroundColor?: string;
  highlightColor?: string;
  width?: number;
  height?: number;
  marginVertical?: number;
  borderRadius?: number;
}

export const Skeleton: FC<Props> = ({
  backgroundColor,
  highlightColor,
  width,
  height,
  marginVertical,
  borderRadius,
}) => {
  return (
    <View style={styles.skeleton}>
      <SkeletonPlaceholder
        backgroundColor={backgroundColor}
        highlightColor={highlightColor}
      >
        <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
          <SkeletonPlaceholder.Item
            width={width}
            height={height}
            marginVertical={marginVertical}
            borderRadius={borderRadius}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};

const styles = StyleSheet.create({
  skeleton: {
    height: 40,
  },
});
