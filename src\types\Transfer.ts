// Generated by https://quicktype.io

export interface TransactionsResponse {
  error: boolean;
  data: Data;
}

interface Data {
  totalPages: number;
  currentPage: number;
  total: number;
  list: Transactions[];
}

export interface Transactions {
  _id: string;
  userId: string;
  txType: string;
  txReferenceIdId: string;
  id: string;
  amount: number;
  description: string;
  smImpactedOn: string;
  smCreatedOn: string;
  transactionReason: TransactionReason;
  sgInfo: SgInfo;
  smUnit: SmUnit;
  userName: string;
  counterpartName: string;
  trxSubject: string;
  sgTransactionId: string;
  smTransactionId: string;
  internal: boolean;
  cuitCredito: string;
  cvuCredito: string;
  cvuDebito: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  operationId: string;
  trxAttach: any[];
  trxObservations: string;
}

interface SgInfo {
  idCoelsa: string;
  cuitCredito: string;
  cvuCredito: string;
  txStatus: string;
  txStatusId: string;
  txTypeTxt: string;
}

interface SmUnit {
  symbol: string;
  description: string;
}

interface TransactionReason {
  id: string;
  description: string;
}
