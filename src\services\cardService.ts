import { USER_ID } from '../constants/asyncStorageKeys';
import { CreatePhyisicalCardRequest, CreateVirtualCardRequest } from '../types/StackCards/CardsRequests';
import { api } from './apiService';
import { getData } from './asyncStorageService';

const CountryNamePartially = "ARG";
const CountryName = "Argentina";

export const createPhysicalCard = async (request: CreatePhyisicalCardRequest) => {
  try {    
    const userId = await getData(USER_ID);
    request = addHardCodedPropsInPhysical(request);

    const response = await api.post(`/cards/create-physical/${userId}`, request);

    return response;
  } catch (error: any) {
    console.error('Error creating physical card:', error.response);
    throw error;
  }
}

export const createVirutalCard = async (request: CreateVirtualCardRequest) => {
  try {    
    const userId = await getData(USER_ID);
    request = addHardCodedPropsInVirtual(request);
    const response = await api.post(`/cards/create-virtual/${userId}`, request);

    return response;
  } catch (error: any) {
    console.error('Error creating virtual card:', error.response);
    throw error;
  }
}

const addHardCodedPropsInPhysical = (request: CreatePhyisicalCardRequest) => {
  request.country = CountryNamePartially;
  request.address.country = CountryName;

  return request;
}

const addHardCodedPropsInVirtual = (request: CreateVirtualCardRequest) => {
  request.legalAddress.country = CountryNamePartially;

  return request;
}