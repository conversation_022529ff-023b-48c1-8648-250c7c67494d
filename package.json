{"name": "toshbank", "version": "0.0.1", "private": true, "scripts": {"android": "run-script-os", "android-qa": "run-script-os", "android-dev": "run-script-os", "android-demo": "run-script-os", "android-prod": "run-script-os", "android:windows": "set ENVFILE=.env && react-native run-android", "android:macos": "ENVFILE=.env react-native run-android", "android-qa:windows": "set ENVFILE=.env.qa && react-native run-android", "android-qa:macos": "ENVFILE=.env.qa react-native run-android", "android-dev:windows": "set ENVFILE=.env.dev && react-native run-android", "android-dev:macos": "ENVFILE=.env.dev react-native run-android", "android-demo:windows": "set ENVFILE=.env.demo && react-native run-android", "android-demo:macos": "ENVFILE=.env.demo react-native run-android", "android-prod:windows": "set ENVFILE=.env.prod && react-native run-android", "android-prod:macos": "ENVFILE=.env.prod react-native run-android", "ios": "ENVFILE=.env react-native run-ios", "ios-qa": "ENVFILE=.env.qa react-native run-ios", "ios-dev": "ENVFILE=.env.dev react-native run-ios", "ios-demo": "ENVFILE=.env.demo react-native run-ios", "ios-prod": "ENVFILE=.env.prod react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "clean": "cd android && gradlew clean && cd ..", "clean-drawables": "rm -rf ./android/app/src/main/res/drawable-*", "generate-apk-debug-qa": "run-script-os", "generate-apk-debug-demo": "run-script-os", "generate-apk-debug-qa:windows": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res && cd android && set ENVFILE=.env.qa && gradlew.bat assembleDebug", "generate-apk-debug-qa:macos": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res && cd android && ENVFILE=.env.qa ./gradlew assembleDebug", "generate-apk-debug-demo:windows": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res && cd android && set ENVFILE=.env.demo && gradlew assembleDebug", "generate-apk-debug-demo:macos": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res && cd android && ENVFILE=.env.demo ./gradlew assembleDebug", "generate-apk-release": "run-script-os", "generate-apk-release:windows": "yarn clean-drawables && react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest ./android/app/build/intermediates/res/merged/release/ && rm -rf ./android/app/src/main/res/raw/* && cd android && set ENVFILE=.env.prod && ./gradlew assembleRelease && cd ..", "generate-apk-release:macos": "yarn clean-drawables && react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest ./android/app/build/intermediates/res/merged/release/ && rm -rf ./android/app/src/main/res/raw/* && cd android && ENVFILE=.env.prod ./gradlew assembleRelease && cd ..", "generate-bundle-release": "run-script-os", "generate-bundle-release:windows": "yarn clean-drawables && react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest ./android/app/build/intermediates/res/merged/release/ && rm -rf ./android/app/src/main/res/raw/* && cd android && set ENVFILE=.env.prod ./gradlew bundleRelease && cd ..", "generate-bundle-release:macos": "yarn clean-drawables && react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest ./android/app/build/intermediates/res/merged/release/ && rm -rf ./android/app/src/main/res/raw/* && cd android && ENVFILE=.env.prod ./gradlew bundleRelease && cd ..", "find-deadcode": "ts-prune", "build:ios": "react-native bundle --entry-file='index.js' --bundle-output='./ios/GoiarWallet/main.jsbundle' --dev=false --platform='ios' --assets-dest='./ios'"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@react-native-async-storage/async-storage": "^1.21.0", "@react-native-clipboard/clipboard": "^1.13.2", "@react-native-community/checkbox": "^0.5.17", "@react-native-firebase/app": "^18.8.0", "@react-native-firebase/messaging": "^18.8.0", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.6.1", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@shopify/flash-list": "^1.6.3", "axios": "^1.6.2", "formik": "^2.4.5", "jail-monkey": "^2.8.0", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "18.2.0", "react-hook-form": "^7.51.0", "react-native": "0.72.8", "react-native-base64": "^0.2.1", "react-native-biometrics": "^3.0.1", "react-native-camera": "react-native-camera/react-native-camera", "react-native-config": "^1.5.1", "react-native-country-flag": "^2.0.2", "react-native-crypto-aes-cbc": "^1.1.3", "react-native-device-info": "^10.12.0", "react-native-encrypted-storage": "^4.0.3", "react-native-gesture-handler": "2.14.0", "react-native-html-to-pdf": "^0.12.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.12.2", "react-native-orientation-locker": "^1.7.0", "react-native-pager-view": "6.3.3", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "3.29.0 ", "react-native-share": "^10.1.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^14.1.0", "react-native-swiper": "^1.6.0", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.1", "react-native-vision-camera": "^3.9.0", "react-native-webview": "13.8.1", "run-script-os": "^1.1.6", "yup": "^1.3.3", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.6", "@types/lodash": "^4.14.202", "@types/react": "^18.0.24", "@types/react-native-base64": "^0.2.2", "@types/react-native-html-to-pdf": "^0.8.3", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "ts-prune": "^0.10.3", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}