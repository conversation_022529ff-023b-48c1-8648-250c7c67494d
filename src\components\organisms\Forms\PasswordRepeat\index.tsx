import React from 'react';
import { InputForm } from '../../../molecules';
import { StyleSheet, View } from 'react-native';
import CheckPassIcon from '../../../atoms/Icons/CheckPassIcon';
import CrossIcon from '../../../atoms/Icons/CrossIcon';
import color from '../../../../theme/pallets/pallet';
import { Text } from '../../../atoms';

type PasswordValidation = {
  length: boolean;
  uppercase: boolean;
  lowercase: boolean;
  number: boolean;
  specialChar: boolean;
};
type PasswordRepeatProps = {
  password: string;
  repeatPassword: string;
  passwordValidation: PasswordValidation;
  passwordMatch: boolean;
  errorPass: boolean;
  handlePasswordChange: (newPassword: string) => void;
  handleRepeatPasswordChange: (newRepeatPassword: string) => void;
};

export const PasswordRepeat = ({
  password,
  repeatPassword,
  passwordValidation,
  passwordMatch,
  errorPass,
  handlePasswordChange,
  handleRepeatPasswordChange,
}: PasswordRepeatProps) => {
  return (
    <>
      <InputForm
        placeholder="Ingresa una contraseña"
        hideText={true}
        text={password}
        setText={handlePasswordChange}
        maxLength={20}
        error={!passwordMatch}
        autoCapitalize="none"
      />
      <Text variant="R7" style={stylesName.topText}>
        Repetir contraseña
      </Text>

      <InputForm
        placeholder="Repite la contraseña"
        hideText={true}
        text={repeatPassword}
        setText={handleRepeatPasswordChange}
        maxLength={20}
        error={!passwordMatch || errorPass}
        errorText={
          !passwordMatch
            ? 'Las contraseñas no coinciden'
            : errorPass
              ? 'Hubo un error.'
              : ''
        }
        autoCapitalize="none"
      />

      <Text variant="R7" color="NEUTRALS_600" style={stylesName.txtDesc}>
        La contraseña debe contener como mínimo:
      </Text>
      {['length', 'uppercase', 'lowercase', 'number', 'specialChar'].map(
        (key, index) => (
          <View key={index} style={stylesName.ckeckPass}>
            {passwordValidation[key as keyof PasswordValidation] ? (
              <CheckPassIcon />
            ) : (
              <CrossIcon />
            )}
            <Text
              variant="R6"
              style={
                passwordValidation[key as keyof PasswordValidation]
                  ? stylesName.greenTxt
                  : stylesName.redTxt
              }
            >
              {key === 'length'
                ? '6 caracteres'
                : key === 'uppercase'
                  ? 'Una mayúscula'
                  : key === 'lowercase'
                    ? 'Una minúscula'
                    : key === 'number'
                      ? 'Un número'
                      : 'Un carácter especial'}
            </Text>
          </View>
        ),
      )}
    </>
  );
};

const stylesName = StyleSheet.create({
  topText: {
    marginVertical: 10,
    color: color.TEXT_PRIMARY,
    fontWeight: '400',
  },
  ckeckPass: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  redTxt: {
    color: '#FF0033',
    marginLeft: 5,
    marginBottom: 3,
  },
  greenTxt: {
    color: '#4DA66B',
    marginLeft: 5,
    marginBottom: 3,
  },
  txtDesc: {
    marginVertical: 5,
  },
});
