import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const RecibirTransferencia: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width="32" height="32" viewBox="0 0 32 32" fill="none">
      <Rect width="32" height="32" rx="8" fill="#F0F6FF" />
      <Path
        d="M18.5 21.8333V20.1667H13.0083L22.6667 10.5083L21.4917 9.33333L11.8333 18.9917V13.5H10.1667V21.8333H18.5Z"
        fill="#0068FF"
      />
    </Svg>
  );
};

export default RecibirTransferencia;
