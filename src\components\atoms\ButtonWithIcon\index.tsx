import React, { FC } from 'react';
import { ActivityIndicator, TouchableOpacity, View } from 'react-native';
import { styles } from './styles';
import TextBase from '../TextBase';
import color from '../../../theme/pallets/pallet';

const colors = color;
type Props = {
  icon: React.JSX.Element;
  text: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  bgColor: string;
  color: string;
  extraStyles?: any;
};

const ButtonWithIcon: FC<Props> = ({
  icon,
  text,
  onPress,
  disabled,
  loading,
  bgColor,
  color,
  extraStyles,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.background,
        {
          backgroundColor: disabled || loading ? colors.NEUTRALS_100 : bgColor,
        },
        extraStyles,
      ]}
      disabled={disabled || loading}
      onPress={onPress}
    >
      {loading && (
        <ActivityIndicator size="large" color="black" style={{ padding: 5 }} />
      )}
      <View>{icon}</View>

      <TextBase
        type="SemiBold"
        style={disabled ? styles.disabledText : [styles.text, { color }]}
      >
        {text}
      </TextBase>
    </TouchableOpacity>
  );
};

export default ButtonWithIcon;
