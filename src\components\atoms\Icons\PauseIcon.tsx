import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const PauseIcon: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M7.49996 13.3333H9.16663V6.66663H7.49996V13.3333ZM9.99996 1.66663C5.39996 1.66663 1.66663 5.39996 1.66663 9.99996C1.66663 14.6 5.39996 18.3333 9.99996 18.3333C14.6 18.3333 18.3333 14.6 18.3333 9.99996C18.3333 5.39996 14.6 1.66663 9.99996 1.66663ZM9.99996 16.6666C6.32496 16.6666 3.33329 13.675 3.33329 9.99996C3.33329 6.32496 6.32496 3.33329 9.99996 3.33329C13.675 3.33329 16.6666 6.32496 16.6666 9.99996C16.6666 13.675 13.675 16.6666 9.99996 16.6666ZM10.8333 13.3333H12.5V6.66663H10.8333V13.3333Z"
        fill={color}
      />
    </Svg>
  );
};

export default PauseIcon;
