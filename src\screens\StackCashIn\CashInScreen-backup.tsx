import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Keyboard,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
  TouchableWithoutFeedback,
  Text,
} from 'react-native';
import HeaderTransfer from '../../components/organisms/Headers/Transfer';
import { Button } from '../../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { isEmpty } from 'lodash';
import { InputAmount } from '../../components/molecules/InputAmount';
import { SelectCashInModal } from '../../components/organisms/Modals/SelectCashIn';

type Props = NativeStackScreenProps<any>;

export const CashInScreen = ({ navigation }: Props) => {
  const [modalVisible, setModalVisible] = useState(false);

  const [amount, onChangeValue] = useState(0);

  const inputRef = useRef<TextInput>(null);
  const [keyboardOpen, setKeyboardOpen] = useState(false);

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardOpen(true);
    });
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardOpen(false);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  const handleAmount = useCallback(
    (value: string) => {
      onChangeValue(Number(value));
    },
    [onChangeValue],
  );

  const handleOnPress = () => {
    if (keyboardOpen) {
      inputRef.current?.blur();
    }
    inputRef.current?.focus();
  };

  const handleKeyPress = (
    event: NativeSyntheticEvent<TextInputKeyPressEventData>,
  ) => {
    const key = event.nativeEvent.key;

    let newValue = amount.toString();
    const maxDecimals = 2;

    const sanitizedKey = key.replace(',', '.');
    const validKeyRegExp = /^[0-9.]$/;
    const decimalRegExp = /^\./;
    const validNumberRegExp = new RegExp(`^\\d+(\\.\\d{0,${maxDecimals}})?$`);

    if (key === 'Backspace' && newValue.length > 0) {
      newValue = newValue.slice(0, -1);
      handleAmount(newValue);
      return;
    }
    if (isEmpty(newValue) && decimalRegExp.test(newValue + sanitizedKey)) {
      newValue = '0' + sanitizedKey;
      handleAmount(newValue);
      return;
    }
    if (isEmpty(newValue) && sanitizedKey === '0') {
      newValue = sanitizedKey + '.';
      handleAmount(newValue);
      return;
    }

    if (!validKeyRegExp.test(sanitizedKey)) {
      return;
    }
    if (!validNumberRegExp.test(newValue + sanitizedKey)) {
      return;
    }

    newValue += sanitizedKey;

    handleAmount(newValue);
  };

  const navigateTo = (screen: string) => {
    navigation.navigate(screen);
  };

  const keyboardDismiss = () => {
    Keyboard.dismiss();
  };

  return (
    <TouchableWithoutFeedback accessible={false} onPress={keyboardDismiss}>
      <View style={styles.container}>
        <View style={styles.header}>
          <HeaderTransfer text="Ingresar dinero" />
          <Text style={styles.txt}>Ingresar importe</Text>
          <InputAmount
            amount={amount}
            handleOnPress={handleOnPress}
            inputRef={inputRef}
            handleKeyPress={handleKeyPress}
          />
        </View>
        <SelectCashInModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          navigateTo={navigateTo}
        />
        <View style={styles.button}>
          <Button
            text="Continuar"
            onPress={() => setModalVisible(!modalVisible)}
            disabled={amount === 0}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  button: {
    marginVertical: 32,
    width: '100%',
  },
  txt: {
    fontSize: 12,
    fontFamily: 'Satoshi-Regular',
    color: '#78838D',
    marginVertical: 8,
  },
});
