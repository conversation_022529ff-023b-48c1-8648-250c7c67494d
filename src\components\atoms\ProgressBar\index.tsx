import React from 'react';
import { View, StyleSheet } from 'react-native';
import color from '../../../theme/pallets/pallet';

interface ProgressBarProps {
  percentage: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ percentage }) => {
  return (
    <View style={styles.progressBar}>
      <View style={[styles.progress, { width: `${percentage}%` }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  progressBar: {
    marginRight: '100%',
    height: 8,
    width: '100%',
    backgroundColor: color.NEUTRALS_100,
    borderRadius: 5,
    overflow: 'hidden',
  },
  progress: {
    height: '100%',
    backgroundColor: '#FF0033',
  },
});

export default ProgressBar;
