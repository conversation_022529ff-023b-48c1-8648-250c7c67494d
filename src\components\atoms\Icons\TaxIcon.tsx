import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const TaxIcon: FC<IconProps> = () => {
  return (
    <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <Rect width="24" height="24" rx="4" fill="#F0F6FF" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.94741 7.24818C8.42047 6.74252 9.16425 6.625 10.11 6.625H13.89C14.8356 6.625 15.5797 6.74245 16.0522 7.24842C16.5164 7.7455 16.6225 8.519 16.62 9.52054V15.155C16.62 15.5174 16.5712 15.8384 16.4653 16.0993C16.3589 16.3614 16.1831 16.5882 15.9199 16.7018C15.6539 16.8166 15.3691 16.7856 15.1093 16.6736C14.8532 16.5631 14.6001 16.3666 14.3563 16.1064L14.3556 16.1056C14.2211 15.9613 14.0681 15.9096 13.9341 15.9169C13.8 15.9243 13.6532 15.9924 13.535 16.15L13.0303 16.8246L13.029 16.8263C12.7663 17.1733 12.3967 17.3763 11.9975 17.3763C11.5983 17.3763 11.2287 17.1733 10.966 16.8263L10.9647 16.8246L10.46 16.15C10.3418 15.9924 10.195 15.9243 10.0609 15.9169C9.92687 15.9096 9.77385 15.9613 9.63935 16.1056L9.63871 16.1063C9.39602 16.3655 9.14348 16.5614 8.88747 16.6715C8.62786 16.7832 8.34318 16.8139 8.07728 16.6994C7.81397 16.586 7.63752 16.3595 7.53058 16.0969C7.42417 15.8356 7.375 15.5138 7.375 15.15V9.52C7.375 8.51822 7.48266 7.74495 7.94741 7.24818ZM8.49509 7.76057C8.25234 8.02005 8.125 8.50678 8.125 9.52V15.15C8.125 15.4537 8.16708 15.6713 8.2252 15.814C8.2828 15.9555 8.34415 15.9977 8.37397 16.0106C8.40119 16.0223 8.46495 16.0368 8.59112 15.9825C8.72089 15.9267 8.89148 15.807 9.09129 15.5937L9.365 15.85L9.09065 15.5944C9.36615 15.2987 9.73063 15.1479 10.1016 15.1681C10.4725 15.1882 10.8182 15.3776 11.06 15.7L11.564 16.3737L11.5646 16.3744C11.7068 16.5619 11.8668 16.6263 11.9975 16.6263C12.1282 16.6263 12.2883 16.5619 12.4305 16.3744L12.431 16.3737L12.9347 15.7004C13.1765 15.378 13.5225 15.1882 13.8934 15.1681C14.2642 15.1479 14.6285 15.2985 14.9039 15.5939C15.105 15.8085 15.2763 15.9288 15.4063 15.9849C15.5328 16.0394 15.5961 16.0247 15.6226 16.0132C15.6519 16.0006 15.713 15.9586 15.7703 15.8172C15.8282 15.6747 15.87 15.4576 15.87 15.155V9.52L15.87 9.51907C15.8725 8.50581 15.746 8.01943 15.5041 7.76032C15.2703 7.51005 14.8344 7.375 13.89 7.375H10.11C9.16575 7.375 8.72953 7.50998 8.49509 7.76057Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.54803 11.5C9.54803 11.2239 9.77189 11 10.048 11H10.0525C10.3287 11 10.5525 11.2239 10.5525 11.5C10.5525 11.7761 10.3287 12 10.0525 12H10.048C9.77189 12 9.54803 11.7761 9.54803 11.5Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.0742 11.5C11.0742 11.2929 11.2421 11.125 11.4492 11.125H14.1992C14.4063 11.125 14.5742 11.2929 14.5742 11.5C14.5742 11.7071 14.4063 11.875 14.1992 11.875H11.4492C11.2421 11.875 11.0742 11.7071 11.0742 11.5Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.54803 9.5C9.54803 9.22386 9.77189 9 10.048 9H10.0525C10.3287 9 10.5525 9.22386 10.5525 9.5C10.5525 9.77614 10.3287 10 10.0525 10H10.048C9.77189 10 9.54803 9.77614 9.54803 9.5Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.0742 9.5C11.0742 9.29289 11.2421 9.125 11.4492 9.125H14.1992C14.4063 9.125 14.5742 9.29289 14.5742 9.5C14.5742 9.70711 14.4063 9.875 14.1992 9.875H11.4492C11.2421 9.875 11.0742 9.70711 11.0742 9.5Z"
        fill="#0068FF"
      />
    </Svg>
  );
};

export default TaxIcon;
