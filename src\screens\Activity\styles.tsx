import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    flex: 1,
    padding: 12,
  },
  date: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 10,
  },
  detail: {
    width: '100%',
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 8,
    paddingBottom: 8,
    backgroundColor: '#F7F8FE',
    borderRadius: 8,
    borderLeftWidth: 2,
    borderLeftColor: '#FDC228',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    marginTop: 20,
  },
  detailBot: {
    marginTop: 15,
  },
  idCoelsa: {
    marginTop: 10,
    gap: 5,
  },
  copyText: {
    display: 'flex',
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  button: {
    marginBottom: 16,
    marginHorizontal: 16,
    backgroundColor: '#FFFFFF',
  },
  nroComprobante: {
    marginTop: 20,
  },
  detailSkeleton: {
    padding: 10,
  },
  detailSkeletonBot: {
    padding: 10,
    marginTop: 70,
  },
  idSkeleton: {
    marginTop: 90,
  },
});
