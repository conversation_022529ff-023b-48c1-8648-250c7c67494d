import { useEffect, useState } from 'react';

import { BiometryType } from 'react-native-biometrics';

import { checkSensorAvailability } from '../lib/biometrics';

type Biometrics = {
  available: boolean;
  biometryType?: BiometryType;
  isLoading: boolean;
  error?: any;
};
export const useBiometrics = () => {
  const [biometric, setBiometric] = useState<Biometrics>({
    available: false,
    biometryType: undefined,
    isLoading: true,
    error: undefined,
  });

  useEffect(() => {
    const checkMethod = async () => {
      try {
        const { available, biometryType } = await checkSensorAvailability();
        setBiometric(prevState => ({
          ...prevState,
          available,
          biometryType,
          isLoading: false,
        }));
      } catch (error: any) {
        setBiometric(prevState => ({
          ...prevState,
          error: error.message,
          isLoading: false,
        }));
      }
    };
    checkMethod();
  }, []);

  return biometric;
};
