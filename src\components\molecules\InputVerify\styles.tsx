import { StyleSheet } from 'react-native';
import color from '../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  mainContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 2,
    width: '80%',
    borderBottomColor: color.NEUTRALS_400,
  },
  container: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  input: {
    color: 'transparent',
    position: 'absolute',
    zIndex: 0,
    fontSize: 0,
  },
  textInput: {
    fontSize: 50,
    color: color.TEXT_PRIMARY,
    fontFamily: 'Satoshi-Regular',
    textAlign: 'center',
  },
});
