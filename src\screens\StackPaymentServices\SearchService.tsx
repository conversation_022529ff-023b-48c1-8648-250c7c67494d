import React from 'react';
import { View } from 'react-native';
import { StyleSheet } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { TextBase } from '../../components/atoms';
import { InputSearch } from '../../components/molecules';
import PopularServices from '../../components/organisms/Cards/PopularServices';
import CardServices from '../../components/organisms/Cards/CardServices';
import OtherServices from '../../components/organisms/Cards/OtherServices';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const SearchService = ({ navigation }: Props) => {
  return (
    <KeyboardAvoidingComponent>
      <View style={styles.containter}>
        <TextBase size="xxl" type="Bold">
          Buscar un servicio
        </TextBase>
        <View style={styles.buttons}>
          <InputSearch
            placeholder={'Busca entre más de 4.000 empresas'}
            text={''}
            setText={() => { }}
          />
        </View>
        <PopularServices />
        <CardServices />
        <OtherServices />
      </View>
    </KeyboardAvoidingComponent>
  );
};

const styles = StyleSheet.create({
  containter: {
    flex: 1,
    backgroundColor: color.WHITE,
    paddingHorizontal: 16,
  },
  buttons: {
    marginTop: '10%',
  },
  containerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  txt: {
    fontSize: 14,
    fontFamily: 'Satoshi-Regular',
    color: color.TEXT_PRIMARY,
    marginLeft: 8,
  },
});
