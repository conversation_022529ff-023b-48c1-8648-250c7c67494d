import React, { <PERSON> } from 'react';
import Svg, { Path, Circle } from 'react-native-svg';
import { IconProps } from './types';

const CodeServicesIcon: FC<IconProps> = ({ width, height }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 48 48" fill="none">
      <Circle cx="24" cy="24" r="24" fill="#FFE7E3" />
      <Path
        d="M13 33V28H15V31H18V33H13ZM30 33V31H33V28H35V33H30ZM16 30V18H18V30H16ZM19 30V18H20V30H19ZM22 30V18H24V30H22ZM25 30V18H28V30H25ZM29 30V18H30V30H29ZM31 30V18H32V30H31ZM13 20V15H18V17H15V20H13ZM33 20V17H30V15H35V20H33Z"
        fill="#FF0033"
      />
    </Svg>
  );
};

export default CodeServicesIcon;
