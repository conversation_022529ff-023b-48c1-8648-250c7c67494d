import { Pressable, Text, View } from 'react-native';
import React, { FC } from 'react';
import { styles } from './styles';
import { TextBase, UserIcon } from '../../../atoms';
import { useNavigation } from '@react-navigation/native';
import color from '../../../../theme/pallets/pallet';

type HeaderHomeProps = {
  nombre: string;
  apellido: string;
  navigate: (screen: string) => void;
  disabled?: boolean;
};

const HeaderMore: FC<HeaderHomeProps> = ({
  nombre,
  apellido,
  navigate,
  disabled,
}) => {
  const navigation = useNavigation();
  return (
    <View style={styles.container}>
      <Text style={styles.txt}>Más</Text>
      <Pressable
        style={styles.perfil}
        onPress={() =>
          navigation.navigate('StackUser', { screen: 'EditProfile' })
        }
        // onPress={() => navigate('StackUser')}
        disabled={disabled}
      >
        <View style={styles.flexRow}>
          {nombre && (
            <UserIcon
              usernameInitials={nombre[0] + apellido[0]}
              backgroundColor={'#BDBDBD'}
            />
          )}
          <TextBase size="l" type="Bold">
            {nombre} {apellido}
          </TextBase>
        </View>
        <TextBase size="m" color={color.PRIMARY_700} type="Bold">
          Mi perfil
        </TextBase>
      </Pressable>
    </View>
  );
};

export default HeaderMore;
