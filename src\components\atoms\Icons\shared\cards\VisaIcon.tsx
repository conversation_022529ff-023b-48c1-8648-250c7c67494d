import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from '../../types';

const VisaIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height="22" viewBox="0 0 64 22" fill="none">
      <Path d="M27.7333 21.0542H22.5488L25.7916 1.01031H30.9757L27.7333 21.0542Z" fill="white" />
      <Path d="M46.527 1.50038C45.5044 1.09482 43.8825 0.647034 41.877 0.647034C36.7572 0.647034 33.1518 3.37627 33.1297 7.27825C33.0872 10.1571 35.711 11.7561 37.6735 12.716C39.6793 13.6969 40.3611 14.3371 40.3611 15.2114C40.3407 16.5542 38.7403 17.1732 37.2476 17.1732C35.1778 17.1732 34.0687 16.8541 32.3833 16.107L31.7006 15.7868L30.9751 20.2862C32.191 20.84 34.4313 21.3314 36.7572 21.3529C42.1971 21.3529 45.7386 18.6659 45.7806 14.5077C45.8013 12.226 44.4158 10.4777 41.4288 9.04899C39.6155 8.13188 38.505 7.51348 38.505 6.57511C38.5262 5.72205 39.4442 4.84829 41.4911 4.84829C43.1766 4.8055 44.4149 5.21049 45.353 5.61577L45.8219 5.82861L46.527 1.50038Z" fill="white" />
      <Path d="M53.4182 13.9533C53.8451 12.8018 55.488 8.34523 55.488 8.34523C55.4665 8.38803 55.9141 7.17248 56.1701 6.42627L56.5325 8.15337C56.5325 8.15337 57.5142 12.9512 57.7274 13.9533C56.9172 13.9533 54.4422 13.9533 53.4182 13.9533ZM59.8177 1.01031H55.8075C54.5709 1.01031 53.6314 1.37251 53.0978 2.67336L45.397 21.0539H50.8369C50.8369 21.0539 51.7325 18.58 51.925 18.0472C52.5217 18.0472 57.8136 18.0472 58.5814 18.0472C58.7302 18.7509 59.2 21.0539 59.2 21.0539H64.0003L59.8177 1.01031Z" fill="white" />
      <Path d="M18.2182 1.01031L13.1409 14.6783L12.5861 11.9063C11.6474 8.70771 8.70346 5.23255 5.41821 3.5046L10.0688 21.0329H15.5512L23.7003 1.01031H18.2182Z" fill="white" />
      <Path d="M8.42661 1.01031H0.0853345L0 1.41531C6.50671 3.07864 10.816 7.08803 12.5865 11.9071L10.7732 2.69518C10.4746 1.41502 9.55722 1.05254 8.42661 1.01031Z" fill="white" />
    </Svg>
  );
};

export default VisaIcon;
