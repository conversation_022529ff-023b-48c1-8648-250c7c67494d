import React from 'react';

import GoBack from '../components/organisms/Buttons/GoBackButton';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { CountryScreen } from '../screens/StackCreateAccount/CountryScreen';
import Logo from '../components/atoms/Icons/Logo';
import { NameScreen } from '../screens/StackCreateAccount/NameScreen';
import { VerifyCode } from '../screens/StackCreateAccount/VerifyCode';
import EmailScreen from '../screens/StackCreateAccount/EmailScreen';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<any>();

function ArrowBack() {
  return <GoBack />;
}

function LogoToshify() {
  return <Logo height={24} width={120} />;
}

export default function StackCreateAccount() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: LogoToshify,
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          headerTitleAlign: 'center',
        }}
        name={'CountryScreen'}
        component={CountryScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: LogoToshify,
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          headerTitleAlign: 'center',
        }}
        name={'NameScreen'}
        component={NameScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: LogoToshify,
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          headerTitleAlign: 'center',
        }}
        name={'EmailScreen'}
        component={EmailScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: LogoToshify,
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          headerTitleAlign: 'center',
        }}
        name={'VerifyScreen'}
        component={VerifyCode}
      />
    </Stack.Navigator>
  );
}
