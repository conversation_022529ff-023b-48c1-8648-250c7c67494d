import { USER_ID } from '../constants/asyncStorageKeys';
import { TransactionsResponse } from '../types/Transfer';
import { api } from './apiService';
import { getData } from './asyncStorageService';

export const getTransactions = async (size: number) => {
  try {
    const userId = await getData(USER_ID);

    const response = await api.get<TransactionsResponse>(
      `/digital/transactions?page=1&size=${size}&userId=${userId}`,
    );
    const transfers = response.data.data.list;
    return transfers;
  } catch (error: any) {
    console.error('Error fetching balance:', error.response);
  }
};

export const getTransactionById = async (id: number) => {
  try {
    const response = await api.get(`/transactions/${id}/details`);
    return response.data.data;
  } catch (error: any) {
    console.error('Error fetching transaction:', error.response);
  }
};
