import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  modalView: {
    backgroundColor: 'white',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    padding: 16,
    marginTop: 'auto',
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
  },
  mt24: {
    marginVertical: 24,
  },
  containerItem: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    paddingHorizontal: 12,
    paddingVertical: 16,
    marginBottom: 8,
  },
  label: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 12,
    color: color.NEUTRALS_800,
  },
});
