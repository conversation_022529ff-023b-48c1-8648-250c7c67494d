import React from 'react';
import Svg, { Path, Rect } from 'react-native-svg';

const TransferButtonToshi = () => (
  <Svg width="49" height="48" viewBox="0 0 49 48" fill="none">
    <Rect x="0.416016" width="48" height="48" rx="16" fill="#E0EDFF" />
    <Path
      d="M35.0971 20.8896H34.989C33.6463 20.8896 32.4448 20.1217 31.8378 18.942C30.543 16.4252 27.8922 14.7004 24.8322 14.7004C21.7723 14.7004 19.1215 16.4252 17.8266 18.942C17.2196 20.1217 16.0183 20.8896 14.6756 20.8896H14.5675C11.8763 20.8896 9.67432 23.059 9.67432 25.7101C9.67432 28.3616 11.8763 30.5308 14.5675 30.5308C15.1893 30.5308 15.7527 30.9211 15.9331 31.5074C16.3579 32.8875 17.6594 33.8923 19.1991 33.8923C20.545 33.8923 21.7091 33.1244 22.2643 32.0091C22.7233 31.0872 23.7054 30.5308 24.7478 30.5308H24.8322H24.9168C25.959 30.5308 26.9412 31.0872 27.4003 32.0091C27.9555 33.1244 29.1194 33.8923 30.4655 33.8923C32.0052 33.8923 33.3066 32.8875 33.7313 31.5074C33.9118 30.9211 34.4753 30.5308 35.0971 30.5308C37.7883 30.5308 39.9901 28.3616 39.9901 25.7101C39.9901 23.059 37.7883 20.8896 35.0971 20.8896Z"
      fill="#0068FF"
    />
    <Path
      d="M20.3203 24.0082C21.5161 24.8851 23.2335 25.2924 24.834 25.2924C26.6501 25.2924 28.2955 24.7788 29.3474 24.0082V25.0534C28.1876 26.1519 26.4613 26.6746 24.834 26.6746C22.9907 26.6746 21.3451 26.0278 20.3203 25.0534V24.0082Z"
      fill="white"
    />
    <Path
      d="M26.4111 21.918C26.8608 21.918 27.2294 22.2811 27.2294 22.7329C27.2294 23.167 26.8608 23.5479 26.4111 23.5479C25.9706 23.5479 25.6018 23.167 25.6018 22.7329C25.6018 22.2811 25.9706 21.918 26.4111 21.918ZM23.2461 21.918C23.6958 21.918 24.0645 22.2811 24.0645 22.7329C24.0645 23.167 23.6958 23.5479 23.2461 23.5479C22.8056 23.5479 22.437 23.167 22.437 22.7329C22.437 22.2811 22.8056 21.918 23.2461 21.918Z"
      fill="white"
    />
  </Svg>
);

export default TransferButtonToshi;
