import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const ChangePinIcon: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M1.66671 13.3333H18.3334V15H1.66671V13.3333ZM2.62504 9.95833L3.33337 8.725L4.04171 9.95833L5.12504 9.33333L4.41671 8.1H5.83337V6.85H4.41671L5.12504 5.625L4.04171 5L3.33337 6.225L2.62504 5L1.54171 5.625L2.25004 6.85H0.833374V8.1H2.25004L1.54171 9.33333L2.62504 9.95833ZM8.20837 9.33333L9.29171 9.95833L10 8.725L10.7084 9.95833L11.7917 9.33333L11.0834 8.1H12.5V6.85H11.0834L11.7917 5.625L10.7084 5L10 6.225L9.29171 5L8.20837 5.625L8.91671 6.85H7.50004V8.1H8.91671L8.20837 9.33333ZM19.1667 6.85H17.75L18.4584 5.625L17.375 5L16.6667 6.225L15.9584 5L14.875 5.625L15.5834 6.85H14.1667V8.1H15.5834L14.875 9.33333L15.9584 9.95833L16.6667 8.725L17.375 9.95833L18.4584 9.33333L17.75 8.1H19.1667V6.85Z"
        fill={color}
      />
    </Svg>
  );
};

export default ChangePinIcon;
