import { useState, useCallback, useEffect } from 'react';

type PasswordValidation = {
  length: boolean;
  uppercase: boolean;
  lowercase: boolean;
  number: boolean;
  specialChar: boolean;
};

export const usePasswordValidation = () => {
  const [password, setPassword] = useState('');
  const [repeatPassword, setRepeatPassword] = useState('');
  const [passwordValidation, setPasswordValidation] =
    useState<PasswordValidation>({
      length: false,
      uppercase: false,
      lowercase: false,
      number: false,
      specialChar: false,
    });
  const [passwordMatch, setPasswordMatch] = useState(true);
  const [passwordValid, setPasswordValid] = useState(false);

  const validatePassword = useCallback((password: string) => {
    const length = /.{6,20}/.test(password);
    const uppercase = /[A-Z]/.test(password);
    const lowercase = /[a-z]/.test(password);
    const number = /[0-9]/.test(password);
    const specialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const isValid = length && uppercase && lowercase && number && specialChar;
    setPasswordValidation({
      length,
      uppercase,
      lowercase,
      number,
      specialChar,
    });
    setPasswordValid(isValid);
    return isValid;
  }, []);

  const handlePasswordChange = (newPassword: string) => {
    console.log(newPassword);
    setPassword(newPassword);
    validatePassword(newPassword);
  };

  const handleRepeatPasswordChange = (newRepeatPassword: string) => {
    setRepeatPassword(newRepeatPassword);
  };

  useEffect(() => {
    setPasswordMatch(password === repeatPassword);
  }, [password, repeatPassword]);

  return {
    password,
    repeatPassword,
    passwordValidation,
    passwordMatch,
    passwordValid,
    handlePasswordChange,
    handleRepeatPasswordChange,
  };
};
