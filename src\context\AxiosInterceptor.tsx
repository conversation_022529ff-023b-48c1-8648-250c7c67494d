import { useContext, useEffect } from 'react';

import { AuthContext } from '../context/AuthContext';
import { setAxiosInterceptorRequest } from '../services/apiService';

interface Props {
  children: JSX.Element;
}

export default function AxiosInterceptor({ children }: Props) {
  const { signOut, checkBiometricsAndSignOut } = useContext(AuthContext);

  useEffect(() => {
    setAxiosInterceptorRequest(signOut, checkBiometricsAndSignOut);
  }, [signOut, checkBiometricsAndSignOut]);
  return children;
}
