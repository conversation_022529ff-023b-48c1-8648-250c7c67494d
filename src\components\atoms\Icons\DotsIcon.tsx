import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const DotsIcon: FC<IconProps> = () => {
  return (
    <Svg width="4" height="12" viewBox="0 0 4 12" fill="none">
      <Path
        d="M2.00001 3.33341C2.73334 3.33341 3.33334 2.73341 3.33334 2.00008C3.33334 1.26675 2.73334 0.666748 2.00001 0.666748C1.26667 0.666748 0.666672 1.26675 0.666672 2.00008C0.666672 2.73341 1.26667 3.33341 2.00001 3.33341ZM2.00001 4.66675C1.26667 4.66675 0.666672 5.26675 0.666672 6.00008C0.666672 6.73341 1.26667 7.33341 2.00001 7.33341C2.73334 7.33341 3.33334 6.73341 3.33334 6.00008C3.33334 5.26675 2.73334 4.66675 2.00001 4.66675ZM2.00001 8.66675C1.26667 8.66675 0.666672 9.26675 0.666672 10.0001C0.666672 10.7334 1.26667 11.3334 2.00001 11.3334C2.73334 11.3334 3.33334 10.7334 3.33334 10.0001C3.33334 9.26675 2.73334 8.66675 2.00001 8.66675Z"
        fill="#535D66"
      />
    </Svg>
  );
};

export default DotsIcon;
