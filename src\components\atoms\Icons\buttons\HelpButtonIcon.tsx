import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const HelpButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#FFECBA" />

      <Path
        d="M16 24.3333C11.3975 24.3333 7.66666 20.6025 7.66666 16C7.66666 11.3975 11.3975 7.66666 16 7.66666C20.6025 7.66666 24.3333 11.3975 24.3333 16C24.3333 20.6025 20.6025 24.3333 16 24.3333ZM16 22.6667C17.7681 22.6667 19.4638 21.9643 20.714 20.714C21.9643 19.4638 22.6667 17.7681 22.6667 16C22.6667 14.2319 21.9643 12.5362 20.714 11.2859C19.4638 10.0357 17.7681 9.33332 16 9.33332C14.2319 9.33332 12.5362 10.0357 11.2859 11.2859C10.0357 12.5362 9.33332 14.2319 9.33332 16C9.33332 17.7681 10.0357 19.4638 11.2859 20.714C12.5362 21.9643 14.2319 22.6667 16 22.6667ZM15.1667 18.5H16.8333V20.1667H15.1667V18.5ZM16.8333 17.1292V17.6667H15.1667V16.4167C15.1667 16.1956 15.2545 15.9837 15.4107 15.8274C15.567 15.6711 15.779 15.5833 16 15.5833C16.2367 15.5833 16.4686 15.5161 16.6686 15.3894C16.8686 15.2628 17.0285 15.082 17.1298 14.868C17.2311 14.654 17.2695 14.4157 17.2406 14.1807C17.2117 13.9457 17.1166 13.7238 16.9665 13.5407C16.8164 13.3577 16.6174 13.221 16.3927 13.1466C16.1679 13.0722 15.9267 13.0632 15.697 13.1206C15.4673 13.1779 15.2587 13.2993 15.0953 13.4707C14.9319 13.642 14.8206 13.8562 14.7742 14.0883L13.1392 13.7608C13.2405 13.2542 13.4746 12.7836 13.8174 12.3971C14.1603 12.0107 14.5996 11.7221 15.0905 11.5611C15.5814 11.4 16.1062 11.3722 16.6114 11.4805C17.1166 11.5888 17.5839 11.8293 17.9657 12.1773C18.3474 12.5254 18.6299 12.9687 18.7843 13.4617C18.9386 13.9548 18.9592 14.4799 18.844 14.9836C18.7289 15.4872 18.482 15.9512 18.1288 16.3282C17.7755 16.7052 17.3284 16.9816 16.8333 17.1292Z"
        fill="#875202"
      />
    </Svg>
  );
};

export default HelpButtonIcon;
