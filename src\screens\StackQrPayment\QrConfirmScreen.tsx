import React, { useContext, useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { InfoAccount } from '../../components/organisms/Cards/InfoAccount';
import HeaderTransfer from '../../components/organisms/Headers/Transfer';
import { colors } from '../../constants/colors';
import ButtonWithIcon from '../../components/atoms/ButtonWithIcon';
import SecureIcon from '../../components/atoms/Icons/SecureIcon';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { LoaderModal } from '../../components/organisms/Modals/Loader';
import { api } from '../../services/apiService';
import { formatCurrency } from '../../helpers/formatCurrency';
import { AuthContext } from '../../context/AuthContext';
import { useBiometrics } from '../../hooks/useBiometrics';
import { storage } from '../../lib/mmkv';
import { promptBiometrics } from '../../lib/biometrics';
import color from '../../theme/pallets/pallet';
type Props = NativeStackScreenProps<any>;

export const QrConfirmScreen = ({ navigation, route }: Props) => {
  const { userData, disableScreenLock } = useContext(AuthContext);
  disableScreenLock();
  const { nombre, amount, cuit, cvu, balance }: any = route.params;
  const [showModal, setShowModal] = useState(false);
  const { available } = useBiometrics();
  const unlockMethodType = storage.getString('unlockMethodType');
  const handleTransfer = async () => {
    if (
      (available && unlockMethodType === 'Biometrics') ||
      unlockMethodType === 'FaceID' ||
      unlockMethodType === 'TouchID'
    ) {
      const success = await promptBiometrics(unlockMethodType);
      if (!success) {
        setShowModal(false);
        return;
      }
    }
    try {
      setShowModal(true);
      const response = await api.post('/transactions/cashout', {
        userId: userData,
        cuit,
        cvu,
        name: nombre,
        importe: Number(amount),
        trxSubject: 'Varios',
        trxType: 'QRPAYMENT',
      });
      if (response.status === 200) {
        setShowModal(false);
        navigation.navigate('QrSuccess', {
          amount,
          nombre,
          numTransaccion: response.data.data.senderTransactionId,
          cuit,
          cvu,
          trxSubject: 'Varios',
        });
      }
    } catch (error) {
      navigation.navigate('QrError');
      setShowModal(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <HeaderTransfer text="Confirmar pago" />
        <InfoAccount nombre={nombre} />
        <Text style={styles.txt}>Total a pagar</Text>
        <Text style={styles.textInput}>${formatCurrency(Number(amount))}</Text>
        <View style={styles.infoContainer}>
          <Text style={styles.labelCard}>Medio de pago</Text>
          <View style={styles.card}>
            <Text style={styles.label}>De mi billetera Toshify</Text>
            <Text style={styles.txt}>
              Saldo disponible: ${formatCurrency(balance)}
            </Text>
          </View>

          <Text style={styles.labelCard}>Tiempo de transacción</Text>
          <View style={styles.card}>
            <Text style={styles.motive}>Inmediata</Text>
          </View>
        </View>
      </View>
      <LoaderModal modalVisible={showModal} />

      <View style={styles.button}>
        <ButtonWithIcon
          bgColor="#FDC228"
          color="#0D3674"
          icon={<SecureIcon size={20} />}
          text="Pagar ahora"
          onPress={handleTransfer}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  button: {
    marginVertical: 32,
    width: '100%',
  },
  textInput: {
    fontSize: 36,
    color: color.TEXT_PRIMARY,
    textAlign: 'center',
  },
  label: {
    fontSize: 12,
    fontFamily: 'Satoshi-Bold',
    color: color.NEUTRALS_800,
  },
  txt: {
    fontSize: 12,
    fontFamily: 'Satoshi-Regular',
    color: '#78838D',
  },
  infoContainer: {
    alignSelf: 'flex-start',
    marginTop: 54,
    width: '100%',
  },
  labelCard: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 12,
    color: color.TEXT_PRIMARY,
  },
  card: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    borderRadius: 8,
    marginTop: 4,
    marginBottom: 24,
  },
  motive: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
});
