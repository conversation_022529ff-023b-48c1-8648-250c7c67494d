import React, { FC, useContext, useEffect, useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import moment from 'moment';
import 'moment/locale/es';
import { styles } from './styles';
import { ArrowRight } from '../../atoms/Icons';
import { formatCurrency } from '../../../helpers/formatCurrency';
import EnviarTransferencia from '../../atoms/Icons/EnviarTransferencia';
import RecibirTransferencia from '../../atoms/Icons/RecibirTransferencia';
import { InfoMovementModal } from '../../organisms/Modals/InfoMovement';
import { Transactions } from '../../../types/Transfer';
import QrPayment from '../../atoms/Icons/QrPayment';
import { AuthContext } from '../../../context/AuthContext';
import color from '../../../theme/pallets/pallet';
import { Text } from '../../atoms';

type TransferItemProps = {
  transfer: Transactions;
};

const TransferItem: FC<TransferItemProps> = ({ transfer }) => {
  const [isModalVisible, setModalVisible] = useState(false);
  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const { isInBackground } = useContext(AuthContext);
  useEffect(() => {
    isInBackground && setModalVisible(false);
  }, [isInBackground]);

  return (
    <View style={styles.separator}>
      <TouchableOpacity onPress={toggleModal}>
        <View style={styles.container}>
          <View style={styles.containerRow}>
            <View style={styles.flexRow}>
              <View style={styles.mr8}>
                {transfer?.description?.toLowerCase()?.includes('pago qr') ? (
                  <QrPayment />
                ) : transfer.amount > 0 ? (
                  <RecibirTransferencia />
                ) : (
                  <EnviarTransferencia />
                )}
              </View>
              <View>
                <Text style={styles.description} numberOfLines={2}>
                  {transfer?.description?.toLowerCase()?.includes('pago qr') ? (
                    <Text variant="B7">
                      Pago QR a {transfer.counterpartName}
                    </Text>
                  ) : transfer.amount > 0 ? (
                    <Text variant="B7">Transferencia recibida</Text>
                  ) : (
                    <Text variant="B7">Transferencia enviada</Text>
                  )}
                </Text>
                <View>
                  <Text variant="R7" color={'NEUTRALS_600'}>
                    {moment(transfer.smImpactedOn).format('MMM D')},{' '}
                    {moment(transfer.smImpactedOn).format('HH:mm')}
                  </Text>
                </View>
              </View>
            </View>
          </View>
          <View style={styles.row}>
            {transfer.amount > 0 ? (
              <Text variant="R7" color="GREEN_700">
                +${formatCurrency(transfer.amount)}
              </Text>
            ) : (
              <Text variant="R7" color="RED_700">
                -${formatCurrency(Number(transfer.amount.toString().slice(1)))}
              </Text>
            )}
            <View style={styles.icon}>
              <ArrowRight size={16} color={color.NEUTRALS_800} />
            </View>
          </View>
        </View>
      </TouchableOpacity>
      <InfoMovementModal
        modalVisible={isModalVisible}
        setModalVisible={setModalVisible}
        accountData={transfer}
        date={
          moment(transfer.smImpactedOn)
            .format('MMMM DD, HH:mm')
            .charAt(0)
            .toUpperCase() +
          moment(transfer.smImpactedOn).format('MMMM DD, HH:mm').slice(1)
        }
        amountNegative={formatCurrency(
          Number(transfer.amount.toString().slice(1)),
        )}
        amountPositive={formatCurrency(transfer.amount)}
      />
    </View>
  );
};

export default TransferItem;
