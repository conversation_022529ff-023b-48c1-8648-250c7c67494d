const createPDF = (
    operationId: number,
    date: any,
    amount: number,
    motive: string,
    from: string,
    fromCuit: number,
    fromCVU: number,
    toName: string,
    toCuit: number,
    toCvu: number,
    idCoelsa: any,
    description: string,
) => `
<style>
@font-face {
    font-family: 'Sora';
    src: local('Sora'), url('Satoshi-Regular.ttf') format('truetype');
}

body {
    font-family: 'Sora', sans-serif;
    /* Otros estilos de body si es necesario */
}

/* Otros estilos adicionales según tus necesidades */
</style>
    <div style="width: 100%; height: 100%; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: inline-flex">
        <div style="width: 328px; justify-content: flex-start; align-items: center; gap: 73px; display: inline-flex">
            <div style="border-radius: 4px; justify-content: center; align-items: center; gap: 2px; display: flex">
            </div>
        </div>
        <div style="color: black; font-size: 18px; font-family: Sora; font-weight: 400; line-height: 27px; word-wrap: break-word">Comprobante de operación</div>
        <div style="align-self: stretch; height: 18px; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
            <div style="align-self: stretch; justify-content: space-between; align-items: flex-start; display: inline-flex">
                <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">${description}</div>
                <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">${date}</div>
            </div>
        </div>
        <div style="align-self: stretch; height: 72px; flex-direction: column; justify-content: flex-start; align-items: flex-start; display: flex">
            <div style="text-align: center; color: #191919; font-size: 36px; font-family: Sora; font-weight: 400; line-height: 54px; word-wrap: break-word">$${amount}</div>
            <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">Motivo: ${motive}</div>
        </div>
        <div style="align-self: stretch; height: 137px; padding-left: 16px; padding-right: 16px; padding-top: 8px; padding-bottom: 8px; background: #F7F8FE; border-radius: 8px; border-left: 2px #FDC228 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
            <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
                <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">De</div>
                <div style="color: #191919; font-size: 14px; font-family: Sora; font-weight: 600; line-height: 21px; word-wrap: break-word">${from}</div>
                <div style="justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
                    <div style="color: #BAC2C7; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">CUIT/CUIL:</div>
                    <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">${fromCuit}</div>
                </div>
            </div>
            <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
                <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 600; line-height: 18px; word-wrap: break-word">Billetera Toshify</div>
                <div style="justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
                    <div style="color: #BAC2C7; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">CVU</div>
                    <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">${fromCVU}</div>
                </div>
            </div>
        </div>
        <div style="align-self: stretch; height: 103px; padding-left: 16px; padding-right: 16px; padding-top: 8px; padding-bottom: 8px; background: #F7F8FE; border-radius: 8px; border-left: 2px #FDC228 solid; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px; display: flex">
            <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 4px; display: flex">
                <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">Para</div>
                <div style="color: #191919; font-size: 14px; font-family: Sora; font-weight: 600; line-height: 21px; word-wrap: break-word">${toName}</div>
                <div style="justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
                    <div style="color: #BAC2C7; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">CUIT/CUIL:</div>
                    <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">${toCuit}</div>
                </div>
                <div style="justify-content: flex-start; align-items: flex-start; gap: 4px; display: inline-flex">
                    <div style="color: #BAC2C7; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">CVU</div>
                    <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">${toCvu}</div>
                </div>
            </div>
        </div>
        <div style="align-self: stretch; border-radius: 8px; justify-content: space-between; align-items: center; display: inline-flex; flex-direction: column;">
        <div style="align-self: stretch; border-radius: 8px; justify-content: space-between; align-items: center; display: inline-flex">
        <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
            <div style="color: #78838D; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">Código de identificación</div>
            <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 600; line-height: 18px; word-wrap: break-word">${operationId}</div>
        </div>
    </div>
        ${idCoelsa
        ? `
        <div style="align-self: stretch; border-radius: 8px; justify-content: space-between; align-items: center; display: inline-flex; margin-top: 10px">
            <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                <div style="color: #78838D; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">ID Coelsa</div>
                <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 600; line-height: 18px; word-wrap: break-word">${idCoelsa}</div>
            </div>
        </div>
        `
        : `
            <div style="align-self: stretch; border-radius: 8px; justify-content: space-between; align-items: center; display: inline-flex">
                <div style="flex-direction: column; justify-content: flex-start; align-items: flex-start; display: inline-flex">
                    <div style="color: #78838D; font-size: 12px; font-family: Sora; font-weight: 400; line-height: 18px; word-wrap: break-word">ID Coelsa</div>
                    <div style="color: #535D66; font-size: 12px; font-family: Sora; font-weight: 600; line-height: 18px; word-wrap: break-word">No aplica</div>
                </div>
            </div>
            `
    }
    </div>
    `;

export default createPDF;
