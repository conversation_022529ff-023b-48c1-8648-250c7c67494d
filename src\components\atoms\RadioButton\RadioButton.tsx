import React from 'react';
import { View, Text, Pressable, StyleSheet } from 'react-native';
import TextBase from '../TextBase';

interface RadioButtonProps {
  value: any;
  label: string;
  description?: string;
  selected: boolean;
  onPress: (value: any) => void;
}

const RadioButton: React.FC<RadioButtonProps> = ({
  value,
  label,
  description,
  selected,
  onPress,
}) => {
  const handleOnPress = (value: any) => {
    onPress(value);
  };

  return (
    <Pressable onPress={() => handleOnPress(value)}>
      <View style={styles.wrap}>
        <Dot selected={selected} />
        <View>
          <TextBase size='s'>{label}</TextBase>
          {description && <Text style={styles.description}>{description}</Text>}
        </View>
      </View>
    </Pressable>
  );
};

interface DotProps {
  selected: boolean;
}

const Dot: React.FC<DotProps> = ({ selected }) => {
  return (
    <View style={styles.radio}>
      <View
        style={{
          ...styles.dot,
          backgroundColor: selected ? '#FF0033' : 'transparent',
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  wrap: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 5,
    gap: 10,
  },
  radio: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#FF0033',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'transparent',
  },
  description: {
    fontSize: 16,
    color: '#EEE',
    fontFamily: 'NUNITO_MEDIUM', // APP-FONT
  },
});

export default RadioButton;
