import React, { FC, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import TextBase from '../../components/atoms/TextBase';
import PrepaidCardFloating from '../../components/atoms/Icons/Cards/PrepaidCardFloating';
import CardIcon from '../../components/atoms/Icons/CardIcon';
import ButtonWithIcon from '../../components/atoms/ButtonWithIcon';
import CheckBox from '@react-native-community/checkbox';
import color from '../../theme/pallets/pallet';

const CardShippingCost = '$ 1.999';

const RequestCardTermsAndConditions: FC<any> = ({ route, navigation }) => {
  const { virtualCardSelected, physicalCardSelected, address } = route.params;
  const [addsPhysicalCard, setAddsPhysicalCard] =
    useState(physicalCardSelected);
  const [acceptTerms, setAcceptTerms] = useState(false);

  const navigateToRequestCardScreen = () => {
    navigation.navigate('StackCards', {
      screen: 'LoadingRequestCardScreen',
      params: {
        virtualCardSelected,
        addsPhysicalCard,
        address,
      },
    });
  };

  return (
    <View style={styles.container}>
      <View>
        <TextBase style={styles.title}>Tarjeta Prepaga</TextBase>
      </View>

      <View style={{ flex: 1, marginTop: 40, gap: 6 }}>
        <View style={{ alignItems: 'center', marginBottom: 10 }}>
          <PrepaidCardFloating />
        </View>
        <TextBase style={styles.text}>Estás por solicitar</TextBase>

        <TextBase type="Bold" size="xxl" style={{ textAlign: 'center' }}>
          {virtualCardSelected && !addsPhysicalCard && 'Tarjeta Virtual'}
          {addsPhysicalCard && 'Tarjeta Virtual y Física'}
        </TextBase>
        <TextBase style={styles.text}>Utiliza el dinero de tu cuenta</TextBase>
      </View>

      <View style={styles.divider} />
      {!addsPhysicalCard && (
        <View style={{ gap: 10, marginVertical: 2 }}>
          <View
            style={{ flexDirection: 'row', justifyContent: 'space-between' }}
          >
            <TextBase>Tarjeta física</TextBase>
            <TouchableOpacity onPress={() => setAddsPhysicalCard(true)}>
              <TextBase type="Bold" color={color.PRIMARY_700}>
                Agregar
              </TextBase>
            </TouchableOpacity>
          </View>
          <TextBase size="l">
            Podés agregarla ahora o solicitarla más adelante.
          </TextBase>
        </View>
      )}
      {addsPhysicalCard && (
        <>
          <View style={{ gap: 10, marginVertical: 2 }}>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}
            >
              <TextBase color={color.NEUTRALS_600}>
                Enviaremos la tarjeta a
              </TextBase>
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <TextBase type="Bold" color={color.PRIMARY_700}>
                  Editar
                </TextBase>
              </TouchableOpacity>
            </View>
            <TextBase size="l">
              {address.streetName} {address.streetNumber}
              {address.floor && ` Piso ${address.floor}`}
              {address.apartment && ` Depto ${address.apartment}`}, C.P.{' '}
              {address.zipCode}
              {address.neighborhood && `, ${address.neighborhood}`}
              {address.city && `, ${address.city}`}
              {address.region && `, ${address.region}`}
            </TextBase>
          </View>

          <View style={styles.divider} />

          <View style={{ gap: 10, marginVertical: 2 }}>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}
            >
              <TextBase>Costo de emisión y envío</TextBase>
              <TextBase type="Bold">{CardShippingCost}</TextBase>
            </View>
            <TextBase size="s" color={color.NEUTRALS_600}>
              Se cobrará del saldo disponible en tu cuenta
            </TextBase>
          </View>
        </>
      )}
      <View style={styles.divider} />

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          gap: 8,
          marginVertical: 16,
        }}
      >
        <CheckBox
          value={acceptTerms}
          tintColors={{ true: color.PRIMARY_700, false: color.NEUTRALS_800 }}
          onFillColor={color.PRIMARY_700}
          tintColor="#535D66"
          onChange={() => setAcceptTerms(!acceptTerms)}
        />
        <View>
          <TextBase>
            Acepto{' '}
            <TextBase
              color={color.PRIMARY_700}
              style={{ textDecorationLine: 'underline' }}
            >
              términos generales
            </TextBase>{' '}
            y las{' '}
            <TextBase
              color={color.PRIMARY_700}
              style={{ textDecorationLine: 'underline' }}
            >
              condiciones específicas
            </TextBase>{' '}
            de la solicitud.
          </TextBase>
        </View>
      </View>

      <View style={styles.buttonsContainer}>
        <ButtonWithIcon
          bgColor={color.PRIMARY_700}
          color={color.WHITE}
          icon={<CardIcon size={20} color={color.WHITE} />}
          text="Solicitar Tarjeta"
          onPress={navigateToRequestCardScreen}
          disabled={!acceptTerms}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    height: '100%',
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
  },
  buttonsContainer: {
    width: '100%',
    paddingVertical: 20,
  },
  text: {
    lineHeight: 20,
    letterSpacing: 0.17,
    textAlign: 'center',
  },
  divider: {
    borderBottomColor: '#EDEFF6',
    borderBottomWidth: 1,
    marginVertical: 16,
  },
});

export default RequestCardTermsAndConditions;
