import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const QrIcon: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 12 13">
      <Path
        d="M9.16667 9.33333V8.66667H7.16667V6.66667H9.16667V8H10.5V9.33333H9.83333V10.6667H8.5V12H7.16667V10H8.5V9.33333H9.16667ZM12.5 12H9.83333V10.6667H11.1667V9.33333H12.5V12ZM0.5 0H5.83333V5.33333H0.5V0ZM1.83333 1.33333V4H4.5V1.33333H1.83333ZM7.16667 0H12.5V5.33333H7.16667V0ZM8.5 1.33333V4H11.1667V1.33333H8.5ZM0.5 6.66667H5.83333V12H0.5V6.66667ZM1.83333 8V10.6667H4.5V8H1.83333ZM10.5 6.66667H12.5V8H10.5V6.66667ZM2.5 2H3.83333V3.33333H2.5V2ZM2.5 8.66667H3.83333V10H2.5V8.66667ZM9.16667 2H10.5V3.33333H9.16667V2Z"
        fill={color}
      />
    </Svg>
  );
};

export default QrIcon;
