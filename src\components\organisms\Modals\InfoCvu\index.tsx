import React from 'react';
import { Modal, Pressable, Text, View, Share } from 'react-native';
import { styles } from './styles';
import CopyIcon from '../../../atoms/Icons/CopyIcon';
import ButtonWithIcon from '../../../atoms/ButtonWithIcon';
import ShareIcon from '../../../atoms/Icons/ShareIcon';
import { useCopyCustomToast } from '../../../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';
import color from '../../../../theme/pallets/pallet';

type ItemProps = {
  label: string;
  value: string;
  icon?: JSX.Element;
};

const Item = ({ label, value, icon }: ItemProps) => {
  const { showToast, toastConfig } = useCopyCustomToast();

  return (
    <View style={styles.containerItem}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.flexRow}>
        <Text style={styles.value}>{value}</Text>
        {icon && <Pressable onPress={() => showToast(value)}>{icon}</Pressable>}
      </View>
      <Toast config={toastConfig} />
    </View>
  );
};

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  user?: any;
  cvuUser: any;
  cuitUser: any;
};

export const InfoCvuModal = ({
  modalVisible,
  setModalVisible,
  user,
  cvuUser,
  cuitUser,
}: Props) => {
  const Items = [
    {
      label: 'Tu alias',
      value: cvuUser ? cvuUser[0]?.cvuAlias : '',
      icon: <CopyIcon size={24} color={color.PRIMARY_500} />,
    },
    {
      label: 'Tu CVU',
      value: cvuUser ? cvuUser[0]?.cvu : '',
      icon: <CopyIcon size={24} color={color.PRIMARY_500} />,
    },
  ];

  const handleShareData = async () => {
    let textToShare = '';
    if (user) {
      const titular = user.nombre + ' ' + user.apellido;
      const cvu = cvuUser ? cvuUser[0]?.cvu : '';
      const alias = cvuUser ? cvuUser[0]?.cvuAlias : '';
      const cuit = cuitUser ? cuitUser : '';
      const banco = 'Banco Coinag';
      textToShare = `${titular}\nCVU:${cvu}\nAlias:${alias}\nCUIT/CUIL:${cuit}\n${banco}`;
    } else {
      textToShare = 'No se han encontrado datos para compartir.';
    }

    await Share.share({
      message: textToShare,
    })
      .then(() => console.log('Shared successfully'))
      .catch(error => console.error('Error sharing:', error));
    setModalVisible(false);
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <View style={styles.overlay}>
        <View style={styles.modalView}>
          <View>
            <View style={styles.containerAccount}>
              <View style={styles.account}>
                <Text style={styles.name} numberOfLines={2}>
                  Datos de tu cuenta
                </Text>
              </View>
              <Pressable
                onPress={() => setModalVisible(false)}
                style={styles.close}
              >
                <Text style={styles.textClose}>Listo</Text>
              </Pressable>
            </View>
            {Items.map((item, i) => (
              <Item
                label={item.label}
                value={item.value!}
                key={i}
                icon={item.icon}
              />
            ))}
          </View>
          <View style={styles.mt24}>
            <ButtonWithIcon
              text="Compartir datos"
              icon={<ShareIcon width={20} height={20} />}
              bgColor="#FF0033"
              color="#FFFFFF"
              onPress={handleShareData}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};
