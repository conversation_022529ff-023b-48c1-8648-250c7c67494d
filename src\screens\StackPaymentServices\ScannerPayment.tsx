import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Keyboard,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
  TouchableWithoutFeedback,
  Text,
} from 'react-native';
import { TextBase } from '../../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { isEmpty } from 'lodash';
import { useUserContext } from '../../context/UserContext';
import ButtonWithIcon from '../../components/atoms/ButtonWithIcon';
import CheckIcon from '../../components/atoms/Icons/CheckIcon';
import { ServiceInfo } from '../../components/organisms/Cards/ServiceInfo';

type Props = NativeStackScreenProps<any>;

export const ScannerPayment = ({ navigation, route }: Props) => {
  const { user } = useUserContext();

  const data = route.params;
  const error = route?.params?.error;
  const objeto = data ? JSON.parse(data[0].value) : null;

  const cuit = 'objeto.cuit';
  const cvu = 'objeto.cvu';
  const nombre = 'Claro';
  const importe = 'objeto.importe';

  const [amount, onChangeValue] = useState(importe);

  const inputRef = useRef<TextInput>(null);
  const [keyboardOpen, setKeyboardOpen] = useState(false);

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardOpen(true);
    });
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardOpen(false);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  const handleAmount = useCallback(
    (value: string) => {
      onChangeValue(value);
    },
    [onChangeValue],
  );

  const handleOnPress = () => {
    if (keyboardOpen) {
      inputRef.current?.blur();
    }
    inputRef.current?.focus();
  };

  const handleKeyPress = (
    event: NativeSyntheticEvent<TextInputKeyPressEventData>,
  ) => {
    const key = event.nativeEvent.key;

    let newValue = importe;
    const maxDecimals = 2;

    const sanitizedKey = key.replace(',', '.');
    const validKeyRegExp = /^[0-9.]$/;
    const decimalRegExp = /^\./;
    const validNumberRegExp = new RegExp(`^\\d+(\\.\\d{0,${maxDecimals}})?$`);

    if (key === 'Backspace' && newValue.length > 0) {
      newValue = newValue.slice(0, -1);
      handleAmount(newValue);
      return;
    }
    if (isEmpty(newValue) && decimalRegExp.test(newValue + sanitizedKey)) {
      newValue = '0' + sanitizedKey;
      handleAmount(newValue);
      return;
    }
    if (isEmpty(newValue) && sanitizedKey === '0') {
      newValue = sanitizedKey + '.';
      handleAmount(newValue);
      return;
    }

    if (!validKeyRegExp.test(sanitizedKey)) {
      return;
    }
    if (!validNumberRegExp.test(newValue + sanitizedKey)) {
      return;
    }

    newValue += sanitizedKey;

    handleAmount(newValue);
  };

  const keyboardDismiss = () => {
    Keyboard.dismiss();
  };

  const navigateToConfirm = () => {
    navigation.navigate('QrConfirm', {
      nombre,
      amount,
      cuit,
      cvu,
      balance: user.balance,
    });
  };

  //   if (
  //     !data ||
  //     !Array.isArray(data) ||
  //     data.length === 0 ||
  //     !data[0].value ||
  //     error
  //   ) {
  //     navigation.navigate('ErrorScreen');
  //   }

  return (
    <TouchableWithoutFeedback accessible={false} onPress={keyboardDismiss}>
      <View style={styles.container}>
        <TextBase size="xxl" type="Bold">
          Pagar servicio
        </TextBase>

        <View style={styles.header}>
          {/* <InfoAccount nombre={nombre} banco="Descripcion" /> */}
          <ServiceInfo nombre={nombre} type="Telefonía" />
          <Text style={styles.txt}>Pagar importe</Text>
          <TextBase size="xxxl">$ 7.846,62</TextBase>
          {/* <InputAmount
            amount={amount}
            handleOnPress={handleOnPress}
            inputRef={inputRef}
            handleKeyPress={handleKeyPress}
            error={insufficientFunds}
            editable={false}
          /> */}
          {/* <Text
            style={[
              styles.txt,
              { color: insufficientFunds ? color.RED_700 : '#78838D' },
            ]}
          >
            Saldo disponible: ${formatCurrency(user.balance)}
          </Text> */}
        </View>
        <View style={styles.button}>
          <ButtonWithIcon
            bgColor="#FF0033"
            text="Pagar ahora"
            onPress={() => navigation.navigate('Home')}
            disabled={false}
            icon={
              <CheckIcon width={20} height={20} size={20} color="#FFFFFF" />
            }
            color="#FFFFFF"
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: 22,
  },
  button: {
    marginVertical: 32,
    width: '100%',
  },
  txt: {
    fontSize: 12,
    fontFamily: 'Satoshi-Regular',
    color: '#78838D',
    marginVertical: 8,
  },
  errorText: {
    color: '#000',
  },
});
