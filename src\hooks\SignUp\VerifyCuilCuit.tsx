import { api } from '../../services/apiService';
import { useState } from 'react';

export const useVerifyCuilCuit = ({ setLoading, setError }: any) => {
  const [successData, setSuccessData] = useState<any>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [exists, setExists] = useState<boolean>(false);

  const verifyCuilCuit = async (cuit: any) => {
    try {
      setLoading(true);
      const response = await api.get(`/digital/signup/cuitVerify/${cuit}`);
      if (response.status === 200) {
        setSuccessData(response.data.exists);
        setSuccess(true);
        setLoading(false);
        setError(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
        setSuccess(false);
      }
      setLoading(false);
    } catch (err: any) {
      if (
        err.response.data.message
          .toLowerCase()
          .includes('cuit, ya se encuentra registrado')
      ) {
        setExists(true);
      }
      setSuccess(false);
      setError(true);
      setLoading(false);
    }
    setLoading(false);
  };

  return {
    verifyCuilCuit,
    successData,
    success,
    setSuccess,
    exists,
    setExists,
  };
};
