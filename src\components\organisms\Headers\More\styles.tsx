import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: color.WHITE,
    padding: 16,
    gap: 24,
    marginBottom: 16,
  },
  txt: {
    fontSize: 26,
    fontFamily: 'Satoshi-SemiBold',
    color: color.BLACK,
  },
  body: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-between',
    alignSelf: 'center',
    marginVertical: 8,
    alignItems: 'center',
  },
  perfil: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 8,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
    padding: 16,
    borderRadius: 8,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});
