import React, { useContext, useEffect } from 'react';
import { Auth, Root } from '.';
import { AuthContext } from '../context/AuthContext';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import SplashScreen from 'react-native-splash-screen';
import useAllow from '../hooks/useAllow';
import NotAllowed from '../screens/NotAllowed';

const Stack = createNativeStackNavigator();

const StackNavigator = () => {
  const { authenticated } = useContext(AuthContext);
  const { isAllowed, isLoadingAllowing } = useAllow();

  useEffect(() => {
    if (!isLoadingAllowing) {
      return;
    }
    SplashScreen.hide();
  }, [isLoadingAllowing]);

  if (!isAllowed) {
    return <NotAllowed />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Root" component={Root} />
      {authenticated && <Stack.Screen name="Auth" component={Auth} />}
    </Stack.Navigator>
  );
};

export default StackNavigator;
