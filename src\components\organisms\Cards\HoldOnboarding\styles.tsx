import { StyleSheet } from 'react-native';
import { colors } from '../../../../constants/colors';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.backgroundGrey,
    padding: 20,
    margin: 15,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
  },
  headerText: {
    marginTop: 10,
    color: color.BLACK,
    fontSize: 20,
    fontFamily: 'Satoshi-Bold',
  },
  descriptionText: {
    textAlign: 'center',
    marginVertical: 10,
    width: '90%',
    fontSize: 16,
    color: color.BLACK,
    fontWeight: '400',
    fontFamily: 'Satoshi-Regular',
  },
  rowStyle: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 10,
  },
  iconText: {
    color: color.BLACK,
    marginLeft: 10,
    fontFamily: 'Satoshi-Regular',
  },
  buttonStyle: {
    marginTop: 20,
    width: '100%',
  },
});
