/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { View, FlatList, TouchableOpacity } from 'react-native';
import { TextBase } from '../../../atoms';
import { styles } from './styles';
import { useNavigation } from '@react-navigation/native';
import { api } from '../../../../services/apiService';
import { LoaderModal } from '../../Modals/Loader';
import { Skeleton } from '../../../atoms/Skeleton';
import BankGo from '../../../atoms/Icons/BankGo';

const Service = ({ service, circle }: any) => {
  return (
    <View style={styles.circleContainer}>
      {service.id === '0' ? (
        <>
          {!circle && (
            <>
              <View style={styles.circle}>
                <TextBase style={styles.plus}>+</TextBase>
              </View>
              <TextBase size="s">{service.servicio}</TextBase>
            </>
          )}
        </>
      ) : (
        <View style={styles.card}>
          <View style={styles.row}>
            <BankGo />
            <View style={styles.expiration}>
              <TextBase size="xl" type="Bold">
                {service.servicio}
              </TextBase>
              <TextBase size="l">{service.fecha_vencimiento}</TextBase>
            </View>
          </View>
          <TextBase type="Bold" size="xl">
            $ {service.monto}
          </TextBase>
        </View>
      )}
    </View>
  );
};

const ExpirationsCard = ({ circle }: any) => {
  //   const navigation: any = useNavigation();
  //   const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const data = [
    {
      id: '1',
      servicio: 'Internet',
      monto: 137.591,
      fecha_vencimiento: '11/4',
    },
    {
      id: '2',
      servicio: 'Electricidad',
      monto: 7.846,
      fecha_vencimiento: '15/4',
    },
    {
      id: '3',
      servicio: 'Agua',
      monto: 7.846,
      fecha_vencimiento: '20/4',
    },
  ];

  //   const navigateToAmount = (accountDetails: Account[]) => {
  //     const selectedAccountData = accountDetails[0];
  //     setModalVisible(false);
  //     navigation.navigate('StackTransfer', {
  //       screen: 'AmountScreen',
  //       params: {
  //         nombre: selectedAccountData?.titulares[0].nombre,
  //         banco: '',
  //         cuit: selectedAccountData?.titulares[0].cuit,
  //         cvu: selectedAccountData?.cvu,
  //       },
  //     });
  //   };

  return (
    <View style={styles.container}>
      <LoaderModal modalVisible={loading} />
      <View style={styles.titleContainer}>
        <TextBase type="SemiBold" size="l">
          Próximos vencimientos
        </TextBase>
      </View>
      {loading ? (
        <Skeleton
          width={400}
          backgroundColor="#F7F8FE"
          highlightColor="#e8e9ed"
          height={60}
        />
      ) : (
        <FlatList
          data={data}
          keyExtractor={item => item.id}
          horizontal
          renderItem={({ item }) => (
            <Service
              service={item}
              circle={circle}
              //   validateAccount={validateAccount}
            />
          )}
          showsHorizontalScrollIndicator={false}
        />
      )}
    </View>
  );
};

export default ExpirationsCard;
