import Svg, { Path } from 'react-native-svg'
import { LoanIconTypeColor } from './types';
import { FC } from 'react';

const Wallet: FC<LoanIconTypeColor> = ({ size, iconColor }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20" fill="none">
      <Path d="M17.0834 6.06667V4.16667C17.0834 3.25 16.3334 2.5 15.4167 2.5H3.75004C2.82504 2.5 2.08337 3.25 2.08337 4.16667V15.8333C2.08337 16.75 2.82504 17.5 3.75004 17.5H15.4167C16.3334 17.5 17.0834 16.75 17.0834 15.8333V13.9333C17.575 13.6417 17.9167 13.1167 17.9167 12.5V7.5C17.9167 6.88333 17.575 6.35833 17.0834 6.06667ZM16.25 7.5V12.5H10.4167V7.5H16.25ZM3.75004 15.8333V4.16667H15.4167V5.83333H10.4167C9.50004 5.83333 8.75004 6.58333 8.75004 7.5V12.5C8.75004 13.4167 9.50004 14.1667 10.4167 14.1667H15.4167V15.8333H3.75004Z" fill={iconColor} />
      <Path d="M12.9167 11.25C13.6071 11.25 14.1667 10.6904 14.1667 10C14.1667 9.30964 13.6071 8.75 12.9167 8.75C12.2264 8.75 11.6667 9.30964 11.6667 10C11.6667 10.6904 12.2264 11.25 12.9167 11.25Z" fill={iconColor} />
    </Svg>
  );
};

export default Wallet;