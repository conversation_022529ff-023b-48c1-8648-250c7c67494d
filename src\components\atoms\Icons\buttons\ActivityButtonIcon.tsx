import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const ActivityButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#F7F8FE" />
      <Path
        d="M21.8333 8.5H10.1667C9.25 8.5 8.5 9.25 8.5 10.1667V21.8333C8.5 22.75 9.25 23.5 10.1667 23.5H21.8333C22.75 23.5 23.5 22.75 23.5 21.8333V10.1667C23.5 9.25 22.75 8.5 21.8333 8.5ZM21.8333 21.8333H10.1667V10.1667H21.8333V21.8333ZM11.8333 14.3333H13.5V20.1667H11.8333V14.3333ZM15.1667 11.8333H16.8333V20.1667H15.1667V11.8333ZM18.5 16.8333H20.1667V20.1667H18.5V16.8333Z"
        fill="#535D66"
      />
    </Svg>
  );
};

export default ActivityButtonIcon;
