import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Dimensions,
} from 'react-native';
import CountryFlag from 'react-native-country-flag';
import color from '../../../theme/pallets/pallet';

type Props = {
  setCountry: (countryCode: string) => void;
  setDropdownVisible: (value: boolean) => void;
  dropdownVisible: boolean;
};

const SelectCountry = ({
  setCountry,
  setDropdownVisible,
  dropdownVisible,
}: Props) => {
  const [selectedCountry, setSelectedCountry] = useState('AR');

  const countries = [
    { code: 'AR', name: 'Argentina' },
    { code: 'BR', name: 'Brazil' },
    { code: 'US', name: 'United States' },
  ];

  useEffect(() => {
    setCountry(selectedCountry);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleCountrySelect = (countryCode: string) => {
    setSelectedCountry(countryCode);
    setCountry(countryCode);
    setDropdownVisible(!dropdownVisible);
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity>
        <Text style={styles.label}>País de residencia</Text>
        <View style={styles.dropdown}>
          <CountryFlag
            isoCode={selectedCountry}
            // eslint-disable-next-line react-native/no-inline-styles
            style={{ marginRight: 8 }}
            size={15}
          />
          <Text style={styles.selectedCountry}>
            {countries.find(country => country.code === selectedCountry)?.name}
          </Text>
        </View>
        <Text style={styles.sublabel}>
          Debe ser igual que el de tu documento de identidad
        </Text>
      </TouchableOpacity>

      {dropdownVisible && (
        <View style={styles.modalContainer}>
          <FlatList
            data={countries}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.modalOption}
                onPress={() => handleCountrySelect(item.code)}
              >
                <CountryFlag
                  isoCode={item.code}
                  style={{ marginRight: 8 }}
                  size={15}
                />
                <Text style={styles.modalOptionText}>{item.name}</Text>
              </TouchableOpacity>
            )}
            keyExtractor={item => item.code}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
    width: '100%',
  },
  label: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.TEXT_PRIMARY,
  },
  sublabel: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 12,
    color: color.NEUTRALS_800,
    top: 2,
    left: 3,
  },
  dropdown: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    borderRadius: 8,
    marginTop: 4,
  },
  selectedCountry: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
  modalContainer: {
    height: Dimensions.get('window').height / 4,
    marginTop: 4,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    borderRadius: 8,
    elevation: 4,
    shadowOffset: {
      height: 0,
      width: 4,
    },
    shadowOpacity: 0.1,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
  },
  modalOptionText: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 12,
    color: color.NEUTRALS_800,
  },
});

export default SelectCountry;
