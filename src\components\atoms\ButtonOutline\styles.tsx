import { StyleSheet } from 'react-native';
import color from '../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  background: {
    width: '100%',
    borderRadius: 12,
    borderColor: color.PRIMARY_700,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  outlineBackground: {
    width: '100%',
    borderRadius: 12,
    borderColor: color.PRIMARY_700,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  disabledBackground: {
    width: '100%',
    backgroundColor: color.NEUTRALS_100,
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  text: {
    textAlign: 'center',
    padding: 11,
    paddingBottom: 14.5,
  },
  disabledText: {
    textAlign: 'center',
    padding: 12,
    paddingBottom: 14.5,
    color: color.NEUTRALS_400,
  },
});
