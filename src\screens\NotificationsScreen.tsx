import React, { useEffect, useState } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import HeaderNotifications from '../components/organisms/Headers/Notifications';
import {
  deleteNotification,
  getNotificationList,
} from '../services/notifications';
import { NotificationItem } from '../components/molecules/NotificationItem';
import { Notification } from '../types/Notifications';

export const NotificationsScreen = () => {
  const [notifications, setNotifications] = useState<Notification[]>();

  const getNotifications = async () => {
    const res = await getNotificationList();
    setNotifications(res?.data);
  };

  const handleDeleteNotification = async () => {
    await deleteNotification();
    getNotifications();
  };

  useEffect(() => {
    getNotifications();
  }, []);

  return (
    <View style={styles.container}>
      <HeaderNotifications
        handleDeleteNotification={handleDeleteNotification}
      />
      <ScrollView style={styles.notification}>
        {notifications?.map(noti => (
          <NotificationItem key={noti._id} item={noti} />
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  notification: {
    paddingHorizontal: 16,
  },
});
