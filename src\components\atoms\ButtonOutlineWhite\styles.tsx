import { StyleSheet } from 'react-native';
import color from '../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  background: {
    width: '100%',
    borderRadius: 4,
    borderColor: color.WHITE,
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  outlineBackground: {
    width: '100%',
    borderRadius: 4,
    borderColor: color.WHITE,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  disabledBackground: {
    width: '100%',
    backgroundColor: color.NEUTRALS_100,
    borderRadius: 4,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  text: {
    textAlign: 'center',
    padding: 12,
    color: color.WHITE,
    fontFamily: 'Satoshi-SemiBold',
    fontSize: 16,
  },
  disabledText: {
    textAlign: 'center',
    padding: 12,
    color: color.NEUTRALS_400,
  },
});
