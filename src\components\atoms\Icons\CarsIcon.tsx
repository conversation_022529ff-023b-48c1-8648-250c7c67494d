import React, { FC } from 'react';
import Svg, { G, Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

interface Props extends IconProps {
  selected?: boolean;
}

const CarsIcon: FC<Props> = ({ color = '#0068FF', selected = false }) => {
  return (
    <Svg width="42" height="42" viewBox="0 0 42 42" fill="none">
      {selected && <Rect width="42" height="42" rx="8" fill="#F0F6FF" />}
      <G transform="translate(6.6 6) scale(1.2)">
        <Path
          d="M18.92 5.01C18.72 4.42 18.16 4 17.5 4H6.5C5.84 4 5.29 4.42 5.08 5.01L3 11V19C3 19.55 3.45 20 4 20H5C5.55 20 6 19.55 6 19V18H18V19C18 19.55 18.45 20 19 20H20C20.55 20 21 19.55 21 19V11L18.92 5.01ZM6.85 6H17.14L18.22 9.11H5.77L6.85 6ZM19 16H5V11H19V16Z"
          fill={color}
        />
        <Path
          d="M7.5 15C8.32843 15 9 14.3284 9 13.5C9 12.6716 8.32843 12 7.5 12C6.67157 12 6 12.6716 6 13.5C6 14.3284 6.67157 15 7.5 15Z"
          fill={color}
        />
        <Path
          d="M16.5 15C17.3284 15 18 14.3284 18 13.5C18 12.6716 17.3284 12 16.5 12C15.6716 12 15 12.6716 15 13.5C15 14.3284 15.6716 15 16.5 15Z"
          fill={color}
        />
      </G>
    </Svg>
  );
};

export default CarsIcon;
