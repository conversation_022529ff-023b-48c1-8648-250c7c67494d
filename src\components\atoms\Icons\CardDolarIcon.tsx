import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const CardDolarIcon: FC<IconProps> = ({ color = '#FF0033', size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M6.66675 14.1667H8.33342V13.3334H9.16675C9.62508 13.3334 10.0001 12.9584 10.0001 12.5V10C10.0001 9.54171 9.62508 9.16671 9.16675 9.16671H6.66675V8.33337H10.0001V6.66671H8.33342V5.83337H6.66675V6.66671H5.83342C5.37508 6.66671 5.00008 7.04171 5.00008 7.50004V10C5.00008 10.4584 5.37508 10.8334 5.83342 10.8334H8.33342V11.6667H5.00008V13.3334H6.66675V14.1667ZM16.6667 3.33337H3.33341C2.40841 3.33337 1.67508 4.07504 1.67508 5.00004L1.66675 15C1.66675 15.925 2.40841 16.6667 3.33341 16.6667H16.6667C17.5917 16.6667 18.3334 15.925 18.3334 15V5.00004C18.3334 4.07504 17.5917 3.33337 16.6667 3.33337ZM16.6667 15H3.33341V5.00004H16.6667V15ZM11.6667 8.33337L13.3334 6.66671L15.0001 8.33337H11.6667ZM15.0001 11.875L13.3334 13.5417L11.6667 11.875"
        fill={color}
      />
    </Svg>
  );
};

export default CardDolarIcon;
