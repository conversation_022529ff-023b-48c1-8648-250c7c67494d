import { StyleSheet } from 'react-native';
import color from '../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  background: {
    width: '100%',
    backgroundColor: color.PRIMARY_700,
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  disabledBackground: {
    width: '100%',
    backgroundColor: color.NEUTRALS_100,
    borderRadius: 12,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  text: {
    textAlign: 'center',
    padding: 12,
    paddingBottom: 14.5,
    color: color.WHITE,
  },
  disabledText: {
    textAlign: 'center',
    padding: 12,
    paddingBottom: 14.5,
    color: color.NEUTRALS_400,
    fontSize: 16,
  },
});
