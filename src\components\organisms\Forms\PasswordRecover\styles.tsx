import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 24,
    justifyContent: 'space-between',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 18,
    backgroundColor: '#FFFFFF',
  },
  left: {
    flex: 1,
  },
  center: {
    flex: 1,
    alignItems: 'center',
  },
  right: {
    flex: 1,
  },
  containerText: {
    display: 'flex',
    alignSelf: 'center',
  },
  topText: {
    marginTop: 10,
  },
  botText: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    alignSelf: 'center',
    color: color.BLACK,
    fontWeight: '800',
    marginTop: 5,
  },
  containerCode: {
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: '10%',
  },
  txtCode: {
    fontSize: 14,
    fontFamily: 'Satoshi-Regular',
    color: '#78838D',
    marginVertical: 8,
  },
  errorCodetxt: {
    marginTop: 10,
  },
  resendContainer: {
    display: 'flex',
    alignSelf: 'center',
    marginTop: '10%',
  },
  textCont: {
    color: '#78838D',
    fontFamily: 'Satoshi-Regular',
    fontSize: 18,
  },
  countdownText: {
    color: color.BLACK,
    fontFamily: 'Satoshi-Regular',
  },
  txtResend: {
    color: '#FF0033',
    fontFamily: 'Satoshi-Regular',
    fontSize: 20,
    fontWeight: '700',
  },
  txtCodeResend: {
    color: color.BLACK,
    fontFamily: 'Satoshi-Regular',
  },
  title: {
    fontFamily: 'Satoshi-Bold',
    fontWeight: '600',
    fontSize: 22,
    color: color.TEXT_PRIMARY,
  },
  subtitle: {
    fontFamily: 'Satoshi-Regular',
    color: color.TEXT_PRIMARY,
  },
  containerInputs: {
    marginTop: 15,
  },
  redTxt: {
    color: '#FF0033',
    marginLeft: 5,
    fontSize: 16,
    fontFamily: 'Satoshi-Regular',
    marginBottom: 3,
  },
  greenTxt: {
    color: '#4DA66B',
    marginLeft: 5,
    fontSize: 16,
    fontFamily: 'Satoshi-Regular',
    marginBottom: 3,
  },
  txtDesc: {
    color: color.NEUTRALS_800,
    fontFamily: 'Satoshi-Regular',
    marginVertical: 5,
  },
  ckeckPass: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  txtmail: {
    marginBottom: 10,
  },
});
