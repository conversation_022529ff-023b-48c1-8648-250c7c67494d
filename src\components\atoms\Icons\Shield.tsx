import React, { FC } from 'react'
import Svg, { Path, G } from 'react-native-svg'
import { IconProps } from './types'

const Shield: FC<IconProps> = ({ width, height }) => {
    return (
        <Svg width={width} height={height} viewBox="0 0 156 194" fill="none" xmlns="http://www.w3.org/2000/svg">
        <Path d="M78 194C121.078 194 156 192.209 156 190C156 187.791 121.078 186 78 186C34.9218 186 0 187.791 0 190C0 192.209 34.9218 194 78 194Z" fill="#FFE7E3"/>
        <Path d="M134.429 20.5144L133.276 119.377C129.205 152.273 70.8918 178.389 70.8918 178.389C70.8918 178.389 12.5699 152.298 8.47373 119.377L7.28778 20.5144L69.6133 0L134.429 20.5144Z" fill="#FF583F"/>
        <Path d="M126.295 26.7722L125.294 115.542C121.745 144.232 70.8833 167.009 70.8833 167.009C70.8833 167.009 20.022 144.232 16.481 115.542L15.4128 26.7722L69.7731 8.88202L126.295 26.7722Z" fill="#FFE7E3"/>
        <Path d="M122.729 32.3739L121.787 113.868C118.422 140.783 70.774 162.114 70.774 162.114C70.774 162.114 23.092 140.758 19.7696 113.868L18.7687 32.3739L69.731 15.6024L122.729 32.3739Z" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
        <Path d="M112.098 102.695H108.784V103.536H112.098V102.695Z" fill="#FF583F"/>
        <Path d="M126.338 102.695H115.529V103.536H126.338V102.695Z" fill="#FF583F"/>
        <Path d="M146.12 88.3371L144.026 112.309C143.917 113.368 143.424 114.351 142.64 115.071C141.856 115.792 140.835 116.201 139.77 116.22C139.269 116.228 138.772 116.131 138.311 115.933C137.851 115.734 137.438 115.441 137.1 115.071C136.762 114.701 136.506 114.263 136.35 113.787C136.194 113.311 136.141 112.807 136.195 112.309L138.273 88.3371C138.466 86.5493 138.277 84.7407 137.718 83.0315C137.16 81.3222 136.244 79.7513 135.032 78.423C133.819 77.0946 132.339 76.0393 130.687 75.3268C129.036 74.6144 127.252 74.2613 125.454 74.2908H116.909C113.087 74.3631 109.423 75.8315 106.61 78.4191C103.796 81.0067 102.026 84.5345 101.634 88.3371L100.793 98.1276H92.9711L93.8122 88.3371C94.8636 76.2758 105.537 66.4685 117.59 66.4685H126.169C128.969 66.4224 131.746 66.9721 134.316 68.0813C136.886 69.1904 139.192 70.8336 141.078 72.9018C142.965 74.97 144.391 77.4159 145.26 80.0771C146.129 82.7383 146.422 85.5539 146.12 88.3371Z" fill="#FF583F"/>
        <G opacity="0.5">
        <Path d="M146.12 88.3371L144.026 112.309C143.917 113.368 143.424 114.351 142.64 115.071C141.856 115.792 140.835 116.201 139.77 116.22C139.269 116.228 138.772 116.131 138.311 115.933C137.851 115.734 137.438 115.441 137.1 115.071C136.762 114.701 136.506 114.263 136.35 113.787C136.194 113.311 136.141 112.807 136.195 112.309L138.273 88.3371C138.466 86.5493 138.277 84.7407 137.718 83.0315C137.16 81.3222 136.244 79.7513 135.032 78.423C133.819 77.0946 132.339 76.0393 130.687 75.3268C129.036 74.6144 127.252 74.2613 125.454 74.2908H116.909C113.087 74.3631 109.423 75.8315 106.61 78.4191C103.796 81.0067 102.026 84.5345 101.634 88.3371L100.793 98.1276H92.9711L93.8122 88.3371C94.8636 76.2758 105.537 66.4685 117.59 66.4685H126.169C128.969 66.4224 131.746 66.9721 134.316 68.0813C136.886 69.1904 139.192 70.8336 141.078 72.9018C142.965 74.97 144.391 77.4159 145.26 80.0771C146.129 82.7383 146.422 85.5539 146.12 88.3371Z" fill="white"/>
        </G>
        <Path d="M146.684 111.434L142.604 158.148C142.456 159.597 141.782 160.941 140.709 161.925C139.636 162.909 138.24 163.466 136.784 163.489H90.0693C89.3851 163.502 88.7061 163.368 88.0775 163.098C87.4488 162.827 86.885 162.426 86.4235 161.921C85.962 161.416 85.6134 160.818 85.4008 160.167C85.1883 159.517 85.1168 158.829 85.1909 158.148L89.2787 111.434C89.3727 110.502 89.6852 109.606 90.1909 108.818C90.6966 108.031 91.3812 107.373 92.1889 106.9C93.0657 106.363 94.071 106.073 95.0991 106.059H141.805C142.76 106.054 143.696 106.319 144.505 106.824C145.261 107.314 145.865 108.005 146.25 108.819C146.635 109.634 146.785 110.539 146.684 111.434Z" fill="#FF583F"/>
        <Path d="M98.1102 131.839L110.247 150.52L150.275 110.492C150.337 110.417 150.37 110.324 150.369 110.227C150.367 110.13 150.331 110.037 150.267 109.965C150.203 109.893 150.115 109.846 150.019 109.833C149.924 109.82 149.826 109.842 149.745 109.894L111.601 139.796L104.393 128.18L98.1102 131.839Z" fill="white"/>
        </Svg>
        
    )
}

export default Shield