import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Dimensions,
} from 'react-native';
import color from '../../../theme/pallets/pallet';

type Props = {
  setMotive: (motive: string) => void;
  keyboardDismiss: () => void;
  setDropdownVisible: (value: boolean) => void;
  dropdownVisible: boolean;
};

const SelectMotive = ({
  setMotive,
  keyboardDismiss,
  setDropdownVisible,
  dropdownVisible,
}: Props) => {
  const [selectedOption, setSelectedOption] = useState('Varios');

  const options = [
    'Varios',
    'Al<PERSON>leres',
    'Cuotas',
    'Expensas',
    'Facturas',
    '<PERSON><PERSON><PERSON>',
    'Honorario<PERSON>',
    'Préstamos',
  ];

  const handleOptionSelect = option => {
    setSelectedOption(option);
    setMotive(option);
    setDropdownVisible(!dropdownVisible);
  };

  const openDropdown = () => {
    setDropdownVisible(true);
    keyboardDismiss();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={openDropdown}>
        <Text
          style={{
            fontFamily: 'Satoshi-Regular',
            fontSize: 12,
            color: color.TEXT_PRIMARY,
          }}
        >
          Motivo
        </Text>
        <View
          style={{
            paddingHorizontal: 16,
            paddingVertical: 12,
            borderWidth: 1,
            borderColor: color.NEUTRALS_100,
            borderRadius: 8,
            marginTop: 4,
          }}
        >
          <Text
            style={{
              fontFamily: 'Satoshi-Regular',
              fontSize: 14,
              color: color.NEUTRALS_800,
            }}
          >
            {selectedOption}
          </Text>
        </View>
      </TouchableOpacity>

      {dropdownVisible && (
        <View
          style={{
            height: Dimensions.get('window').height / 4,
            marginTop: 4,
            borderWidth: 1,
            borderColor: color.NEUTRALS_100,
            borderRadius: 8,
            elevation: 4,
            shadowOffset: {
              height: 0,
              width: 4,
            },
            shadowOpacity: 0.1,
          }}
        >
          <FlatList
            data={options}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.modalOption}
                onPress={() => handleOptionSelect(item)}
              >
                <Text
                  style={{
                    textAlign: 'left',
                    fontFamily: 'Satoshi-Regular',
                    fontSize: 12,
                    color: color.NEUTRALS_800,
                  }}
                >
                  {item}
                </Text>
              </TouchableOpacity>
            )}
            keyExtractor={item => item}
          />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 20,
    width: '100%',
  },
  dropdownButton: {
    height: 50,
    borderColor: 'gray',
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalOption: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    // alignItems: 'center',
  },
});

export default SelectMotive;
