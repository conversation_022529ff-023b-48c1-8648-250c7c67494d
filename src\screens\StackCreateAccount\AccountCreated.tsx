import React, { useContext, useEffect, useState } from 'react';
import {
  ActivityIndicator,
  BackHandler,
  Image,
  View,
} from 'react-native';
import Logo from '../../components/atoms/Icons/Logo';
import { stylesCountry, stylesCreated } from './styles';
import { Button, Text } from '../../components/atoms';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import { AuthContext } from '../../context/AuthContext';
import { CommonActions, useNavigation } from '@react-navigation/native';
import { getUserInfo } from '../../services/users';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import { storage } from '../../lib/mmkv';
import color from '../../theme/pallets/pallet';

export const AccountCreated = ({ route }: any) => {
  const { signIn } = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [link, setLink] = useState('');

  const navigation: any = useNavigation();

  const email = route.params.email;
  const password = route.params.password;
  useDisableBackButton();
  const handleSignIn = async () => {
    setLoading(true);
    try {
      storage.delete('unlockMethodType');
      await signIn(email, password);
      const userInfo = await getUserInfo();
      setLink(userInfo?.linkOnboarding!);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleIdentity = () => {
    if (!link) return;
    navigation.navigate('WebViewScreen', { link });
  };

  const handleGoHome = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: 'Auth' }],
      }),
    );
  };

  useEffect(() => {
    handleSignIn();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      () => true,
    );

    return () => backHandler.remove();
  }, []);

  if (loading) {
    return (
      <View style={stylesCountry.container}>
        <View style={[stylesCountry.logo, stylesCountry.flex1]}>
          <ActivityIndicator size={64} color={color.PRIMARY_500} />
        </View>
      </View>
    );
  }

  return (
    <KeyboardAvoidingComponent collapse>
      <View style={stylesCreated.container}>
        <View>
          <View style={stylesCountry.logo}>
            <Logo height={100} width={100} />
          </View>
          <View style={stylesCreated.imgContainer}>
            <Image
              source={require('../../assets/ilustrations/present-wallet/present-wallet.png')}
              style={stylesCreated.image}
            />
          </View>
          <View style={stylesCreated.containerText}>
            <Text variant="B3">¡Tu cuenta está lista!</Text>
            <Text variant="R6">
              Accede a tu cuenta o valida tu identidad para comenzar a operar.
            </Text>
          </View>
        </View>
        <View style={stylesCreated.gap}>
          <Button text="Validar identidad" onPress={handleIdentity} />
          <ButtonOutline text="Ir a mi billetera" onPress={handleGoHome} />
        </View>
      </View>
    </KeyboardAvoidingComponent>
  );
};
