import { useCallback, useState } from 'react';

type ReturnType = [boolean, (value?: boolean) => void];

const useToggle = (initialState = false): ReturnType => {
  // Initialize the state
  const [state, setState] = useState(initialState);

  // Define and memorize toggler function in case we pass down the comopnent,
  // This function change the boolean value to it's opposite value
  const toggle = useCallback((value?: boolean) => {
    if (value === true || value === false) setState(value);
    else setState(prevState => !prevState);
  }, []);

  return [state, toggle];
};

export default useToggle;
