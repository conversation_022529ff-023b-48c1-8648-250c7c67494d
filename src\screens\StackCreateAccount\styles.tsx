import { StyleSheet } from 'react-native';
import color from '../../theme/pallets/pallet';

export const stylesCountry = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.WHITE,
    paddingHorizontal: 16,
    paddingVertical: 24,
    justifyContent: 'space-between',
  },
  back: {
    position: 'absolute',
    top: 20,
  },
  logo: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    color: color.TEXT_PRIMARY,
  },
  subtitle: {
    fontFamily: 'Satoshi-Regular',
    color: color.TEXT_PRIMARY,
  },
  button: {
    marginBottom: 16,
  },
  buttonRelative: {
    position: 'relative',
    marginTop: '30%',
  },
  flex1: {
    flex: 1,
  },
});

export const stylesName = StyleSheet.create({
  checkboxStyle: {
    marginBottom: 16,
    gap: 24,
  },
  containerInputs: {
    marginTop: 15,
  },
  topText: {
    marginVertical: 10,
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.TEXT_PRIMARY,
  },
  ckeckPass: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 2,
  },
  redTxt: {
    color: '#FF0033',
    marginLeft: 5,
    fontSize: 16,
    fontFamily: 'Satoshi-Regular',
    marginBottom: 3,
  },
  greenTxt: {
    color: '#4DA66B',
    marginLeft: 5,
    fontSize: 16,
    fontFamily: 'Satoshi-Regular',
    marginBottom: 3,
  },
  txtDesc: {
    color: color.NEUTRALS_800,
    fontFamily: 'Satoshi-Regular',
    marginVertical: 5,
  },
  noMatchPass: {
    color: '#B83232',
    marginLeft: 5,
    fontSize: 16,
    fontFamily: 'Satoshi-Regular',
    marginTop: 5,
  },
  inputNumber: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  inputPhone: {
    marginLeft: 10,
    width: '50%',
  },
  pickerPhone: {
    width: 120,
    color: color.BLACK,
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
  },
});

export const stylesVerify = StyleSheet.create({
  containerText: {
    display: 'flex',
    alignSelf: 'center',
  },
  topText: {
    color: color.BLACK,
  },
  botText: {
    alignSelf: 'center',
    color: color.BLACK,
    marginTop: 5,
  },
  txtCode: {
    marginVertical: 8,
  },
  containerCode: {
    paddingHorizontal: 16,
    alignItems: 'center',
    marginTop: '10%',
  },
  resendContainer: {
    display: 'flex',
    alignSelf: 'center',
    marginTop: '10%',
  },
  countdownText: {
    color: color.BLACK,
    fontFamily: 'Satoshi-Regular',
  },
  textCont: {
    color: '#78838D',
  },
  txtResend: {
    color: '#FF0033',
  },
  errorCodetxt: {
    marginTop: 10,
  },
});

export const stylesCreated = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 20,
    justifyContent: 'space-between',
  },
  image: {
    resizeMode: 'contain',
  },
  separatebutton: {
    marginVertical: 5,
  },
  containerText: {
    alignItems: 'center',
    marginTop: '10%',
  },
  txtTop: {
    color: color.TEXT_PRIMARY,
    fontFamily: 'Satoshi-Bold',
    fontSize: 24,
  },
  txtBot: {
    fontFamily: 'Satoshi-Regular',
    color: color.NEUTRALS_800,
    marginTop: 20,
    textAlign: 'center',
    fontSize: 18,
  },
  gap: {
    marginVertical: 16,
    gap: 22,
  },
  imgContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
