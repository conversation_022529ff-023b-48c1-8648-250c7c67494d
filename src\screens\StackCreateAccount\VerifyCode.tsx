import React, { useEffect, useRef, useState } from 'react';
import { TextInput, TouchableWithoutFeedback, View } from 'react-native';
import { stylesCountry, stylesVerify } from './styles';
import { useNavigation } from '@react-navigation/native';
import { But<PERSON>, Text, TextBase } from '../../components/atoms';
import { InputVerify } from '../../components/molecules/InputVerify';
import { useResendCode, useVerifyCode } from '../../hooks/SignUp/VerifyCode';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import color from '../../theme/pallets/pallet';

export const VerifyCode = ({ route }: any) => {
  const [code, setCode] = useState('');
  const [resendDisabled, setResendDisabled] = useState(true);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(35);
  const [loading, setLoading] = useState<boolean>(false);
  const [isCodeComplete, setIsCodeComplete] = useState(false);
  const [error, setError] = useState<boolean>(false);
  const navigation: any = useNavigation();

  const email = route.params.email;
  const password = route.params.password;

  const inputRef = useRef<TextInput>(null);

  const handleOnPress = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const { postCode, success } = useVerifyCode({ setLoading, setError });

  const { resendCode } = useResendCode();

  const handleContinue = async () => {
    const body = {
      username: email,
      code: code,
    };
    await postCode(body);
  };

  if (success) {
    navigation.navigate('AccountCreated', {
      email: email,
      password: password,
    });
  }

  const handleResendCode = async () => {
    setMinutes(0);
    setSeconds(35);
    setResendDisabled(true);
    const body: any = {
      email: email,
    };
    await resendCode(body);
  };

  const handleCodeChange = (value: string) => {
    setCode(value);
    if (value.length === 6) {
      setIsCodeComplete(true);
    } else {
      setIsCodeComplete(false);
    }
  };

  useEffect(() => {
    if (code.length > 0) {
      setError(false);
    }
  }, [code]);

  useEffect(() => {
    const timer = setInterval(() => {
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(timer);
          setResendDisabled(false);
          return;
        } else {
          setMinutes(minutes - 1);
          setSeconds(59);
        }
      } else {
        setSeconds(seconds - 1);
      }
    }, 1000);
    return () => clearInterval(timer);
  }, [minutes, seconds]);

  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

  return (
    <KeyboardAvoidingComponent collapse>
      <View style={stylesCountry.container}>
        <View>
          <View style={stylesVerify.containerText}>
            <Text variant="R6" style={stylesVerify.topText}>
              Enviamos un código al correo electrónico
            </Text>
            <Text variant="B6" style={stylesVerify.botText}>
              {email}
            </Text>
          </View>
          <View style={stylesVerify.containerCode}>
            <Text
              variant="R7"
              color="NEUTRALS_600"
              style={stylesVerify.txtCode}
            >
              Ingresa el código
            </Text>
            <InputVerify
              code={code}
              handleOnPress={handleOnPress}
              handleOnChange={handleCodeChange}
              inputRef={inputRef}
              error={error}
              maxLength={6}
            />
            {error && (
              <TextBase
                size="l"
                type="SemiBold"
                color={color.RED_700}
                style={stylesVerify.errorCodetxt}
              >
                Código incorrecto
              </TextBase>
            )}
          </View>
          <View style={stylesVerify.resendContainer}>
            {resendDisabled ? (
              <Text variant="R7" style={stylesVerify.textCont}>
                Reenviar código{' '}
                <Text style={stylesVerify.countdownText}>
                  {minutes < 10 ? `0${minutes}` : minutes}:{formattedSeconds}
                </Text>
              </Text>
            ) : (
              <TouchableWithoutFeedback onPress={handleResendCode}>
                <Text variant="R7" style={stylesVerify.txtResend}>
                  Reenviar código <Text variant="R7">00:00</Text>
                </Text>
              </TouchableWithoutFeedback>
            )}
          </View>
        </View>
        <Button
          text="Verificar código"
          onPress={handleContinue}
          loading={loading}
          disabled={!isCodeComplete}
        />
      </View>
    </KeyboardAvoidingComponent>
  );
};
