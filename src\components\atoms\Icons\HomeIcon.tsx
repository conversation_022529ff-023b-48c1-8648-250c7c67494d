import React, { <PERSON> } from 'react';
import Svg, { G, Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

interface Props extends IconProps {
  selected?: boolean;
}

const HomeIcon: FC<Props> = ({ color = '#0068FF', selected = false }) => {
  return (
    <Svg width="42" height="42" viewBox="0 0 42 42" fill="none">
      {selected && <Rect width="42" height="42" rx="8" fill="#F0F6FF" />}
      <G transform="translate(6.6 6) scale(1.2)">
        <Path
          d="M12 6.69L17 11.19V19H15V13H9V19H7V11.19L12 6.69ZM12 4L2 13H5V21H11V15H13V21H19V13H22L12 4Z"
          fill={color}
        />
      </G>
    </Svg>
  );
};

export default HomeIcon;
