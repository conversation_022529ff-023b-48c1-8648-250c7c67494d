import React from 'react';
import { View } from 'react-native';
import { styles } from './styles';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import color from '../../../../theme/pallets/pallet';

export const Skeleton = () => {
  return (
    <View style={styles.container}>
      <SkeletonPlaceholder
        backgroundColor={color.PRIMARY_700}
        highlightColor={color.PRIMARY_900}
      >
        <SkeletonPlaceholder.Item
          flexDirection="row"
          justifyContent="space-between"
          width="94%"
          margin={8}
        >
          <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
            <SkeletonPlaceholder.Item
              width={40}
              height={40}
              borderRadius={50}
            />
            <SkeletonPlaceholder.Item
              marginLeft={8}
              width={100}
              height={20}
              borderRadius={4}
            />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};
