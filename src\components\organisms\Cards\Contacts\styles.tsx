import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  containerContact: {
    borderColor: color.NEUTRALS_100,
    borderWidth: 1,
    borderRadius: 16,
    padding: 8,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#EAEAEA',
  },
  image: {
    width: 34,
    height: 34,
    marginRight: 16,
  },
  txt: {
    fontSize: 14,
    fontFamily: 'Satoshi-Bold',
    color: color.TEXT_PRIMARY,
    marginVertical: 16,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  circle: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: color.PRIMARY_100,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  plus: {
    color: '#858585',
    fontSize: 16,
  },
  name: {
    fontSize: 16,
    marginLeft: 10,
    maxWidth: 200,
    textTransform: 'capitalize',
  },
  cuenta: {
    fontSize: 14,
    marginLeft: 10,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  search: {
    marginBottom: 16,
  },
  star: {
    marginRight: 16,
  },
});
