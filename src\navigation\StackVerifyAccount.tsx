import React from 'react';
import GoBack from '../components/organisms/Buttons/GoBackButton';
import { useNavigation } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Logo from '../components/atoms/Icons/Logo';
import { VerifyCode } from '../screens/StackVerifyAccount/VerifyCode';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<any>();

function ArrowBack() {
  const navigation = useNavigation();
  return <GoBack onPress={() => navigation.goBack()} />;
}

function LogoToshify() {
  return <Logo height={24} width={120} />;
}

export default function StackVerifyAccount() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: LogoToshify,
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          headerTitleAlign: 'center',
        }}
        name={'VerifyCode'}
        component={VerifyCode}
      />
    </Stack.Navigator>
  );
}
