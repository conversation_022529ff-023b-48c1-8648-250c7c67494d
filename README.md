This is a new [**React Native**](https://reactnative.dev) project, bootstrapped using [`@react-native-community/cli`](https://github.com/react-native-community/cli).

# Getting Started

>**Note**: Make sure you have completed the [React Native - Environment Setup](https://reactnative.dev/docs/environment-setup) instructions till "Creating a new application" step, before proceeding.

# Gonnectia - Toshify app

Este es un proyecto React Native con una estructura modular y bien organizada. A continuación, se detallan los pasos para la instalación y ejecución del proyecto, así como una descripción de la estructura de carpetas.

## Requisitos

- Node.js v18.16.0
- Yarn
- React Native CLI
- Android Studio (para desarrollo en Android)
- Xcode (para desarrollo en iOS)

## Instalación

  ### 1. Clonar el repositorio

   <NAME_EMAIL>:goiar/gon-mobile.git
   ### 2. Instalar dependencias

 yarn install

 ### 3. Ejecutar en Android
   Para ejecutar la aplicación en un dispositivo o emulador Android:

 #### QA Environment: `yarn android`

 #### Demo Environment: `yarn android:demo`

 #### Development Environment: `yarn android:dev`

 ### 4. Ejecutar en iOS (Solo con Macbook)
   Para ejecutar la aplicación en un dispositivo o simulador iOS:

 #### QA Environment: `yarn ios`

 ### 5. Otros comandos útiles
  
   ####  Lint: Ejecuta el linter para verificar el código: `yarn lint`

 #### Start: Inicia el servidor de desarrollo de React Native: `yarn start`

 #### Clean: Limpia el proyecto de Android: `yarn clean`

 #### Bundle: Crea el bundle de la aplicación para Android: `yarn bundle`

 #### Build APK: Crea el APK para Android: `yarn bundle-apk` (Se guardaran en este path 'goiar\gon-mobile\android\app\build\outputs\apk\debug')

  #### Assemble Debug QA APK: Ensambla el APK de debug para el entorno QA.: `yarn android:qa-apk`

  #### Assemble Debug Demo APK: Ensambla el APK de debug para el entorno Demo: `yarn android:demo-apk`

  #### Build iOS: Crea el bundle para iOS: `yarn build:ios`    

 #### Find Dead Code: Encuentra código no utilizado en el proyecto: `yarn find-deadcode`

 ## Estructura del proyecto
  La estructura del proyecto está organizada de la siguiente manera:

```
  ├── App.tsx
  │
  ├── assets
  │ ├── fonts
  │ ├── img
  ├── components
  | ├── atoms
  │ ├── molecules
  │ ├── organisms
  ├── constants
  │ ├── schemas
  ├── context
  ├── helpers
  ├── context
  ├── hooks
  ├── layout
  ├── lib
  ├── navigation
  ├── screens
  │   ├── StackCashin
  │   ├── StackCreateAccount
  │   ├── etc...
  ├── services
  ├── types
```

### Descripción de las Carpetas

- **app**: Contiene el archivo `App.js` que renderiza todas las pantallas y configura la navegación de la aplicación.
- **assets**: Carpeta que incluye recursos estáticos utilizados en la aplicación.
    - fonts: Fuentes personalizadas que se utilizan en la   aplicación.
    - img: Imágenes y otros recursos gráficos.
- **components**: Contiene componentes reutilizables que se utilizan en diferentes partes de la aplicación

   - atoms: Componentes básicos e individuales que no dependen de otros componentes.
   - molecules: Componentes más complejos que combinan dos o más átomos.
   - organisms: Componentes de alto nivel que pueden consistir en múltiples moléculas y átomos.
- **constants**: Incluye constantes y esquemas utilizados en la aplicación
    
    - schemas: Esquemas de validación de yup para la logica de login.

- **context**: Proveedores de contexto para la gestión del estado global de la aplicación. (se añadio zustand y esta embebido en esta carpeta, ideal usar en toda la app)

- **helpers**: Funciones auxiliares y utilidades que ayudan a realizar diversas tareas comunes.

- **hooks**: Custom hooks para manejar la logica de la llamada a la api.

- **layout**: Componentes y estilos relacionados con la disposición visual de la aplicación. (renderiza toda la app)


- **lib**: Bibliotecas externas o módulos auxiliares utilizados en la aplicación.

- **navigation**: Configuración de la navegación de la aplicación, como pilas de navegadores y rutas.

- **screens**: Incluye todas las pantallas de la aplicación. Se descrimina el flujo de navegation por stack de carpeta y dentro de las mismas todas las pantallas que poseera dicha funcionalidad.

- **services**: Servicios para interactuar con APIs u otros servicios externos.

- **types**: Definiciones de tipos TypeScript utilizados en la aplicación. (Se deberia usar mas :D)


 ## Enlaces utiles
 Enlaces utiles a todo lo que refiere a la app.
  - [Figma](https://www.figma.com/design/DVyK8tJl7lJDZZlTdVOBrO/Gollet?node-id=1706-50966&t=q8dAcKrlVi1sIXlP-0) - Diseño de Gollet.

  - [APK](https://goiar.sharepoint.com/sites/gonnectia/Documentos%20compartidos/Forms/AllItems.aspx?id=%2Fsites%2Fgonnectia%2FDocumentos%20compartidos%2FGollet%20APP&p=true&ga=1) - Sharepoint en donde estan las versiones en formato APK (android).


  - [Store iOS](https://appstoreconnect.apple.com/login) - Store para ver la ficha de la app en la store de iOS.


   # Learn More

   To learn more about React Native, take a look at the following resources:

   - [React Native Website](https://reactnative.dev) - learn more about React Native.
   - [Getting Started](https://reactnative.dev/docs/environment-setup) - an **overview** of React Native and how setup your environment.
   - [Learn the Basics](https://reactnative.dev/docs/getting-started) - a **guided tour** of the React Native **basics**.
   - [Blog](https://reactnative.dev/blog) - read the latest official React Native **Blog** posts.
   - [`@facebook/react-native`](https://github.com/facebook/react-native) - the Open Source; GitHub **repository** for React Native.
