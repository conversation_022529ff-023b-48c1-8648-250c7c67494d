import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const FlagError: FC<IconProps> = ({ width, height, color }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 20 21">
      <Path
        d="M4.16667 13.8333V18.8333H2.5V3H10.3183C10.4731 3.00008 10.6247 3.04324 10.7563 3.12463C10.8879 3.20602 10.9942 3.32243 11.0633 3.46083L11.6667 4.66667H16.6667C16.8877 4.66667 17.0996 4.75446 17.2559 4.91074C17.4122 5.06702 17.5 5.27899 17.5 5.5V14.6667C17.5 14.8877 17.4122 15.0996 17.2559 15.2559C17.0996 15.4122 16.8877 15.5 16.6667 15.5H11.3483C11.1936 15.4999 11.042 15.4568 10.9104 15.3754C10.7788 15.294 10.6725 15.1776 10.6033 15.0392L10 13.8333H4.16667ZM4.16667 4.66667V12.1667H11.03L11.8633 13.8333H15.8333V6.33333H10.6367L9.80333 4.66667H4.16667Z"
        fill={color}
      />
    </Svg>
  );
};

export default FlagError;
