import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const SecureIcon: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 21 21">
      <Path
        d="M9.66667 2.16669L15.7483 4.06669C15.918 4.11964 16.0662 4.22542 16.1714 4.36859C16.2767 4.51177 16.3334 4.68483 16.3333 4.86252V6.33335H18C18.221 6.33335 18.433 6.42115 18.5893 6.57743C18.7455 6.73371 18.8333 6.94567 18.8333 7.16669V13.8334C18.8333 14.0544 18.7455 14.2663 18.5893 14.4226C18.433 14.5789 18.221 14.6667 18 14.6667L15.3167 14.6675C14.9942 15.0925 14.6025 15.4675 14.15 15.7759L9.66667 18.8334L5.18333 15.7767C4.51044 15.3179 3.95979 14.7017 3.57928 13.9816C3.19877 13.2616 2.99992 12.4594 3 11.645V4.86252C3.0001 4.68497 3.05691 4.5121 3.16214 4.36909C3.26736 4.22609 3.41552 4.12043 3.585 4.06752L9.66667 2.16669ZM9.66667 3.91169L4.66667 5.47502V11.645C4.66655 12.1552 4.78354 12.6586 5.00864 13.1165C5.23373 13.5743 5.56092 13.9744 5.965 14.2859L6.1225 14.3992L9.66667 16.8167L12.8183 14.6667H8.83333C8.61232 14.6667 8.40036 14.5789 8.24408 14.4226C8.0878 14.2663 8 14.0544 8 13.8334V7.16669C8 6.94567 8.0878 6.73371 8.24408 6.57743C8.40036 6.42115 8.61232 6.33335 8.83333 6.33335H14.6667V5.47502L9.66667 3.91169ZM9.66667 10.5V13H17.1667V10.5H9.66667ZM9.66667 8.83335H17.1667V8.00002H9.66667V8.83335Z"
        fill="#0D3674"
      />
    </Svg>
  );
};

export default SecureIcon;
