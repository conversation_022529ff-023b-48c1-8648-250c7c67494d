import React from 'react';
import { Modal, Pressable, View } from 'react-native';
import BankIcon from '../../../atoms/Icons/BankIcon';
import { Button, Text } from '../../../atoms';
import { styles } from './styles';
import { Account } from '../../../../types/Account';
import CopyIcon from '../../../atoms/Icons/CopyIcon';
import { useCopyCustomToast } from '../../../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';
import color from '../../../../theme/pallets/pallet';

type ItemProps = {
  label: string;
  value: string;
  icon?: JSX.Element;
};

const Item = ({ label, value, icon }: ItemProps) => {
  const { showToast, toastConfig } = useCopyCustomToast();

  return (
    <View style={styles.containerItem}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.flexRow}>
        <Text style={styles.value}>{value}</Text>
        {icon && <Pressable onPress={() => showToast(value)}>{icon}</Pressable>}
      </View>
      <Toast config={toastConfig} />
    </View>
  );
};

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  navigateToAmount: () => void;
  accountData?: Account;
};

export const InfoAccountModal = ({
  modalVisible,
  setModalVisible,
  navigateToAmount,
  accountData,
}: Props) => {
  const { titulares, cvu, alias } = accountData || {};
  const Items = [
    // {
    //   label: 'Banco',
    //   value: 'Banco Nombre',
    // },
    {
      label: 'CBU/CVU',
      value: cvu,
      icon: <CopyIcon size={24} color={color.PRIMARY_500} />,
    },
    {
      label: 'Alias',
      value: alias,
    },
  ];
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <View style={styles.overlay}>
        <View style={styles.modalView}>
          <View>
            <View style={styles.containerAccount}>
              <BankIcon />
              <View style={styles.account}>
                <Text variant="B5" numberOfLines={2}>
                  {titulares && titulares[0].nombre}
                </Text>
                <Text variant="R6" color="NEUTRALS_600">
                  CUIT/CUIL: {titulares && titulares[0].cuit}
                </Text>
              </View>
              <Pressable
                onPress={() => setModalVisible(false)}
                style={styles.close}
              >
                <Text style={styles.textClose}>Cerrar</Text>
              </Pressable>
            </View>
            {Items.map((item, i) => (
              <Item
                label={item.label}
                value={item.value!}
                key={i}
                icon={item.icon}
              />
            ))}
          </View>
          <View style={styles.mt24}>
            <Button onPress={navigateToAmount} text="Continuar" />
          </View>
        </View>
      </View>
    </Modal>
  );
};
