import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const TransferIcon: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 21" fill="none">
      <Path
        d="M5.825 9.66669L2.5 13L5.825 16.3334V13.8334H11.6667V12.1667H5.825V9.66669ZM17.5 8.00002L14.175 4.66669V7.16669H8.33333V8.83335H14.175V11.3334L17.5 8.00002Z"
        fill={color}
      />
    </Svg>
  );
};

export default TransferIcon;
