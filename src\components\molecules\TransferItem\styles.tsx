import { StyleSheet } from 'react-native';
import { typograpy } from '../../../constants/typography';

export const styles = StyleSheet.create({
  separator: {
    borderBottomWidth: 0.5,
    borderBottomColor: '#E0E0E0',
    marginBottom: 10,
  },
  container: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },

  description: {
    ...typograpy.text,
  },
  amountNeutral: {
    ...typograpy.priceNeutral,
  },
  amountPositive: {
    ...typograpy.pricePositive,
  },
  amountNegative: {
    ...typograpy.priceNegative,
  },
  mr8: {
    marginRight: 8,
  },
  containerRow: {
    flex: 1,
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 12,
  },
  row: {
    flex: 1,
    justifyContent: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 12,
  },
  icon: {
    marginTop: 2,
  },
  separatorDate: {
    borderBottomWidth: 5,
    borderBottomColor: 'blue',
  },
  modalContainer: {
    backgroundColor: 'blue',
    height: '50%',
    bottom: 0,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  txtTransfer: {
    letterSpacing: 0.5,
  },
});
