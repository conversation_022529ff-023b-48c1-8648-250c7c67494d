import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const CrossIcon: FC<IconProps> = () => {
  return (
    <Svg width="18" height="17" viewBox="0 0 18 17" fill="none">
      <Path
        d="M9.7507 8.92435L12.3181 11.4917L11.9916 11.8182L9.42422 9.25082L8.99996 8.82655L8.5757 9.25082L6.00829 11.8182L5.68182 11.4917L8.24922 8.92435L8.67349 8.50008L8.24922 8.07582L5.68182 5.50841L6.00829 5.18194L8.5757 7.74935L8.99996 8.17361L9.42422 7.74935L11.9916 5.18194L12.3181 5.50841L9.7507 8.07582L9.32643 8.50008L9.7507 8.92435ZM1.26663 8.50008C1.26663 4.22312 4.723 0.766748 8.99996 0.766748C13.2769 0.766748 16.7333 4.22312 16.7333 8.50008C16.7333 12.777 13.2769 16.2334 8.99996 16.2334C4.723 16.2334 1.26663 12.777 1.26663 8.50008ZM1.73329 8.50008C1.73329 12.5065 4.99359 15.7667 8.99996 15.7667C13.0063 15.7667 16.2666 12.5065 16.2666 8.50008C16.2666 4.49371 13.0063 1.23341 8.99996 1.23341C4.99359 1.23341 1.73329 4.49371 1.73329 8.50008Z"
        fill="black"
        stroke="#FF0033"
        strokeWidth="1.2"
      />
    </Svg>
  );
};

export default CrossIcon;
