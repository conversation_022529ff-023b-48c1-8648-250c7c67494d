import React, { useContext, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { InfoAccount } from '../../components/organisms/Cards/InfoAccount';
import HeaderTransfer from '../../components/organisms/Headers/Transfer';
import ButtonWithIcon from '../../components/atoms/ButtonWithIcon';
import SecureIcon from '../../components/atoms/Icons/SecureIcon';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { LoaderModal } from '../../components/organisms/Modals/Loader';
import { api } from '../../services/apiService';
import { formatCurrency } from '../../helpers/formatCurrency';
import { AuthContext } from '../../context/AuthContext';
import { useBiometrics } from '../../hooks/useBiometrics';
import { storage } from '../../lib/mmkv';
import { promptBiometrics } from '../../lib/biometrics';
import color from '../../theme/pallets/pallet';
import SwipeToContinue from '../../components/organisms/Buttons/SwipeToContinue/SwipeToContinue';
import { Text } from '../../components/atoms';
import { SwipeButton } from 'mobile-core';
import DoubleArrowRight from '../../components/atoms/Icons/DoubleArrowRight';

type Props = NativeStackScreenProps<any>;

export const ConfirmScreen = ({ navigation, route }: Props) => {
  const { userData, disableScreenLock } = useContext(AuthContext);
  const { nombre, banco, amount, motive, cuit, cvu, balance }: any =
    route.params;
  const [showModal, setShowModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { available } = useBiometrics();
  const unlockMethodType = storage.getString('unlockMethodType');
  disableScreenLock();

  const handleTransfer = async () => {
    setIsLoading(true);

    if (
      (available && unlockMethodType === 'Biometrics') ||
      unlockMethodType === 'FaceID' ||
      unlockMethodType === 'TouchID'
    ) {
      const success = await promptBiometrics(unlockMethodType);
      if (!success) {
        setShowModal(false);
        setIsLoading(false);
        return;
      }
    }
    try {
      setShowModal(true);
      const response = await api.post('/digital/transactions/cashout', {
        userId: userData,
        cuit,
        cvu,
        name: nombre,
        importe: Number(amount),
      });
      if (response.status === 200) {
        setShowModal(false);
        navigation.navigate('SuccessScreen', {
          amount,
          nombre,
          motive,
          numTransaccion: response.data.data.senderTransactionId,
          cuit,
          cvu,
        });
      }
    } catch (error: any) {
      console.error(error.response.data.errors);
      console.error(error);
      navigation.navigate('ErrorScreen');
      setShowModal(false);
      setIsLoading(false);
    }
  };

  return (
    <>
      <View style={styles.container}>
        <HeaderTransfer text="Transferir" />
        <View style={styles.header}>
          <InfoAccount nombre={nombre} banco={banco} />
          <Text variant="R7">Importe</Text>
          <Text variant="R1">${formatCurrency(Number(amount))}</Text>
          {/* <View style={styles.infoContainer}>
          <Text style={styles.labelCard}>Medio de pago</Text>
          <View style={styles.card}>
            <Text style={styles.label}>De mi billetera Toshify</Text>
            <Text style={styles.txt}>
              Saldo disponible: ${formatCurrency(balance)}
            </Text>
          </View>

          <Text style={styles.labelCard}>Motivo</Text>
          <View style={styles.card}>
            <Text style={styles.motive}>{motive}</Text>
          </View>
        </View> */}
        </View>
        <LoaderModal modalVisible={showModal} />

        {/* <View style={styles.button}>
        <ButtonWithIcon
          bgColor="#FDC228"
          color="#0D3674"
          icon={<SecureIcon size={20} />}
          text="Transferir ahora"
          onPress={handleTransfer}
        />
      </View> */}
      </View>
      <View
        style={{
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderWidth: 1,
          borderColor: color.NEUTRALS_200,
          padding: 16,
          gap: 16,
          elevation: 0.5,
        }}
      >
        <View>
          <View
            style={{ flexDirection: 'row', justifyContent: 'space-between' }}
          >
            <View>
              <Text variant="B6">Retirarss </Text>
              <Text variant="R7">de mi saldo </Text>
            </View>
            <Text variant="B2">${formatCurrency(Number(amount))}</Text>
          </View>
        </View>
        {/* <SwipeToContinue
          onSwipeComplete={handleTransfer}
          isLoading={isLoading}
        /> */}
        <SwipeButton
          onSwipeComplete={() => console.log('Acción confirmada')}
          label="Desliza para pagar"
          isLoading={false}
          thumbIcon={<DoubleArrowRight />}
          trackColor="#222"
          fillColor="#4CAF50"
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  button: {
    marginVertical: 32,
    width: '100%',
  },
  textInput: {
    fontSize: 36,
    color: color.TEXT_PRIMARY,
    textAlign: 'center',
  },
  label: {
    fontSize: 12,
    fontFamily: 'Satoshi-Bold',
    color: color.NEUTRALS_800,
  },
  txt: {
    fontSize: 12,
    fontFamily: 'Satoshi-Regular',
    color: '#78838D',
  },
  infoContainer: {
    alignSelf: 'flex-start',
    marginTop: 54,
    width: '100%',
  },
  labelCard: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 12,
    color: color.TEXT_PRIMARY,
  },
  card: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    borderRadius: 8,
    marginTop: 4,
    marginBottom: 24,
  },
  motive: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
});
