import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const ListIcon: FC<IconProps> = ({ color = '#FF0033', size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M15.8334 18.3333H4.16675C3.50371 18.3333 2.86782 18.0699 2.39898 17.6011C1.93014 17.1322 1.66675 16.4963 1.66675 15.8333V2.49996C1.66675 2.27895 1.75455 2.06698 1.91083 1.9107C2.06711 1.75442 2.27907 1.66663 2.50008 1.66663H14.1667C14.3878 1.66663 14.5997 1.75442 14.756 1.9107C14.9123 2.06698 15.0001 2.27895 15.0001 2.49996V12.5H18.3334V15.8333C18.3334 16.4963 18.07 17.1322 17.6012 17.6011C17.1323 18.0699 16.4965 18.3333 15.8334 18.3333ZM15.0001 14.1666V15.8333C15.0001 16.0543 15.0879 16.2663 15.2442 16.4225C15.4004 16.5788 15.6124 16.6666 15.8334 16.6666C16.0544 16.6666 16.2664 16.5788 16.4227 16.4225C16.579 16.2663 16.6667 16.0543 16.6667 15.8333V14.1666H15.0001ZM13.3334 16.6666V3.33329H3.33341V15.8333C3.33341 16.0543 3.42121 16.2663 3.57749 16.4225C3.73377 16.5788 3.94573 16.6666 4.16675 16.6666H13.3334ZM5.00008 5.83329H11.6667V7.49996H5.00008V5.83329ZM5.00008 9.16663H11.6667V10.8333H5.00008V9.16663ZM5.00008 12.5H9.16675V14.1666H5.00008V12.5Z"
        fill={color}
      />
    </Svg>
  );
};

export default ListIcon;
