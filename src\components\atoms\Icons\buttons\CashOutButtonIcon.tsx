import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const CashOutButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#FFD6D6" />

      <Path
        d="M8.5 21.8333H23.5V23.5H8.5V21.8333ZM16.8333 16.9767L21.8925 11.9167L23.0708 13.095L16 20.1667L8.92917 13.0958L10.1075 11.9167L15.1667 16.975V7.66667H16.8333V16.9767Z"
        fill="#B83232"
      />
    </Svg>
  );
};

export default CashOutButtonIcon;
