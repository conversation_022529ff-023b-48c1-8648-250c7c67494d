import { AxiosRequestConfig } from 'axios';
import React, { createContext, FC, useEffect, useState } from 'react';
import { TOKEN, REFRESH_TOKEN, USER_ID } from '../constants/asyncStorageKeys';
import { api } from '../services/apiService';
import { getData, setData } from '../services/asyncStorageService';
import { deletAccessData } from '../helpers/authHelper';
import { useNavigation } from '@react-navigation/native';
import {
  postFcmToken,
  subscribeToTopic,
  unsubscribeFromTopic,
} from '../services/notifications';
import { checkCredentials } from '../helpers/encryptedPassword';
import { removeCredentials } from '../helpers/asyncStorage';
import { storage } from '../lib/mmkv';
import { useBackgroundState } from '../hooks/useBackgroundState';

type Props = {
  children: JSX.Element;
};

type storeValues = {
  authenticated?: boolean | null;
  signIn: (email: string, password: string) => void;
  signOut: () => void;
  userData?: string;
  error: boolean;
  setError: (error: boolean) => void;
  disableScreenLock: () => void;
  isInBackground: boolean;
  enableScreenLock: () => void;
  isActive?: boolean;
  setIsActive?: (isActive: boolean) => void;
  checkBiometricsAndSignOut?: () => void;
  reauthWithBiometrics?: boolean | null;
};

const initialState: storeValues = {
  authenticated: null,
  signIn: () => null,
  signOut: () => null,
  userData: '',
  error: false,
  setError: () => null,
  disableScreenLock: () => null,
  isInBackground: false,
  enableScreenLock: () => null,
  isActive: false,
  setIsActive: () => null,
  checkBiometricsAndSignOut: () => null,
  reauthWithBiometrics: null,
};

const AuthContext = createContext(initialState);
const { Provider } = AuthContext;

const AuthProvider: FC<Props> = ({ children }) => {
  const [authenticated, setAuthenticated] = useState(false);
  const [reauthWithBiometrics, setReauthWithBiometrics] = useState(false);
  const [userData, setUserData] = useState<string>('');
  const [error, setError] = useState<boolean>(false);
  const [isActive, setIsActive] = useState<boolean>(false);
  const navigation: any = useNavigation();
  const { disableScreenLock, isInBackground, enableScreenLock } =
    useBackgroundState(authenticated);

  useEffect(() => {
    api.interceptors.request.use(
      async (config: AxiosRequestConfig) => {
        const token = await getData(TOKEN);
        if (config.headers) {
          if (token) {
            setAuthenticated(true);
            config.headers['Authorization'] = `Bearer ${token}`;
          }
          config.headers['Content-Type'] = 'application/json';
        }
        return config;
      },
      function (error) {
        return Promise.reject(error);
      },
    );

    (async () => {
      try {
        const token = await getData(TOKEN);
        const accessToken = await getData(REFRESH_TOKEN);
        const userId = await getData(USER_ID);
        if (token || accessToken) {
          setAuthenticated(true);
          setUserData(userId!);
        }
      } catch (err) {
        console.log('error: ', err);
      }
    })();
  }, []);

  const signOut = async () => {
    console.log('Sign Out');
    try {
      await removeCredentials();
      await unsubscribeFromTopic();
      await deletAccessData();
      storage.delete('unlockMethodType');

      setAuthenticated(false);
      setUserData('');
      navigation.navigate('Login');
    } catch (err) {
      console.error('error: ', err);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { data } = await api.post('/auth/signin', {
        username: email,
        password: password,
      });

      setData(REFRESH_TOKEN, data.auth.RefreshToken);
      setData(TOKEN, data.auth.AccessToken);
      setData(USER_ID, data.userId);

      if (data.userId) {
        await postFcmToken();
        await subscribeToTopic();
      }

      setAuthenticated(true);
      setIsActive(true);
      setUserData(data.userId);
      setReauthWithBiometrics(false);
      api.defaults.headers['Authorization'] = `Bearer ${data.auth.AccessToken}`;

      checkCredentials(email, password);
      return { data };
    } catch (err: any) {
      console.log('🚀 ~ signIn ~ err:', err);
      return { err };
    }
  };

  const checkBiometricsAndSignOut = () => {
    const biometricsEnable =
      storage.getString('unlockMethodType') !== undefined;

    if (biometricsEnable) {
      setReauthWithBiometrics(true);
      navigation.navigate('BlockingBiometricsScreen');
    } else {
      signOut();
    }
  };

  const value = {
    authenticated,
    signIn,
    signOut,
    userData,
    error,
    setError,
    disableScreenLock,
    enableScreenLock,
    isInBackground,
    isActive,
    setIsActive,
    checkBiometricsAndSignOut,
    reauthWithBiometrics,
  };

  return <Provider value={value}>{children}</Provider>;
};

export { AuthContext, AuthProvider };
