import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const QrIconButoon: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 25 24" fill={color}>
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.5 6H10.34V9.84005H6.5V6Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M6.49988 13.6804H10.3399V17.5204H6.49988V13.6804Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M18.02 9.84018H14.1799V6.00013H18.02V9.84018Z"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M16.1338 15.5669C16.1526 15.5857 16.1526 15.6161 16.1338 15.6348C16.1151 15.6536 16.0847 15.6536 16.0659 15.6348C16.0472 15.6161 16.0472 15.5857 16.0659 15.5669C16.0847 15.5482 16.1151 15.5482 16.1338 15.5669"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M14.2138 13.6468C14.2326 13.6656 14.2326 13.696 14.2138 13.7147C14.1951 13.7335 14.1647 13.7335 14.146 13.7147C14.1272 13.696 14.1272 13.6656 14.146 13.6468C14.1647 13.6281 14.1951 13.6281 14.2138 13.6468"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M14.2138 17.487C14.2326 17.5058 14.2326 17.5362 14.2138 17.5549C14.1951 17.5736 14.1647 17.5736 14.146 17.5549C14.1272 17.5362 14.1272 17.5058 14.146 17.487C14.1647 17.4683 14.1951 17.4683 14.2138 17.487"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M18.0539 17.487C18.0727 17.5058 18.0727 17.5362 18.0539 17.5549C18.0352 17.5736 18.0048 17.5736 17.986 17.5549C17.9673 17.5362 17.9673 17.5058 17.986 17.487C18.0048 17.4683 18.0352 17.4683 18.0539 17.487"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M18.0539 13.6468C18.0727 13.6656 18.0727 13.696 18.0539 13.7147C18.0352 13.7335 18.0048 13.7335 17.986 13.7147C17.9673 13.696 17.9673 13.6656 17.986 13.6468C18.0048 13.6281 18.0352 13.6281 18.0539 13.6468"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <Path
        d="M3.5 6V4.5C3.5 3.67157 4.17157 3 5 3H6.5"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
      />
      <Path
        d="M21.5 6V4.5C21.5 3.67157 20.8284 3 20 3H18.5"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
      />
      <Path
        d="M3.5 18V19.5C3.5 20.3284 4.17157 21 5 21H6.5"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
      />
      <Path
        d="M21.5 18V19.5C21.5 20.3284 20.8284 21 20 21H18.5"
        stroke="white"
        stroke-width="1.5"
        stroke-linecap="round"
      />
    </Svg>
  );
};

export default QrIconButoon;
