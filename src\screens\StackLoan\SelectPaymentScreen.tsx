import React, { useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { Button } from '../../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import LoanTypeIcon from '../../components/atoms/Icons/LoanTypeIcon';
import { getLoanTypeTranslation } from '../../types/StackLoan/LoanTypes';
import { useSimulateLoan } from '../../hooks/Loans/useSimulateLoan';
import { SelectPayments } from '../../components/molecules/SelectQuotasCheckbox';
import { Payment } from '../../types/StackLoan/SimulateLoanResponse';
import { Skeleton } from '../../components/atoms/Skeleton';
import { formatCurrency } from '../../helpers/formatCurrency';
import color from '../../theme/pallets/pallet';

const SkeletonColor = 'rgba(132, 26, 10, 0.2)';

type Props = NativeStackScreenProps<any>;

export const SelectPaymentScreen = ({ navigation, route }: Props) => {
  const { loanTypeId, amount, loanType } = route.params;
  const [paymentSelected, setPaymentSelected] = useState<Payment>();
  const { simulationResult, loading, error } = useSimulateLoan(loanTypeId, amount);

  const navigateToResumeLoan = () => {
    navigation.navigate('StackLoan', {
      screen: 'LoanResumeScreen',
      params: {
        loanTypeId: loanTypeId,
        loanType: loanType,
        terms: paymentSelected?.term,
        amount: amount,
        monthlyPayment: paymentSelected?.monthlyPayment,
        totalPayment: paymentSelected?.totalPayment,
        firstPayment: simulationResult?.firstPayment,
        tna: simulationResult?.loanInfo.tna,
        tea: simulationResult?.loanInfo.tea,
        ctf: simulationResult?.loanInfo.ctf,
      }
    });
  };


  const PaymentSkeletons = () => {
    const PaymentSkeleton = () => <Skeleton highlightColor={SkeletonColor} width={340} height={30} borderRadius={10} />;

    return (
      <>
        <Skeleton highlightColor={SkeletonColor} width={200} height={25} borderRadius={10} />
        <PaymentSkeleton />
        <PaymentSkeleton />
        <PaymentSkeleton />
        <PaymentSkeleton />
        <PaymentSkeleton />
      </>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Simular préstamo</Text>
      <View style={styles.header}>
        <View style={styles.loanTypeContainer}>
          <LoanTypeIcon loanType={loanType} />
          <Text style={styles.loanType}>{getLoanTypeTranslation(loanType)}</Text>
        </View>

        <Text style={styles.txt}>$ {formatCurrency(Number(amount))}</Text>

        <View style={{ width: '100%', paddingTop: 20 }}>
          {loading && <PaymentSkeletons />}

          {
            !loading && !error &&
            <SelectPayments payments={simulationResult?.payments} setPaymentSelected={payment => setPaymentSelected(payment)} />
          }
        </View>
      </View>
      <View style={styles.button}>
        <Button text="Siguiente" onPress={navigateToResumeLoan} disabled={loading} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
    marginBottom: 16,
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
    width: "auto"
  },
  button: {
    marginVertical: 32,
    width: '100%',
  },
  txt: {
    fontSize: 36,
    fontFamily: 'Satoshi-Regular',
    color: color.BLACK,
    marginVertical: 8,
  },
  txtHeader: {
    fontSize: 15,
    fontFamily: 'Satoshi-Regular',
    color: color.BLACK,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  loanType: {
    fontSize: 16,
    fontWeight: "700",
    color: color.TEXT_PRIMARY
  },
  loanTypeContainer: {
    flexDirection: "row",
    gap: 8,
    alignItems: "center",
    marginVertical: 20
  }
});
