import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const PersonIcon: FC<IconProps> = () => {
  return (
    <Svg width="16" height="16" viewBox="0 0 14 14" fill="none">
      <Path
        d="M7.00001 1.99992C7.91668 1.99992 8.66668 2.74992 8.66668 3.66659C8.66668 4.58325 7.91668 5.33325 7.00001 5.33325C6.08334 5.33325 5.33334 4.58325 5.33334 3.66659C5.33334 2.74992 6.08334 1.99992 7.00001 1.99992ZM7.00001 10.3333C9.25001 10.3333 11.8333 11.4083 12 11.9999H2.00001C2.19168 11.3999 4.75834 10.3333 7.00001 10.3333ZM7.00001 0.333252C5.15834 0.333252 3.66668 1.82492 3.66668 3.66659C3.66668 5.50825 5.15834 6.99992 7.00001 6.99992C8.84168 6.99992 10.3333 5.50825 10.3333 3.66659C10.3333 1.82492 8.84168 0.333252 7.00001 0.333252ZM7.00001 8.66658C4.77501 8.66658 0.333344 9.78325 0.333344 11.9999V13.6666H13.6667V11.9999C13.6667 9.78325 9.22501 8.66658 7.00001 8.66658Z"
        fill="#535D66"
      />
    </Svg>
  );
};

export default PersonIcon;
