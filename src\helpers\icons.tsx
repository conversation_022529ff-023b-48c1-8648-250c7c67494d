import React from 'react';
import TransferOut from '../components/atoms/Icons/cashFlow/TransferOut';
import TransferIn from '../components/atoms/Icons/cashFlow/TransferIn';
import { View } from 'react-native';
import QrPay from '../components/atoms/Icons/cashFlow/QrPay';

export const getIcon = (type: string) => {
  let icon: React.JSX.Element = <></>;

  switch (type) {
    case 'TRANSFEROUT':
      icon = <TransferOut />;
      break;
    case 'TRANSFERIN':
      icon = <TransferIn />;
      break;
    case 'QRPAYMENT':
      icon = <QrPay />;
      break;
    default:
      icon = <></>;
  }
  return (
    <View
      style={{
        width: 40,
        height: 40,
        backgroundColor: '#EF6C0014',
        borderRadius: 40,
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      {icon}
    </View>
  );
};
