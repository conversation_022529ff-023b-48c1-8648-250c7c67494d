import React from 'react';
import { Text } from '../../atoms';
import { StyleSheet, View } from 'react-native';

type Props = {
  title: string;
  primaryColor: string;
};

function getBackgroundColorFromPrimary(hex: string, alpha = 0.1) {
  if (!hex) {
    return;
  }
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

export const ChipStateColor = ({ title, primaryColor }: Props) => {
  const backgroundColor = getBackgroundColorFromPrimary(primaryColor, 0.1);

  return (
    <View
      style={{
        ...styles.tokenBadge,
        backgroundColor,
      }}
    >
      <Text variant="R7" style={{ color: primaryColor }}>
        {title}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  tokenBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
});
