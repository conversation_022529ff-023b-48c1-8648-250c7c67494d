/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { Text } from '../../../atoms';
import color from '../../../../theme/pallets/pallet';
import { StyleSheet, View } from 'react-native';
import { ChipStateColor } from '../../../molecules/ChipStateColor/ChipStateColor';
import ArrowUpIcon from '../../../atoms/Icons/ArrowUpIcon';
import TrendingUpIcon from '../../../atoms/Icons/TrendingUpIcon';
import { IMyInvestment } from '../../../../types/Investments';
import { formatCurrency } from '../../../../helpers/formatCurrency';

export const InvestmentDetails = ({
  investment,
}: {
  investment: IMyInvestment | undefined;
}) => {
  return (
    <View>
      <Text variant="R6" style={{ fontFamily: 'Satoshi-Regular' }}>
        Detalle de la inversión
      </Text>
      <View style={styles.cardContainer}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <Text variant="B7" color="NEUTRALS_800" style={{ paddingRight: 3 }}>
              {investment?.name}
            </Text>
            <ChipStateColor
              title={investment?.state.name || ''}
              primaryColor={investment?.state.color || ''}
            />
          </View>
          <Text variant="R7" color={'NEUTRALS_600'}>
            {investment?.tokens.tokensAcquired} Tokens
          </Text>
        </View>
        <Text variant="B2">
          $ {formatCurrency(investment?.tokens.investedAmount || 0)}
        </Text>
        <View style={styles.iconTextContainer}>
          <TrendingUpIcon color={color.NEUTRALS_400} size={12} />
          <Text variant="R7" color="NEUTRALS_800">
            Ganancias estimadas:{' '}
            <Text variant="B7">{investment?.returnRangeMin}</Text> a{' '}
            <Text variant="B7">{investment?.returnRangeMax}</Text> % por mes
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    padding: 16,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
    borderRadius: 16,
    marginTop: 8,
  },
  iconTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    margin: 2,
  },
});
