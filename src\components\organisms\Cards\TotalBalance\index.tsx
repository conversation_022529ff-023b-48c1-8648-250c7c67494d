import React, { FC, useContext, useEffect, useState } from 'react';
import { View, TouchableOpacity, Pressable } from 'react-native';

import { styles } from './styles';
import {
  TransferIcon,
  CashInIcon,
  CashOutIcon,
  QrIcon,
  EyeIcon,
  EyeSlashIcon,
} from '../../../atoms/Icons';

import { formatCurrency } from '../../../../helpers/formatCurrency';
import { Skeleton } from './Skeleton';
import { Text, TextBase } from '../../../atoms';
import { InfoCvuModal } from '../../Modals/InfoCvu';
import { AuthContext } from '../../../../context/AuthContext';
import color from '../../../../theme/pallets/pallet';

const Separator = () => <View style={styles.separator} />;
type Props = {
  balance: number;
  loading: boolean;
  navigateToTransfer: () => void;
  navigateToCashIn: () => void;
  navigateToQr: () => void;
  user: any;
  cvuUser: any;
  cuitUser: any;
  onboardingStatus: any;
};

const TotalBalanceCard: FC<Props> = ({
  balance,
  loading,
  navigateToTransfer,
  navigateToCashIn,
  navigateToQr,
  user,
  cvuUser,
  cuitUser,
  onboardingStatus,
}) => {
  const amount: string = formatCurrency(balance);
  const quickAccessButtons = [
    {
      icon: CashInIcon,
      text: 'Ingresar',
      onPress: navigateToCashIn,
    },
    {
      icon: CashOutIcon,
      text: 'Retirar',
      onPress: () => console.log('Retirar'),
    },
    {
      icon: TransferIcon,
      text: 'Transferir',
      onPress: navigateToTransfer,
    },
    {
      icon: QrIcon,
      text: 'Pago QR',
      onPress: navigateToQr,
    },
  ];
  const isAmountZero = amount === '0';
  const [isModalVisible, setModalVisible] = useState(false);
  const handlePressCvu = () => {
    setModalVisible(true);
  };
  const [showValue, setShowValue] = useState(true);

  const { isInBackground } = useContext(AuthContext);
  useEffect(() => {
    isInBackground && setModalVisible(false);
  }, [isInBackground]);

  const approved = onboardingStatus === 'approved';
  const pending =
    onboardingStatus === 'pending' || onboardingStatus === 'to check';
  const rejected =
    onboardingStatus === 'rejected' ||
    onboardingStatus === 'cancelled' ||
    onboardingStatus === 'not verified' ||
    onboardingStatus === 'in progress';
  return (
    <View style={styles.container}>
      <View>
        <View style={styles.textTop}>
          <Text variant="R6" color={'NEUTRALS_800'}>
            Tu inversión
          </Text>
          {/* {loading ? (
            <Skeleton />
          ) : (
            <>
              {approved && (
                <Pressable onPress={handlePressCvu}>
                  <TextBase style={styles.cvuText}>Tu CVU</TextBase>
                </Pressable>
              )}
            </>
          )} */}
        </View>
        {loading ? (
          <Skeleton />
        ) : (
          <View>
            <View style={{ ...styles.flexRow, ...styles.itemsCenter }}>
              <>
                <Text color={'NEUTRALS_800'} variant="B1">
                  ${' '}
                  {isAmountZero
                    ? '0'
                    : showValue
                      ? amount.split(',')[0]
                      : '***'}
                </Text>
                <Text style={styles.mt18} color={'NEUTRALS_800'} variant="R4">
                  {isAmountZero
                    ? '00'
                    : showValue
                      ? amount.split(',')[1]
                      : '**'}
                </Text>
              </>
              <View style={{ marginLeft: 10 }}>
                <TouchableOpacity onPress={() => setShowValue(!showValue)}>
                  {showValue ? (
                    <EyeIcon size={22} color={color.PRIMARY_500} />
                  ) : (
                    <EyeSlashIcon size={22} color={color.PRIMARY_500} />
                  )}
                </TouchableOpacity>
              </View>
            </View>
            <Text color={'NEUTRALS_400'} variant="R7">
              US$ {'0,00'}
            </Text>
          </View>
        )}
        <InfoCvuModal
          modalVisible={isModalVisible}
          setModalVisible={setModalVisible}
          user={user}
          cvuUser={cvuUser}
          cuitUser={cuitUser}
        />
      </View>
    </View>
  );
};

export default TotalBalanceCard;
