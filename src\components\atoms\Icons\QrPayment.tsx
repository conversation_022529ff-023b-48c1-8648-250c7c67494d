import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const QrPayment: FC<IconProps> = () => {
  return (
    <Svg width="32" height="32" viewBox="0 0 32 32" fill="none">
      <Rect width="32" height="32" rx="8" fill="#F0F6FF" />
      <Path
        d="M8.5 15.1667H15.1667V8.5H8.5V15.1667ZM10.1667 10.1667H13.5V13.5H10.1667V10.1667Z"
        fill="#0068FF"
      />
      <Path
        d="M8.5 23.5H15.1667V16.8333H8.5V23.5ZM10.1667 18.5H13.5V21.8333H10.1667V18.5Z"
        fill="#0068FF"
      />
      <Path
        d="M16.8333 8.5V15.1667H23.5V8.5H16.8333ZM21.8333 13.5H18.5V10.1667H21.8333V13.5Z"
        fill="#0068FF"
      />
      <Path d="M23.5 21.8333H21.8333V23.5H23.5V21.8333Z" fill="#0068FF" />
      <Path d="M18.5 16.8333H16.8333V18.5H18.5V16.8333Z" fill="#0068FF" />
      <Path d="M20.1667 18.5H18.5V20.1667H20.1667V18.5Z" fill="#0068FF" />
      <Path d="M18.5 20.1667H16.8333V21.8333H18.5V20.1667Z" fill="#0068FF" />
      <Path d="M20.1667 21.8333H18.5V23.5H20.1667V21.8333Z" fill="#0068FF" />
      <Path
        d="M21.8333 20.1667H20.1667V21.8333H21.8333V20.1667Z"
        fill="#0068FF"
      />
      <Path d="M21.8333 16.8333H20.1667V18.5H21.8333V16.8333Z" fill="#0068FF" />
      <Path d="M23.5 18.5H21.8333V20.1667H23.5V18.5Z" fill="#0068FF" />
    </Svg>
  );
};

export default QrPayment;
