import React, { useRef, useState } from 'react';
import { View, StyleSheet, Image, ScrollView, Pressable } from 'react-native';
import { Button, Text } from '../../components/atoms';
import color from '../../theme/pallets/pallet';
import { FlashList } from '@shopify/flash-list';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import { CarsStackParams } from '../../navigation/stack-cars/types';
import CalendarIcon from '../../components/atoms/Icons/CalendarIcon';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import CheckIcon from '../../components/atoms/Icons/CheckIcon';

const GeneralDescription = () => (
  <View style={styles.earningsContainer}>
    <View style={styles.earningsContainer}>
      <View
        style={{
          backgroundColor: color.PRIMARY_700,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          paddingHorizontal: 16,
          paddingVertical: 8,
        }}
      >
        <Text variant="B6" color="WHITE">
          Ganancia mensual estimada
        </Text>
        <Text variant="R7" color="WHITE">
          Cobras{' '}
          <Text variant="B7" color="WHITE">
            {' '}
            ARS 90.000 - 98.000
          </Text>{' '}
          cada mes
        </Text>
      </View>
      <View style={styles.earningsDetails}>
        <View style={styles.detailItem}>
          <Text variant="R7">Inversión total</Text>
          <Text variant="B4" color="PRIMARY_500">
            4 tokens
          </Text>
        </View>
        <View style={styles.detailItem}>
          <Text variant="R7">Rendimiento mensual</Text>
          <Text variant="B4" color="PRIMARY_500">
            4,2 - 4,9 %
          </Text>
        </View>
      </View>
      <View
        style={{
          flexDirection: 'row',
          gap: 8,
          marginHorizontal: 16,
          marginBottom: 16,
        }}
      >
        <View style={{ marginTop: 8 }}>
          <CalendarIcon size={16} color={color.NEUTRALS_400} />
        </View>
        <View>
          <Text variant="B6">Ganancias mensuales:</Text>
          <Text variant="R7">Los pagos se hacen los días 5 de cada mes.</Text>
        </View>
      </View>
    </View>
  </View>
);

const Activity = () => (
  <FlashList
    data={[
      {
        id: '1',
        name: 'Pago de renta',
        detail: 'Hoy',
        amount: '+$ 90.000',
      },
      {
        id: '2',
        name: 'Inversion',
        detail: 'Hoy',
        amount: '-$ 10.000',
      },
    ]}
    keyExtractor={(item, index) => item.id + index}
    renderItem={({ item }) => (
      <View
        style={{
          borderBottomColor: color.NEUTRALS_100,
          borderBottomWidth: 1,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          paddingVertical: 16,
        }}
      >
        <View>
          <Text variant="B7">{item.name}</Text>
          <Text variant="R7">{item.detail}</Text>
        </View>
        <View>
          <Text
            variant="R7"
            color={item.amount.includes('+') ? 'GREEN_700' : 'NEUTRALS_800'}
          >
            {item.amount}
          </Text>
        </View>
      </View>
    )}
    estimatedItemSize={300}
    onEndReached={() => { }}
    onEndReachedThreshold={0.1}
    ListFooterComponent={<View />}
    refreshControl={<View />}
  />
);

const steps = [
  { label: 'Token', completed: true },
  { label: 'Compra', completed: true },
  { label: 'Retiro', completed: true },
  { label: 'En marcha', completed: true, active: true },
];

const Status = () => (
  <View
    style={{
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      paddingVertical: 20,
    }}
  >
    {steps.map((step, index) => (
      <View key={index} style={styles.stepContainer}>
        {/* Línea de progreso */}
        {index !== 0 && <View style={styles.line} />}

        {/* Icono con check */}
        <View
          style={[
            styles.circle,
            step.completed && styles.circleCompleted,
            step.active && styles.circleActive,
          ]}
        >
          <View style={{ position: 'absolute', top: 2, left: 3 }}>
            <CheckIcon
              size={20}
              color={!step.active ? color.NEUTRALS_800 : color.GREEN_700}
            />
          </View>
        </View>

        {/* Etiqueta */}
        <Text
          style={[
            styles.label,
            step.active && styles.activeLabel,
            step.completed && !step.active && styles.completedLabel,
          ]}
        >
          {step.label}
        </Text>
      </View>
    ))}
  </View>
);

type Props = NativeStackScreenProps<CarsStackParams, 'CarsDetailScreen'>;
export default function CarDetailsScreen({ navigation }: Props) {
  const [activeTab, setActiveTab] = useState<number>(0);

  const tabs = ['Descripción general', 'Actividad', 'Estado'];
  const scrollViewRef = useRef<ScrollView>(null);

  const handleTabPress = (index: number) => {
    setActiveTab(index);

    // Asegúrate de centrar la pestaña activa
    scrollViewRef.current?.scrollTo({
      x: index * 100 - (150 - 100), // Ajusta el cálculo según el tamaño de la pestaña
      animated: true,
    });
  };

  const handleNavigateToAmount = (type: string) => {
    navigation.navigate('CarsAmountScreen', { type });
  };

  return (
    <View
      style={{
        paddingHorizontal: 16,
        gap: 24,
        flex: 1,
        justifyContent: 'space-between',
      }}
    >
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Text variant="B2">Fiat Cronos</Text>
        <Text variant="R7" color="GREEN_700">
          En marcha
        </Text>
      </View>
      <Image
        source={require('../../assets/ilustrations/fiat-cronos/fiat-cronos.png')}
        style={{ alignSelf: 'center' }}
      />
      <View
        style={{
          borderRadius: 16,
          backgroundColor: color.NEUTRALS_50,
          padding: 16,
        }}
      >
        <Text variant="R7" color="NEUTRALS_800">
          Tu inversion
        </Text>
        <Text variant="B5">$ 2.001.451,04</Text>
        <Text variant="R7" color="GREEN_700">
          $ 1.451,04
        </Text>
      </View>
      <View>
        <ScrollView
          ref={scrollViewRef}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.scrollContainer}
        >
          {tabs.map((tab, index) => (
            <Pressable
              key={index}
              onPress={() => handleTabPress(index)}
              style={styles.tabButton}
            >
              <Text
                variant={activeTab === index ? 'B6' : 'R6'}
                color={activeTab === index ? 'PRIMARY_700' : 'NEUTRALS_800'}
              >
                {tab}
              </Text>
              {activeTab === index && <View style={styles.underline} />}
            </Pressable>
          ))}
        </ScrollView>
      </View>
      <ScrollView contentContainerStyle={{ gap: 8 }}>
        {activeTab === 0 && <GeneralDescription />}
        {activeTab === 1 && <Activity />}
        {activeTab === 2 && <Status />}
      </ScrollView>
      <View
        style={{
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderWidth: 1,
          borderColor: color.NEUTRALS_200,
          padding: 16,
          marginHorizontal: -16,
          gap: 16,
        }}
      >
        <View>
          <View style={{ flexDirection: 'row', gap: 16 }}>
            <View style={{ flex: 1 }}>
              <ButtonOutline
                text="Vender"
                onPress={() => handleNavigateToAmount('sell')}
              />
            </View>
            <View style={{ flex: 1 }}>
              <Button
                text="Comprar más"
                onPress={() => handleNavigateToAmount('buy')}
              />
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    flexDirection: 'row',
  },
  tabButton: {
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  activeTabText: {
    color: color.PRIMARY_700,
    fontWeight: 'bold',
  },
  underline: {
    marginTop: 4,
    height: 2,
    backgroundColor: color.PRIMARY_700,
    width: '100%',
  },
  earningsContainer: {
    borderRadius: 16,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
  },
  earningsDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    margin: 16,
  },
  detailItem: {
    alignItems: 'center',
    borderLeftWidth: 2,
    borderColor: color.PRIMARY_500,
    paddingLeft: 8,
  },

  divider: {
    width: 2,
    height: 40,
    backgroundColor: color.PRIMARY_500,
  },
  stepContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  circle: {
    width: 30,
    height: 30,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#ccc',
    // justifyContent: 'center',
    // alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  circleCompleted: {
    backgroundColor: color.NEUTRALS_100,
    borderColor: color.NEUTRALS_100,
  },
  circleActive: {
    backgroundColor: color.GREEN_50,
    borderColor: color.GREEN_50,
  },
  line: {
    position: 'absolute',
    top: 15,
    left: -30,
    width: 60,
    height: 2,
    backgroundColor: '#ccc',
    zIndex: -1,
  },
  label: {
    marginTop: 8,
    fontSize: 12,
    color: '#aaa',
  },
  activeLabel: {
    color: '#000',
    fontWeight: 'bold',
  },
  completedLabel: {
    color: '#ccc',
  },
});
