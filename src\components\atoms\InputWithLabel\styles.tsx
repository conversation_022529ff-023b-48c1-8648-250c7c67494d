import { StyleSheet } from 'react-native';
import color from '../../../theme/pallets/pallet';

type GetDynamicStyles = (
  isFocused: boolean,
  disabled?: boolean,
  numberOfLines?: number,
) => {
  input: {
    color: string;
    backgroundColor: string;
    borderColor: string;
  };
};

export const styles = StyleSheet.create({
  container: {
    gap: 6,
    flex: 1,
  },
  input: {
    textAlignVertical: 'top',
    borderWidth: 1,
    borderRadius: 4,
    padding: 12,
    fontSize: 14,
    fontFamily: 'Satoshi-Regular',
  },
});

export const getDynamicStyles: GetDynamicStyles = (
  isFocused,
  disabled,
  numberOfLines,
) => ({
  input: {
    color: disabled ? color.NEUTRALS_400 : '#333333',
    backgroundColor: disabled ? '#F7F8FE' : '#FFFFFF',
    borderColor: isFocused ? color.PRIMARY_500 : color.NEUTRALS_200,
    height: numberOfLines !== undefined && numberOfLines > 0 ? undefined : 45,
  },
});
