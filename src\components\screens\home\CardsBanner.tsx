import React, { <PERSON> } from 'react';
import {
  FlatList,
  ImageBackground,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import GetPrepaidCardBanner, {
  getPrepaidBannerSize,
} from '../../atoms/Icons/Home/GetPrepaidCardBanner';
import { TextBase } from '../../atoms';
import LinearGradient from 'react-native-linear-gradient';
import VisaIcon from '../../atoms/Icons/shared/cards/VisaIcon';
import { ArrowRight } from '../../atoms/Icons';
import MasterIcon from '../../atoms/Icons/shared/cards/MasterIcon';
import {
  Card,
  CardProviders,
  getProductTypeTranslate,
} from '../../../types/StackCards/Cards';
import color from '../../../theme/pallets/pallet';

type BannerProps = {
  activateCard?: boolean;
  title: string;
  subtitle: string;
  buttonText?: string;
  navigateToRequestCardScreen: () => void;
};

interface Props {
  cards?: Card[];
  navigateToRequestCardScreen: () => void;
}

interface SimpleCardProps {
  backgroundColors: string[];
  data: Card;
  onPress: () => void;
}

const SimpleCard: FC<SimpleCardProps> = ({
  backgroundColors,
  data,
  onPress,
}) => {
  const { width } = useWindowDimensions();

  const CardProviderIcon: FC<{ provider: string }> = ({ provider }) => {
    switch (provider) {
      case CardProviders.MASTERCARD:
        return <MasterIcon size={64} />;
      case CardProviders.VISA:
        return <VisaIcon size={64} />;
      default:
        return null;
    }
  };

  return (
    <Pressable onPress={onPress}>
      <LinearGradient
        colors={backgroundColors}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={[styles.simpleCard, { width: width - 35 }]}
      >
        <View style={{ flexDirection: 'row', gap: 10, alignItems: 'center' }}>
          <CardProviderIcon provider={data.provider} />
          <TextBase size="s" color={color.WHITE}>
            {getProductTypeTranslate(data.product_type)}
          </TextBase>
        </View>
        <View style={{ flexDirection: 'row', gap: 10, alignItems: 'center' }}>
          <TextBase type="Bold" color={color.WHITE}>
            **** {data.last_four}
          </TextBase>
          <ArrowRight size={16} color={color.WHITE} />
        </View>
      </LinearGradient>
    </Pressable>
  );
};

export const Banner: FC<BannerProps> = ({
  activateCard,
  title,
  subtitle,
  buttonText = 'Pedir tarjeta',
  navigateToRequestCardScreen,
}) => {
  const { width, height } = getPrepaidBannerSize();

  return (
    <TouchableOpacity
      style={
        activateCard
          ? {
              marginLeft: 16,
              width: 380,
              height: 150,
              borderRadius: 10,
              overflow: 'hidden',
              marginVertical: 16,
            }
          : { width: width / 1.8, height: height }
      }
      onPress={navigateToRequestCardScreen}
    >
      {activateCard ? (
        <ImageBackground
          source={require('../../../assets/ilustrations/banner-activate-card/banner-activate-card.png')}
          style={{ flex: 1 }}
          resizeMode="cover"
        />
      ) : (
        <GetPrepaidCardBanner width={width} height={height} />
      )}
      <View
        style={[
          styles.bannerContentContainer,
          {
            padding: activateCard ? 16 : 36,
            paddingTop: activateCard ? 24 : 36,
            width: activateCard ? '70%' : '100%',
          },
        ]}
      >
        <TextBase type="Bold" color={activateCard ? color.BLACK : color.WHITE}>
          {title}
        </TextBase>
        <TextBase
          size="s"
          color={activateCard ? color.NEUTRALS_800 : color.WHITE}
          style={{ letterSpacing: 0.5, lineHeight: 17 }}
        >
          {subtitle}
        </TextBase>
        <TouchableOpacity
          style={[
            styles.getCardButton,
            {
              backgroundColor: activateCard ? color.PRIMARY_700 : color.WHITE,
            },
          ]}
          onPress={navigateToRequestCardScreen}
        >
          <TextBase
            type="Bold"
            color={activateCard ? color.WHITE : color.PRIMARY_700}
            style={{ textAlign: 'center' }}
          >
            {buttonText}
          </TextBase>
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const CardsBanner: FC<Props> = ({ cards, navigateToRequestCardScreen }) => {
  const hasCards = cards !== undefined && cards.length > 0;

  if (!hasCards) {
    return (
      <Banner
        title="Tarjeta Prepaga"
        subtitle="Solicitala sin cargo y utiliza el dinero de tu cuenta donde quieras."
        navigateToRequestCardScreen={navigateToRequestCardScreen}
      />
    );
  }

  return (
    <View>
      <FlatList
        data={cards}
        keyExtractor={item => item.id}
        horizontal
        renderItem={({ item }) => (
          <SimpleCard
            backgroundColors={[color.PRIMARY_500, '#FF0033']}
            data={item}
            onPress={navigateToRequestCardScreen}
          />
        )}
        showsHorizontalScrollIndicator={false}
      />
    </View>
  );
};

export default CardsBanner;

const styles = StyleSheet.create({
  getCardButton: {
    width: 140,
    height: 30,
    paddingVertical: 2,
    borderRadius: 5,
    marginTop: 12,
  },
  bannerContentContainer: {
    position: 'absolute',
    gap: 4,
  },
  simpleCard: {
    flex: 1,
    height: 66,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    margin: 16,
    borderRadius: 16,
  },
});
