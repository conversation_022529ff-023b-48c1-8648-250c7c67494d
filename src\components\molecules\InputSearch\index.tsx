import React, { <PERSON> } from 'react';
import { View } from 'react-native';
import { Input } from '../../atoms';
import { InputProps } from '../../atoms/Input';
import SearchIcon from '../../atoms/Icons/SearchIcon';

interface InputFormProps extends InputProps {
  touched?: boolean;
}

const InputSearch: FC<InputFormProps> = ({
  text,
  setText,
  onBlur,
  placeholder,
  hideText,
}) => {
  return (
    <View>
      <Input
        icon={<SearchIcon size={19} />}
        text={text}
        setText={setText}
        onBlur={onBlur}
        placeholder={placeholder}
        hideText={hideText}
      />
    </View>
  );
};

export default InputSearch;
