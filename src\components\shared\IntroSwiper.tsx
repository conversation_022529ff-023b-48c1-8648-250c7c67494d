import React, { FC, useRef } from 'react';
import { StyleSheet, View } from 'react-native';
import TextBase from '../atoms/TextBase';
import Swiper from 'react-native-swiper';
import IconOne from '../atoms/Icons/IconOne';
import IconTwo from '../atoms/Icons/IconTwo';
import IconTree from '../atoms/Icons/IconTree';
import color from '../../theme/pallets/pallet';

interface Props {
  titleOne: string;
  subtitleOne: string;
  titleTwo: string;
  subtitleTwo: string;
  titleThree: string;
  subtitleThree: string;
}

const IntroSwiper: FC<Props> = ({
  titleOne,
  subtitleOne,
  titleTwo,
  subtitleTwo,
  titleThree,
  subtitleThree,
}) => {
  let swiperRef = useRef(null);

  return (
    <Swiper
      loop={false}
      showsButtons={false}
      dot={<View style={styles.paginationDot} />}
      ref={swiperRef}
      activeDot={<View style={styles.activeDot} />}
      containerStyle={styles.swiperContainer}
      autoplayTimeout={5}
    >
      {/* Página 1 */}
      <View style={styles.slide}>
        <View style={styles.content}>
          <IconOne height={250} width={328} />
          <TextBase style={styles.textHeader}>{titleOne}</TextBase>
          <TextBase style={styles.textDescription}>{subtitleOne}</TextBase>
        </View>
      </View>

      {/* Página 2 */}
      <View style={styles.slide}>
        <View style={styles.content}>
          <IconTwo height={250} width={328} />
          <TextBase style={styles.textHeader}>{titleTwo}</TextBase>
          <TextBase style={styles.textDescription}>{subtitleTwo}</TextBase>
        </View>
      </View>

      {/* Página 3 */}
      <View style={styles.slide}>
        <View style={styles.content}>
          <IconTree height={250} width={328} />
          <TextBase style={styles.textHeader}>{titleThree}</TextBase>
          <TextBase style={styles.textDescription}>{subtitleThree}</TextBase>
        </View>
      </View>
    </Swiper>
  );
};

const styles = StyleSheet.create({
  paginationDot: {
    backgroundColor: color.NEUTRALS_100,
    width: 8,
    height: 8,
    borderRadius: 4,
    margin: 5,
  },
  activeDot: {
    backgroundColor: color.PRIMARY_500,
    width: 10,
    height: 10,
    borderRadius: 10,
    margin: 5,
  },
  swiperContainer: {
    flex: 1,
  },

  slide: {
    flex: 1,
    alignItems: 'center',
    paddingBottom: 50, // espacio para los dots
  },

  content: {
    flex: 1,
    justifyContent: 'space-evenly',
    alignItems: 'center',
    width: '100%',
  },

  textHeader: {
    fontSize: 18,
    textAlign: 'center',
    width: '90%',
    letterSpacing: 0.5,
    color: color.TEXT_PRIMARY,
    fontFamily: 'Satoshi-Bold',
    marginBottom: 16,
  },
  textDescription: {
    fontSize: 14,
    textAlign: 'center',
    width: '90%',
    fontWeight: '300',
    letterSpacing: 0.5,
    color: color.NEUTRALS_800,
    fontFamily: 'Satoshi-Regular',
  },
});

export default IntroSwiper;
