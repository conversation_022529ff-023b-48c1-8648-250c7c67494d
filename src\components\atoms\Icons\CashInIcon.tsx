import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const CashInIcon: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 13 13">
      <Path
        d="M0.5 11.6666H12.5V13H0.5V11.6666ZM7.16667 7.78131L11.214 3.73331L12.1567 4.67598L6.5 10.3333L0.843333 4.67665L1.786 3.73331L5.83333 7.77998V0.333313H7.16667V7.78131Z"
        fill={color}
      />
    </Svg>
  );
};

export default CashInIcon;
