import React, { useState } from 'react';
import { Keyboard, View } from 'react-native';
import { stylesCountry } from './styles';
import { useNavigation } from '@react-navigation/native';
import SelectCountry from '../../components/molecules/SelectCountry';
import { Button, Text } from '../../components/atoms';

export const CountryScreen = () => {
  const [dropdownVisible, setDropdownVisible] = useState(false);
  const [country, setCountry] = useState('');
  const navigation: any = useNavigation();

  const keyboardDismiss = () => {
    Keyboard.dismiss();
  };

  const handleContinue = () => {
    navigation.navigate('NameScreen', { country: country });
  };

  return (
    <View style={stylesCountry.container}>
      <View>
        <View>
          <Text variant="B3">Crea una cuenta</Text>
          <Text variant="R6">Solo te llevará algunos minutos</Text>
        </View>
        <View>
          <SelectCountry
            setCountry={setCountry}
            keyboardDismiss={keyboardDismiss}
            dropdownVisible={dropdownVisible}
            setDropdownVisible={setDropdownVisible}
          />
        </View>
      </View>
      <View style={stylesCountry.button}>
        <Button text="Siguiente" onPress={handleContinue} />
      </View>
    </View>
  );
};
