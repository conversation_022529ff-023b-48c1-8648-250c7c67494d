import Svg, { Path } from 'react-native-svg'
import { LoanIconTypeColor } from './types';
import { FC } from 'react';

const HouseRounded: FC<LoanIconTypeColor> = ({ size, iconColor }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32" fill="none">
      <Path d="M21.8333 11.8333H20.1666V13.5H21.8333V11.8333Z" fill={iconColor} />
      <Path d="M21.8333 15.1667H20.1666V16.8333H21.8333V15.1667Z" fill={iconColor} />
      <Path d="M21.8333 18.5H20.1666V20.1667H21.8333V18.5Z" fill={iconColor} />
      <Path d="M6.83331 15.1667V23.5H11.8333V19.3333H13.5V23.5H18.5V15.1667L12.6666 11L6.83331 15.1667ZM16.8333 21.8333H15.1666V17.6667H10.1666V21.8333H8.49998V16L12.6666 13.0833L16.8333 16V21.8333Z" fill={iconColor} />
      <Path d="M14.3333 8.5V10.1417L16 11.3333V10.1667H23.5V21.8333H20.1666V23.5H25.1666V8.5H14.3333Z" fill={iconColor} />
    </Svg>

  )
}

export default HouseRounded;