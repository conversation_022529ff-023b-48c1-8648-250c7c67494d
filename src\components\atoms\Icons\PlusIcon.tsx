import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

interface Props extends IconProps {
  bgColor: string;
}

const PlusIcon: FC<Props> = ({ size, color, bgColor }) => {
  const viewBoxSize = 28;
  
  return (
    <Svg
      width={size}
      height={size}
      viewBox={`0 0 ${viewBoxSize} ${viewBoxSize}`}
      fill="none"
    >
      <Rect
        width={viewBoxSize}
        height={viewBoxSize}
        rx="4"
        fill={bgColor}
      />
      <Path
        d="M19.8334 14.8334H14.8334V19.8334H13.1667V14.8334H8.16675V13.1667H13.1667V8.16669H14.8334V13.1667H19.8334V14.8334Z"
        fill={color}
      />
    </Svg>
  );
};

export default PlusIcon;
