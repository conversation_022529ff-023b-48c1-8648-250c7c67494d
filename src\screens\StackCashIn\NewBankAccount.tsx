import React from 'react';
import { View, Text } from 'react-native';
import { InputWithLabel } from '../../components/atoms/InputWithLabel';

export const NewBankAccount = () => {
  return (
    <View
      style={{
        flex: 1,
        padding: 16,
      }}
    >
      <View
        style={{
          marginBottom: 24,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Text>Añadir cuenta bancaria</Text>
        <Text>Agregue una cuenta bancaria para ingresar dinero</Text>
      </View>
      <View style={{ gap: 24 }}>
        <InputWithLabel text="Nombre del banco" />
        <InputWithLabel text="Número de cuenta" />
        <InputWithLabel text="Nombre del titular" />
      </View>
    </View>
  );
};
