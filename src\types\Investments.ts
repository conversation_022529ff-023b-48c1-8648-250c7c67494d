export interface AllInvestmentReponse {
  data: AllInvestmentData;
  operationOk: OperationOk;
}

export interface AllInvestmentData {
  items: InvestmentBidding[];
  totalCount: number;
  pageSize: number;
  pageNumber: number;
  totalPages: number;
}

export interface InvestmentBidding {
  id: string;
  name: string;
  goal: number;
  state: State;
  token: Token;
  vehicle: Vehicle;
  hasDelete: boolean;
}

export interface Token {
  totalTokenQuantity: number;
  totalTokenAmount: number;
  soldTokenPercentage: number;
  availableTokenQuantity: number;
}

export interface InvestmentDetailReponse {
  data: InvestmentDetailData;
  operationOk: OperationOk;
}

export interface InvestmentDetailData {
  biddingStartDate: string;
  biddingEndDate: string;
  termsAndConditionsPdfPath: string;
  tokens: DetailTokens;
  id: string;
  name: string;
  goal: number;
  returnRangeMin: number;
  returnRangeMax: number;
  state: State;
  vehicle: Vehicle;
}

export interface DetailTokens {
  totalTokenQuantity: number;
  totalTokenAmount: number;
  soldTokenQuantity: number;
  soldTokenAmount: number;
  availableTokenQuantity: number;
  availableTokenAmount: number;
  soldTokenPercentage: number;
  unitValue: number;
}

export interface MyInvestmentReponse {
  data: MyInvestmentData;
  operationOk: OperationOk;
}

export interface MyInvestmentData {
  items: IMyInvestment[];
  totalCount: number;
  pageSize: number;
  pageNumber: number;
  totalPages: number;
}

export interface IMyInvestment {
  tokens: InvestmentTokens;
  biddingStartDate: string;
  biddingEndDate: string;
  termsAndConditionsPdfPath: string;
  id: string;
  name: string;
  goal: number;
  returnRangeMin: number;
  returnRangeMax: number;
  state: State;
  vehicle: Vehicle;
}

export interface InvestmentTokens {
  tokensAcquired: number;
  investedAmount: number;
  unitValue: number;
}

export interface State {
  name: string;
  color: string;
  id: number;
}

export interface Vehicle {
  manufactureYear: number;
  licensePlate: string;
  engineNumber: string;
  chassisNumber: string;
  id: string;
  name: string;
  state: State2;
  brand: Brand;
  model: Model;
  imagePath: string;
}

export interface State2 {
  name: string;
  color: string;
  id: number;
}

export interface Brand {
  name: string;
  description: string;
  id: number;
}

export interface Model {
  imageFileId: string;
  imageUrl: string;
  name: string;
  description: string;
  id: number;
}

export interface OperationOk {
  message: string;
  detail: string;
}
