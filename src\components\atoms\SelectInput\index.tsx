import React, { useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Text,
  FlatList,
  LogBox,
  Pressable,
} from 'react-native';

import TextBase from '../TextBase';
import color from '../../../theme/pallets/pallet';

interface Props {
  label?: string;
  data: string[];
  input: string;
  onChange: (key: string, value: string) => void;
  name?: string;
  showDropdown: boolean;
  setShowDropdown: (value: boolean) => void;
  placeholder?: string;
}

export default function SelectInput({
  label,
  data,
  input,
  onChange,
  name,
  showDropdown,
  setShowDropdown,
  placeholder,
}: Props) {
  const onChangeInput = (value: string) => {
    setShowDropdown(false);
    onChange(name!, value);
  };

  useEffect(() => {
    LogBox.ignoreLogs(['VirtualizedLists should never be nested']);
  }, []);

  return (
    <Pressable style={{ gap: 6 }} onPress={() => setShowDropdown(true)}>
      {label && <TextBase size="s">{label}</TextBase>}

      <View
        style={{
          borderWidth: 1,
          borderColor: color.NEUTRALS_200,
          borderRadius: 4,
          height: 45,
          padding: 12,
        }}
      >
        {placeholder && !input && (
          <TextBase color={color.NEUTRALS_400}>{placeholder}</TextBase>
        )}
        {input && <TextBase>{input}</TextBase>}
      </View>
      {showDropdown && (
        <View
          style={{
            height: Dimensions.get('window').height / 4,
            marginTop: 4,
            borderWidth: 1,
            borderColor: color.NEUTRALS_100,
            borderRadius: 8,
            elevation: 4,
            shadowOffset: {
              height: 0,
              width: 4,
            },
            shadowOpacity: 0.1,
          }}
        >
          <FlatList
            data={data}
            renderItem={({ item, index }) => (
              <TouchableOpacity
                style={styles.modalOption}
                onPress={() => onChangeInput(item)}
              >
                <Text
                  style={{
                    textAlign: 'left',
                    fontFamily: 'Satoshi-Regular',
                    fontSize: 12,
                    color: color.NEUTRALS_800,
                  }}
                >
                  {item}
                </Text>
              </TouchableOpacity>
            )}
            keyExtractor={(item, index) => index.toString()}
          />
        </View>
      )}
    </Pressable>
  );
}

const styles = StyleSheet.create({
  cardContainer: {
    paddingBottom: 10,
  },
  card: {
    marginHorizontal: 10,
    marginVertical: 8,
  },
  modalOption: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    // alignItems: 'center',
  },
});
