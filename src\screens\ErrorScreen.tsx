import * as React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { Button } from '../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import ErrorImage from '../components/atoms/Icons/ErrorImage';
import useDisableBackButton from '../hooks/utils/useDisableBackButton';
import color from '../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

const ErrorScreen = ({ navigation }: Props) => {
  const handleHome = () => {
    navigation.navigate('Auth');
  };

  useDisableBackButton();

  return (
    <View style={styles.error}>
      <View style={[styles.content, styles.contentFlexBox]}>
        <ErrorImage width={135} height={104} />
        <Text style={[styles.h3, styles.pFlexBox]} numberOfLines={1}>
          Ups, no podemos continuar
        </Text>
        <Text style={[styles.p, styles.pFlexBox]} numberOfLines={3}>
          Te pedimos disculpas, ocurrió un error que no esperábamos. Por favor,
          vuelve a intentar en algunos minutos.
        </Text>
      </View>
      <View style={[styles.actions, styles.contentFlexBox]}>
        <Button text="Volver al inicio" onPress={handleHome} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  buttonBtn: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Satoshi-SemiBold',
  },
  buttonBtn1: {
    padding: 12,
  },
  buttonBtn2: {
    alignSelf: 'stretch',
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  contentFlexBox: {
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  pFlexBox: {
    marginTop: 8,
    textAlign: 'center',
    alignSelf: 'stretch',
  },
  imageIcon: {
    width: 163,
    height: 126,
  },
  h3: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '600',
    fontFamily: 'Satoshi-SemiBold',
    color: color.TEXT_PRIMARY,
  },
  p: {
    fontSize: 12,
    lineHeight: 18,
    fontFamily: 'Satoshi-Regular',
    color: color.NEUTRALS_800,
  },
  content: {
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 0,
    flex: 1,
    alignSelf: 'stretch',
  },
  actions: {
    padding: 16,
  },
  error: {
    width: '100%',
    overflow: 'hidden',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
});

export default ErrorScreen;
