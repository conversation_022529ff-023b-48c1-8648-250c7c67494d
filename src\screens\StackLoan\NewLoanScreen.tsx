import React, { FC } from 'react';
import { RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { colors } from '../../constants/colors';
import { useNavigation } from '@react-navigation/native';
import { useLoansList } from '../../hooks/Loans/useLoansList';
import {
  getLoanTypeTranslation,
  LoanType,
} from '../../types/StackLoan/LoanTypes';
import LoanTypeIcon from '../../components/atoms/Icons/LoanTypeIcon';
import { formatCurrency } from '../../helpers/formatCurrency';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import { TextBase } from '../../components/atoms';
import color from '../../theme/pallets/pallet';

interface LoanCardProps {
  key: React.Key;
  id: string;
  loanType: LoanType;
  limitAmount: number;
  tna: number;
  tea: number;
}

export const NewLoanScreen = () => {
  const navigation: any = useNavigation();
  const { getLoansListFromApi, loansList, loading } = useLoansList();

  const navigateToSimulateLoanIntro = (
    loanTypeId: string,
    loanType: string,
    limitAmount: number,
  ) => {
    navigation.navigate('StackLoan', {
      screen: 'SimulateLoanIntroScreen',
      params: { loanType, limitAmount, loanTypeId },
    });
  };

  const LoansList = () => {
    return loansList?.map(loan => (
      <LoanCard
        key={loan._id}
        id={loan._id}
        loanType={loan.loanType as LoanType}
        limitAmount={loan.limitAmount}
        tna={loan.tna}
        tea={loan.tea}
      />
    ));
  };

  const LoanCard: FC<LoanCardProps> = ({
    id,
    loanType,
    limitAmount,
    tna,
    tea,
  }) => (
    <View style={styles.cardContainer}>
      <View style={styles.cardContainerRow}>
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
          <LoanTypeIcon loanType={loanType as LoanType} />
          <TextBase style={styles.textBold}>
            {getLoanTypeTranslation(loanType as LoanType)}
          </TextBase>
        </View>
      </View>

      <View style={styles.cardContainerRow}>
        <View style={{ flexDirection: 'column' }}>
          <TextBase color={color.NEUTRALS_800} size="s">
            Hasta
          </TextBase>
          <TextBase style={styles.textBold}>
            {' '}
            $ {formatCurrency(Number(limitAmount))}
          </TextBase>
        </View>
        <View style={{ flexDirection: 'row', gap: 40 }}>
          <View style={{ flexDirection: 'column' }}>
            <TextBase color={color.NEUTRALS_800} size="s">
              TNA
            </TextBase>
            <TextBase style={styles.textBold}>{tna}%</TextBase>
          </View>
          <View style={{ flexDirection: 'column' }}>
            <TextBase color={color.NEUTRALS_800} size="s">
              TEA
            </TextBase>
            <TextBase style={styles.textBold}>{tea}%</TextBase>
          </View>
        </View>
      </View>
      <ButtonOutline
        text="Simular"
        onPress={() => navigateToSimulateLoanIntro(id, loanType, limitAmount)}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <TextBase style={styles.title}>Nuevo préstamo</TextBase>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={loading}
            colors={[color.WHITE]}
            onRefresh={getLoansListFromApi}
            progressBackgroundColor={color.PRIMARY_500}
          />
        }
      >
        <View style={styles.cardsContainer}>
          <LoansList />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
    marginBottom: 16,
  },
  cardContainer: {
    borderColor: '#EDEFF6',
    borderStyle: 'solid',
    width: '100%',
    borderWidth: 1,
    borderRadius: 8,
    flexDirection: 'column',
    padding: 14,
    gap: 12,
  },
  cardContainerRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  textBold: {
    fontWeight: '700',
    fontSize: 16,
    color: color.TEXT_PRIMARY,
  },
  cardsContainer: {
    flex: 1,
    paddingTop: 20,
    width: '100%',
    gap: 12,
    paddingBottom: 25,
  },
});
