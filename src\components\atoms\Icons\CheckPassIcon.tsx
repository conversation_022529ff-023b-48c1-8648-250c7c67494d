import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const CheckPassIcon: FC<IconProps> = () => {
  return (
    <Svg width="18" height="17" viewBox="0 0 18 17" fill="none">
      <Path
        d="M6.94514 10.6981L7.33404 11.0855L7.7222 10.6973L12.8236 5.59594L13.2235 5.99871L7.33329 11.8889L4.77778 9.33342L5.17571 8.93548L6.94514 10.6981ZM1.21663 8.50008C1.21663 4.20384 4.70372 0.716748 8.99996 0.716748C13.2962 0.716748 16.7833 4.20384 16.7833 8.50008C16.7833 12.7963 13.2962 16.2834 8.99996 16.2834C4.70372 16.2834 1.21663 12.7963 1.21663 8.50008ZM1.78329 8.50008C1.78329 12.4788 5.0212 15.7167 8.99996 15.7167C12.9787 15.7167 16.2166 12.4788 16.2166 8.50008C16.2166 4.52133 12.9787 1.28341 8.99996 1.28341C5.0212 1.28341 1.78329 4.52133 1.78329 8.50008Z"
        fill="black"
        stroke="#4DA66B"
        strokeWidth="1.1"
      />
    </Svg>
  );
};

export default CheckPassIcon;
