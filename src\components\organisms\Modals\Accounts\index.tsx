import React from 'react';
import { Modal, Pressable, Text, TouchableOpacity, View } from 'react-native';
import { TextBase } from '../../../atoms';
import { styles } from './styles';
import { Account } from '../../../../types/Account';
import KeyboardAvoidingComponent from '../../../molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import AccountIcon from '../../../atoms/Icons/Account';
import ChevronIcon from '../../../atoms/Icons/Chevron';
import { useNavigation } from '@react-navigation/native';
import { useCopyCustomToast } from '../../../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';
import color from '../../../../theme/pallets/pallet';

type ItemProps = {
  label: string;
  value: string;
  icon?: JSX.Element;
};

const Item = ({ label, value, icon }: ItemProps) => {
  const { showToast, toastConfig } = useCopyCustomToast();

  return (
    <View style={styles.containerItem}>
      <View style={styles.iconAccount}>
        <AccountIcon />
      </View>
      <View>
        <TextBase color={color.NEUTRALS_800} type="SemiBold">
          {label}
        </TextBase>
        <TextBase style={styles.value}>{value}</TextBase>
      </View>
      <View style={styles.flexRow}>
        {icon && <Pressable onPress={() => showToast(value)}>{icon}</Pressable>}
      </View>
      <Toast config={toastConfig} />
    </View>
  );
};

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  accountData?: Account[];
};

export const Accounts = ({
  modalVisible,
  setModalVisible,
  accountData,
}: Props) => {
  const { titulares, cvu, alias } = (accountData && accountData[0]) || {};
  const accounts = Array.isArray(accountData) ? accountData : [accountData];
  const Items = [
    {
      label: 'Toshify',
      value: cvu || alias,
      icon: <ChevronIcon />,
    },
  ];
  const navigation: any = useNavigation();

  const navigateToAmount = () => {
    const selectedAccountData = accountData && accountData[0];
    setModalVisible(false);
    navigation.navigate('StackTransfer', {
      screen: 'AmountScreen',
      params: {
        nombre: selectedAccountData?.titulares[0].nombre,
        banco: '',
        cuit: selectedAccountData?.titulares[0].cuit,
        cvu: selectedAccountData?.cvu,
      },
    });
  };

  const firstLetter = titulares && titulares[0]?.nombre.charAt(0).toUpperCase();
  const secondLetter =
    (titulares &&
      titulares[0]?.nombre.split(' ')[1]?.charAt(0).toUpperCase()) ||
    '';

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <KeyboardAvoidingComponent>
        <View style={styles.overlay}>
          <View style={styles.modalView}>
            <View style={styles.containerAccount}>
              <View style={styles.circle}>
                <TextBase style={styles.plus}>
                  {firstLetter}
                  {secondLetter}
                </TextBase>
              </View>
              <View style={styles.account}>
                <TextBase
                  size="l"
                  color={color.TEXT_PRIMARY}
                  type="Bold"
                  numberOfLines={2}
                >
                  {titulares && titulares[0].nombre}
                </TextBase>
                <TextBase style={styles.data}>
                  CUIT/CUIL: {titulares && titulares[0].cuit}
                </TextBase>
              </View>
              <Pressable
                onPress={() => setModalVisible(false)}
                style={styles.close}
              >
                <Text style={styles.textClose}>Cerrar</Text>
              </Pressable>
            </View>
            {accounts.map((account, accountIndex) => (
              <View key={accountIndex}>
                <TouchableOpacity onPress={navigateToAmount}>
                  {account?.titulares && account?.titulares?.length > 1
                    ? account.titulares.map((titular: any, index: any) => (
                        <View key={index}>
                          <TextBase>Cuenta {index + 1}</TextBase>
                          {Items.map((item, i) => (
                            <Item
                              label={item.label}
                              value={titular.cvu}
                              key={i}
                              icon={i === 0 ? item.icon : undefined}
                            />
                          ))}
                        </View>
                      ))
                    : Items.map((item, i) => (
                        <Item
                          label={item.label}
                          value={item.value!}
                          key={i}
                          icon={item.icon}
                        />
                      ))}
                </TouchableOpacity>
              </View>
            ))}
            {/* <View style={styles.mt24}>
              <Button onPress={navigateToAmount} text="Continuar" />
            </View> */}
          </View>
        </View>
      </KeyboardAvoidingComponent>
    </Modal>
  );
};
