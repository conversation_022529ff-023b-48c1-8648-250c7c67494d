import React, { FC } from 'react';
import { ActivityIndicator, TouchableOpacity } from 'react-native';
import { styles } from './styles';
import Text, { variants } from '../Text';

type Props = {
  text: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: variants;
};

const Button: FC<Props> = ({ text, onPress, disabled, loading, variant }) => {
  return (
    <TouchableOpacity
      style={
        disabled || loading ? styles.disabledBackground : styles.background
      }
      disabled={disabled || loading}
      onPress={onPress}
    >
      {loading && (
        <ActivityIndicator size="large" color="black" style={{ padding: 5 }} />
      )}
      {text && (
        <Text
          variant={variant || 'B6'}
          color="PRIMARY_500"
          style={disabled ? styles.disabledText : styles.text}
        >
          {text}
        </Text>
      )}
    </TouchableOpacity>
  );
};

export default Button;
