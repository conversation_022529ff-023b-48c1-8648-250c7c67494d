import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import color from '../../theme/pallets/pallet';
import { InvestmentStackParams } from './types';
import GoBack from '../../components/organisms/Buttons/GoBackButton';
import {
  InvestmentAmountScreen,
  InvestmentConfirmScreen,
  InvestmentDetailScreen,
  InvestmentScreen,
  InvestmentSuccessScreen,
  InvestmentSummaryScreen,
} from '../../screens/stack-investment';

const Stack = createNativeStackNavigator<InvestmentStackParams>();

export default function StackInvestment() {
  const goBack = () => <GoBack />;
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="InvestmentScreen"
        component={InvestmentScreen}
      />
      <Stack.Screen
        options={{ headerShown: false }}
        name="InvestmentDetailScreen"
        component={InvestmentDetailScreen}
      />
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="InvestmentAmountScreen"
        component={InvestmentAmountScreen}
      />
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="InvestmentSummaryScreen"
        component={InvestmentSummaryScreen}
      />
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="InvestmentConfirmScreen"
        component={InvestmentConfirmScreen}
      />
      <Stack.Screen
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
        name="InvestmentSuccessScreen"
        component={InvestmentSuccessScreen}
      />
    </Stack.Navigator>
  );
}
