import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { TransferScreen } from '../screens/StackTransfer/TransferScreen';
import { NewAccountScreen } from '../screens/StackTransfer/NewAccountScreen';
import GoBack from '../components/organisms/Buttons/GoBackButton';
import { AmountScreen } from '../screens/StackTransfer/AmountScreen';
import { ConfirmScreen } from '../screens/StackTransfer/ConfirmScreen';
import { SuccessScreen } from '../screens/StackTransfer/SuccessScreen';
import { ErrorScreen } from '../screens/StackTransfer/ErrorScreen';
import { Pressable } from 'react-native';
import { ArrowLeftLarge } from '../components/atoms/Icons';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<any>();

export default function StackTransfer() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name={'TransferScreen'}
        component={TransferScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name={'NewAccountScreen'}
        component={NewAccountScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name={'AmountScreen'}
        component={AmountScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name={'ConfirmScreen'}
        component={ConfirmScreen}
      />
      <Stack.Screen
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
        name={'SuccessScreen'}
        component={SuccessScreen}
      />
      <Stack.Screen
        options={{
          headerShown: false,
          gestureEnabled: false,
        }}
        name={'ErrorScreen'}
        component={ErrorScreen}
      />
    </Stack.Navigator>
  );
}
