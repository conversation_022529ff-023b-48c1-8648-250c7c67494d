import { AuthContext } from '../../context/AuthContext';
import { api } from '../../services/apiService';
import { useContext, useState } from 'react';

export const useEditContact = () => {
  const [successData, setSuccessData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  const { userData } = useContext(AuthContext);

  const editContactName = async ({ contactId, name }: any) => {
    try {
      setLoading(true);
      const response = await api.put(`/contacts/${userData}/${contactId}`, {
        name,
      });
      console.log('response', JSON.stringify(response, null, 4));
      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
      }
      setLoading(false);
    } catch (err: any) {
      console.error('Error:', JSON.stringify(err.response, null, 4));
      setError(true);
      setErrorMessage(err.response.data.message);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return {
    editContactName,
    successData,
    errorMessage,
    loading,
    success,
    error,
  };
};
