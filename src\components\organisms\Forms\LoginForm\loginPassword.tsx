import React, { FC, useContext, useEffect, useState } from 'react';
import { View, Text, ScrollView, Pressable } from 'react-native';
import { Formik } from 'formik';
import { loginValidationSchema } from '../../../../constants/schemas/loginSchema';
import { AuthContext } from '../../../../context/AuthContext';
import { Button } from '../../../atoms';
import { InputForm } from '../../../molecules';
import { styles, stylesPass } from './styles';
import Logo from '../../../atoms/Icons/Logo';
import Shield from '../../../atoms/Icons/Shield';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { LoginScreenParams } from '../../../../types/Navigation';
import ArrowLeft from '../../../atoms/Icons/ArrowLeft';
import color from '../../../../theme/pallets/pallet';

type Props = NativeStackScreenProps<LoginScreenParams, 'LoginPassword'>;

const LoginPassword: FC<Props> = ({ route, navigation }) => {
  const { signIn, error } = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const { email } = route.params;

  const handleOnSubmit = async ({ password }: { password: string }) => {
    setLoading(true);
    signIn(email, password);
  };

  const textError = error ? 'Email o contraseña incorrectos' : '';

  useEffect(() => {
    error && setLoading(false);
  }, [error]);

  return (
    <ScrollView contentContainerStyle={[styles.container, styles.flex1]}>
      <View style={stylesPass.containerLogo}>
        <View style={styles.containerVolver}>
          <Pressable style={styles.rowCenter} onPress={navigation.goBack}>
            <ArrowLeft size={16} color={color.PRIMARY_700} />
            <Text style={styles.textVolver}>Volver</Text>
          </Pressable>
          <Logo width={70} height={30} />
          <View />
          <View />
        </View>
        <Shield width={188} height={191} />
      </View>
      <Formik
        initialValues={{ email: email, password: '' }}
        validationSchema={loginValidationSchema}
        onSubmit={handleOnSubmit}
      >
        {({ values, handleChange, handleBlur }) => (
          <View style={styles.containerInputs}>
            <View>
              <Text style={styles.textBottom}>Ingresa tu contraseña</Text>
            </View>
            <InputForm
              text={values.password}
              setText={handleChange('password')}
              onBlur={handleBlur('password')}
              placeholder="Ingresa tu contraseña"
              error={!!textError}
              hideText={true}
            />
            <Pressable onPress={() => navigation.navigate('PasswordRecover')}>
              <Text style={stylesPass.textRestablecer}>
                Restablecer contraseña
              </Text>
            </Pressable>

            <View style={stylesPass.button}>
              <Button
                loading={loading}
                text="Iniciar sesion"
                onPress={() => handleOnSubmit(values)}
              />
            </View>
          </View>
        )}
      </Formik>
    </ScrollView>
  );
};

export default LoginPassword;
