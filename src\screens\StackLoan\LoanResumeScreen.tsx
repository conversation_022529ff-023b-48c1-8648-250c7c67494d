import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { Button, TextBase } from '../../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import LoanTypeIcon from '../../components/atoms/Icons/LoanTypeIcon';
import { getLoanTypeTranslation } from '../../types/StackLoan/LoanTypes';
import { formatCurrency } from '../../helpers/formatCurrency';
import { formatDateToNaturalLanguage } from '../../helpers/dateHelper';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const LoanResumeScreen = ({ navigation, route }: Props) => {
  const {
    loanTypeId,
    loanType,
    terms,
    amount,
    totalPayment,
    monthlyPayment,
    firstPayment,
    tna,
    tea,
    ctf,
  } = route.params;

  const navigateToRequestingNewLoanScreen = () => {
    navigation.navigate('StackLoan', {
      screen: 'RequestingNewLoanScreen',
      params: {
        loanTypeId: loanTypeId,
        loanType: loanType,
        terms: terms,
        amount: amount,
        monthlyPayment: monthlyPayment,
        totalPayment: totalPayment,
        firstPayment: firstPayment,
        tna: tna,
        tea: tea,
        ctf: ctf,
      },
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Resumen del préstamo</Text>
      <View style={styles.header}>
        <View style={styles.loanTypeContainer}>
          <LoanTypeIcon loanType={loanType} />
          <Text style={styles.loanType}>
            {getLoanTypeTranslation(loanType)}
          </Text>
        </View>

        <View style={{ width: '100%', paddingTop: 20, paddingBottom: 20 }}>
          <View style={styles.optionsContainer}>
            <Text style={styles.txtResumeTitle}>Recibes</Text>
            <Text style={styles.txtResult}>
              ${formatCurrency(Number(amount))}
            </Text>
          </View>
          <View style={styles.hr} />
          <View style={styles.optionsContainer}>
            <Text style={styles.txtResumeTitle}>Pagas</Text>
            <Text style={styles.txtResumeTitle}>
              {' '}
              {terms}x de{' '}
              <Text style={styles.txtResult}>
                ${formatCurrency(Number(monthlyPayment))}
              </Text>
            </Text>
          </View>
          <View style={styles.hr} />
          <View style={styles.optionsContainer}>
            <View>
              <Text style={styles.txtResumeTitle}>Pagas la primer cuota</Text>
              <Text style={{ color: color.PRIMARY_700 }}>
                Modificar la fecha
              </Text>
            </View>
            <Text style={styles.txtResult}>
              {formatDateToNaturalLanguage(firstPayment)}
            </Text>
          </View>
          <View style={styles.hr} />
          <View style={styles.optionsContainer}>
            <Text style={styles.txtResumeTitle}>En total devuelves</Text>
            <Text style={styles.txtResult}>
              ${formatCurrency(totalPayment)}
            </Text>
          </View>
          <View style={styles.hr} />
        </View>
        <View style={{ width: '100%', paddingBottom: 20, gap: 10 }}>
          <View style={styles.optionsContainer}>
            <TextBase color={color.NEUTRALS_600}>
              Tasa Nominal Anual (TNA)
            </TextBase>
            <TextBase color={color.NEUTRALS_600}>{tna}%</TextBase>
          </View>
          <View style={styles.optionsContainer}>
            <TextBase color={color.NEUTRALS_600}>
              Tasa Efectiva Anual (TEA)
            </TextBase>
            <TextBase color={color.NEUTRALS_600}>{tea}%</TextBase>
          </View>
          <View style={styles.optionsContainer}>
            <TextBase color={color.NEUTRALS_600}>
              Costo Financiero Total (CFT)
            </TextBase>
            <TextBase color={color.NEUTRALS_600}>{ctf}%</TextBase>
          </View>
        </View>
      </View>
      <View style={{ width: '100%', alignItems: 'center' }}>
        <TextBase color={color.NEUTRALS_800}>
          Al confirmar aceptas los{' '}
          <TextBase color={color.PRIMARY_700}>términos generales</TextBase> y
          las{' '}
          <TextBase color={color.PRIMARY_700}>condiciones específicas</TextBase>{' '}
          de este préstamo.
        </TextBase>
      </View>
      <View style={styles.button}>
        <Button
          text="Solicitar préstamo"
          onPress={navigateToRequestingNewLoanScreen}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.TEXT_PRIMARY,
    marginBottom: 16,
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  button: {
    marginTop: 20,
    marginBottom: 32,
    backgroundColor: 'red',
    width: '100%',
  },
  txt: {
    fontSize: 30,
    fontFamily: 'Satoshi-Regular',
    color: color.TEXT_PRIMARY,
    marginVertical: 8,
  },
  txtHeader: {
    fontSize: 15,
    fontFamily: 'Satoshi-Regular',
    color: color.TEXT_PRIMARY,
    fontWeight: 'bold',
    marginVertical: 8,
  },
  txtResumeTitle: {
    fontSize: 18,
    color: color.TEXT_PRIMARY,
  },
  txtResult: {
    color: color.TEXT_PRIMARY,
    fontSize: 20,
    fontWeight: 'bold',
  },
  txtRates: {
    fontSize: 14,
  },
  optionsContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  hr: {
    borderBottomColor: 'grey',
    borderBottomWidth: 0.3,
    marginVertical: 10,
  },
  loanType: {
    fontSize: 16,
    fontWeight: '700',
    color: color.TEXT_PRIMARY,
  },
  loanTypeContainer: {
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
    marginVertical: 20,
  },
});
