import React from 'react';
import { View, Linking } from 'react-native';
import WalletMiniIcon from '../../../atoms/Icons/WalletMiniIcon';
import PlusIcon from '../../../atoms/Icons/PlusIcon';
import TaxIcon from '../../../atoms/Icons/TaxIcon';
import WalletCardsIcon from '../../../atoms/Icons/WalletCardsIcon';
import { Button, Text } from '../../../atoms';
import { styles } from './styles';
import color from '../../../../theme/pallets/pallet';

const OnboardingScreen = ({
  navigateToOnboarding,
}: {
  navigateToOnboarding: () => void;
}) => {
  return (
    <View style={styles.container}>
      <View>
        <WalletMiniIcon />
      </View>
      <Text style={styles.headerText}>¡Empezá a operar con Toshify!</Text>
      <Text style={styles.descriptionText}>
        Abrí tu billetera en pocos minutos y aprovechá todos los beneficios que
        tenemos para ofrecerte.
      </Text>
      <View>
        <View style={styles.rowStyle}>
          <WalletCardsIcon />
          <Text style={styles.iconText}>Cargar saldo</Text>
        </View>
        <View style={styles.rowStyle}>
          <TaxIcon />
          <Text style={styles.iconText}>Pagar servicios</Text>
        </View>
        <View style={styles.rowStyle}>
          <PlusIcon
            size={24}
            bgColor={color.PRIMARY_50}
            color={color.PRIMARY_500}
          />
          <Text style={styles.iconText}>Y mucho más...</Text>
        </View>
      </View>
      <View style={styles.buttonStyle}>
        <Button text="Validar identidad" onPress={navigateToOnboarding} />
      </View>
    </View>
  );
};

export default OnboardingScreen;
