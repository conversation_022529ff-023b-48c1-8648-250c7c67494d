import * as React from 'react';
import { View, Pressable } from 'react-native';
import { formatCurrency } from '../../../../helpers/formatCurrency';
import { ArrowRight } from '../../../atoms/Icons';
import { styles } from './styles';
import LoanTypeIcon from '../../../atoms/Icons/LoanTypeIcon';
import { LoanType } from '../../../../types/StackLoan/LoanTypes';
import { formatDateToDDMMYYYY } from '../../../../helpers/dateHelper';
import { Button, TextBase } from '../../../atoms';
import LoanStatusStack from '../../../../screens/StackLoan/LoanStatutsStack';
import QuotaProgressBar from '../../../../screens/StackLoan/QuotaProgressBar';
import ButtonOutline from '../../../atoms/ButtonOutline';
import { Installment } from '../../../../types/StackLoan/UserLoansResponse';
import color from '../../../../theme/pallets/pallet';

interface LoanCardProps {
  loanType: LoanType;
  finishDate?: string | null;
  finished?: boolean;
  requestAmount: number;
  quoteAmount?: number;
  totalInstallments?: number;
  expired?: number;
  duesPaid?: number;
  nextPaymentDate?: Date | null;
  currentInstallment?: number;
  installments: Installment[];
  navigateToLoanDetailedScreen: () => void;
  navigateToPayInstallmentConfirmation?: (installmentAmount: string) => void;
}

const getEarliestPendingInstallment = (
  installments: Installment[],
): Installment | undefined => {
  return installments
    .filter(installment => installment.status === 'pending')
    .reduce((earliest, current) => {
      return new Date(current.estimatedPaymentDate) <
        new Date(earliest.estimatedPaymentDate)
        ? current
        : earliest;
    }, installments[0]);
};

const LoanCard: React.FC<LoanCardProps> = ({
  loanType,
  finishDate,
  finished = false,
  requestAmount,
  quoteAmount,
  totalInstallments,
  expired,
  duesPaid,
  nextPaymentDate,
  currentInstallment,
  installments,
  navigateToLoanDetailedScreen,
  navigateToPayInstallmentConfirmation,
}) => {
  const FinishedLoanCardPart = () => (
    <View style={{ flexDirection: 'column' }}>
      <TextBase color={color.NEUTRALS_800} size="s">
        Finalizado el
      </TextBase>
      <TextBase type="Bold">{finishDate}</TextBase>
    </View>
  );

  const ProgressBar = () =>
    duesPaid !== undefined &&
    currentInstallment !== undefined &&
    totalInstallments !== undefined &&
    !finishDate && (
      <QuotaProgressBar
        duesPaid={duesPaid}
        currentInstallment={currentInstallment}
        totalInstallments={totalInstallments}
      />
    );

  const PayButtons = () => {
    const today = new Date();
    const erliestPendingInstallment =
      getEarliestPendingInstallment(installments);
    const pendingCuotaExpireDate =
      erliestPendingInstallment?.estimatedPaymentDate &&
      new Date(erliestPendingInstallment?.estimatedPaymentDate);

    if (
      erliestPendingInstallment &&
      pendingCuotaExpireDate &&
      navigateToPayInstallmentConfirmation
    ) {
      if (pendingCuotaExpireDate.getTime() <= today.getTime()) {
        return (
          <Button
            text="Pagar ahora"
            onPress={() =>
              navigateToPayInstallmentConfirmation(
                erliestPendingInstallment.amount,
              )
            }
          />
        );
      }

      return (
        <ButtonOutline
          outline
          text="Adelantar cuota"
          onPress={() =>
            navigateToPayInstallmentConfirmation(
              erliestPendingInstallment.amount,
            )
          }
        />
      );
    }
  };

  return (
    <Pressable style={styles.container} onPress={navigateToLoanDetailedScreen}>
      <View style={styles.containerRow}>
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}>
          <LoanTypeIcon loanType={loanType} />
          <TextBase type="Bold" size="l">
            $ {formatCurrency(Number(requestAmount))}
          </TextBase>
        </View>
        <LoanStatusStack finished={finished} expiredInstallments={expired} />
      </View>

      <ProgressBar />

      <View style={styles.containerRow}>
        {finishDate ? (
          <FinishedLoanCardPart />
        ) : (
          <>
            <View style={{ flexDirection: 'column' }}>
              <TextBase color={color.NEUTRALS_800} size="s">
                Próxima cuota
              </TextBase>
              <TextBase style={styles.textBold}>
                $ {formatCurrency(Number(quoteAmount))}
              </TextBase>
            </View>
            <View style={{ flexDirection: 'column' }}>
              <TextBase color={color.NEUTRALS_800} size="s">
                Vencimiento
              </TextBase>
              <TextBase style={styles.textBold}>
                {nextPaymentDate && formatDateToDDMMYYYY(nextPaymentDate)}
              </TextBase>
            </View>
          </>
        )}
        <ArrowRight size={20} color="#FF583F" />
      </View>

      <View>
        <PayButtons />
      </View>
    </Pressable>
  );
};

export default LoanCard;
