const path = require('path');
const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  resolver: {
    extraNodeModules: {
      'mobile-core': path.resolve(__dirname, '..', 'mobile-core'),
    },
  },
  watchFolders: [path.resolve(__dirname, '..', 'mobile-core')],
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
