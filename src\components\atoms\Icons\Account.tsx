import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const AccountIcon: FC<IconProps> = () => {
  return (
    <Svg width="32" height="32" viewBox="0 0 32 32" fill="none">
      <Rect width="32" height="32" rx="16" fill="#EDEFF7" />
      <Path
        d="M11.8333 15.1667H10.1667V21.0001H11.8333V15.1667ZM16.8333 15.1667H15.1667V21.0001H16.8333V15.1667ZM23.9167 22.6667H8.08334V24.3334H23.9167V22.6667ZM21.8333 15.1667H20.1667V21.0001H21.8333V15.1667ZM16 9.55008L20.3417 11.8334H11.6583L16 9.55008ZM16 7.66675L8.08334 11.8334V13.5001H23.9167V11.8334L16 7.66675Z"
        fill="#535D66"
      />
    </Svg>
  );
};

export default AccountIcon;
