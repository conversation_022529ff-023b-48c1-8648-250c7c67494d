import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const TransferOut: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <Path
        d="M9 5V7H15.59L4 18.59L5.41 20L17 8.41V15H19V5H9Z"
        fill="#FF0033"
      />
    </Svg>
  );
};

export default TransferOut;
