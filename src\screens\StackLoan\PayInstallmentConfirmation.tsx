import React, { useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { Button } from '../../components/atoms';
import { formatCurrency } from '../../helpers/formatCurrency';
import color from '../../theme/pallets/pallet';

export const PayInstallmentConfirmation = ({ navigation, route }: any) => {
  const [loading, setLoading] = useState(false);
  const { installmentAmount } = route.params;

  const navigateToPayInstallmentResultScreen = (error: boolean) => {
    navigation.navigate('StackLoan', {
      screen: 'PayInstallmentResultScreen',
      params: { error },
    });
  };

  const payInstallment = () => {
    setLoading(true);

    const randomResult = (): boolean => Math.random() < 0.5;

    setTimeout(() => {
      navigateToPayInstallmentResultScreen(randomResult());
      setLoading(false);
    }, 3000);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={{ width: '100%', paddingTop: 20 }}>
          <Text style={styles.txt}>
            ¿Esta seguro que desea pagar su cuota por un valor de ${' '}
            {formatCurrency(Number(installmentAmount))}?
          </Text>
        </View>
      </View>

      <View style={styles.button}>
        <Button text="Continuar" onPress={payInstallment} loading={loading} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
    width: 'auto',
  },
  button: {
    marginVertical: 32,
    width: '100%',
  },
  txt: {
    fontSize: 36,
    fontFamily: 'Satoshi-Regular',
    color: color.BLACK,
    marginVertical: 8,
  },
});
