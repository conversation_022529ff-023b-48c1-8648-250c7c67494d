import React from 'react';
import { SafeAreaView, StyleSheet, Text } from 'react-native';
import { name } from '../../package.json';

const NotAllowed = (): JSX.Element => {
  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.text}>
        App {name} is not allowed for emulators or devices in debug/develop
        mode.
      </Text>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    fontSize: 20,
    textAlign: 'center',
    padding: 20,
  },
});

export default NotAllowed;
