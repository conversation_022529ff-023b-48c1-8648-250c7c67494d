import React from 'react';
import { Text as RNText, TextProps } from 'react-native';
import { DefaultPallet } from '../../../theme/enums/defaultPallet';
import color from '../../../theme/pallets/pallet';

export type colors = keyof typeof DefaultPallet;
export type aligns = 'center' | 'left' | 'right';

type FontType = 'Satoshi-Bold' | 'Satoshi-Regular';
type FontSize = 36 | 24 | 21 | 18 | 16 | 14 | 12;
export type variants =
  | 'R1'
  | 'R2'
  | 'R3'
  | 'R4'
  | 'R5'
  | 'R6'
  | 'R7'
  | 'B1'
  | 'B2'
  | 'B3'
  | 'B4'
  | 'B5'
  | 'B6'
  | 'B7';

function getFontDetails(variant: variants): {
  fontFamily: FontType;
  fontSize: FontSize;
} {
  let fontFamily: FontType = 'Satoshi-Regular';
  let fontSize: FontSize = 18;

  if (variant.startsWith('R')) {
    fontFamily = 'Satoshi-Regular';
  } else if (variant.startsWith('B')) {
    fontFamily = 'Satoshi-Bold';
  }

  const variantNumber = parseInt(variant.substring(1), 10);
  const sizeMap: { [key: number]: FontSize } = {
    1: 36,
    2: 24,
    3: 21,
    4: 18,
    5: 16,
    6: 14,
    7: 12,
  };
  fontSize = sizeMap[variantNumber] || 20; // Si la variante no está en el mapa, se usa el tamaño predeterminado

  return { fontFamily, fontSize };
}

type Props = {
  variant?: variants;
  color?: colors;
  align?: aligns;
} & TextProps;

export type Pallet = Record<DefaultPallet, string>;

const colors: Pallet = color;

export default function Text(props: Props) {
  const {
    variant = 'R6',
    // eslint-disable-next-line @typescript-eslint/no-shadow
    color = 'TEXT_PRIMARY',
    align = 'left',
    ...defaultProps
  } = props;

  const { fontFamily, fontSize } = getFontDetails(variant);
  const defaultStyle = {
    fontSize,
    fontFamily,
    color: colors[color],
    textAlign: align,
  };

  return (
    <RNText {...defaultProps} style={[defaultStyle, defaultProps.style]}>
      {props.children}
    </RNText>
  );
}
