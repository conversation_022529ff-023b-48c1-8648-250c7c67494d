import { useEffect, useState } from 'react';
import { UserLoansResponse } from '../../types/StackLoan/UserLoansResponse';
import { getUserLoans } from '../../services/loanServices';

export const useUserLoans = () => {
  const [userLoans, setUserLoans] = useState<UserLoansResponse | undefined>();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  const getUserLoansFromApi = async () => {
    setLoading(true);
    try {
      const loansResponse = await getUserLoans();
      setUserLoans(loansResponse);

    } catch (err: any) {
      setError(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getUserLoansFromApi();
  }, []);

  return { getUserLoansFromApi, userLoans, loading, error };
};
