import color from '../theme/pallets/pallet';
import { colors } from './colors';

export const typograpy = {
  h1: {
    fontFamily: 'Satoshi-SemiBold',
    fontSize: 32,
    color: color.BLACK,
  },
  h2: {
    fontFamily: 'Satoshi-SemiBold',
    fontSize: 18,
    color: color.TEXT_PRIMARY500,
  },
  text: {
    fontFamily: 'Satoshi-SemiBold',
    fontSize: 14,
    color: color.BLACK,
  },
  priceDark: {
    fontFamily: 'Satoshi-SemiBold',
    fontSize: 16,
    color: color.BLACK,
  },
  priceNeutral: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.TEXT_PRIMARY500,
  },
  priceNegative: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: colors.negative,
  },
  pricePositive: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: colors.positive,
  },
  date: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 12,
    color: color.TEXT_PRIMARY500,
    textTransform: 'capitalize',
  },
  suich: {
    fontFamily: 'Satoshi-SemiBold',
    fontSize: 13,
    color: color.BLACK,
  },
};
