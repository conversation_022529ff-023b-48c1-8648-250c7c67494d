import React, { useContext } from 'react';
import { View, ScrollView } from 'react-native';

import { HeaderMore } from '../components/organisms';
import { LogOutButtonIcon } from '../components/atoms/Icons/buttons';

import MoreButton from '../components/organisms/Buttons/MoreButtons';
import { AuthContext } from '../context/AuthContext';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { useUserContext } from '../context/UserContext';
import SeguridadButtonIcon from '../components/atoms/Icons/buttons/SeguridadButtonIcon';
import color from '../theme/pallets/pallet';
import { Text, UserIcon } from '../components/atoms';
import { colors } from '../components/atoms/Text';
import ActivityButtonIcon from '../components/atoms/Icons/buttons/ActivityButtonIcon';
import EditProfileIcon from '../components/atoms/Icons/buttons/EditProfileIcon';

type Props = NativeStackScreenProps<any>;

export default function ProfileScreen({ navigation }: Props) {
  const { signOut } = useContext(AuthContext);
  const { user } = useUserContext();
  const quickButtons = [
    {
      icon: <EditProfileIcon size={32} />,
      label: 'Editar perfil',
      arrow: true,
      onPress: () =>
        user.onboardingStatus === 'approved' &&
        navigation.navigate('StackUser', { screen: 'EditProfile' }),
    },
    {
      icon: <ActivityButtonIcon size={32} />,
      label: 'Actividad',
      arrow: true,
      onPress: () =>
        user.onboardingStatus === 'approved' &&
        navigation.navigate('StackCars', { screen: 'CarsActivityScreen' }),
    },
    {
      icon: <SeguridadButtonIcon size={32} />,
      label: 'Seguridad',
      arrow: true,
      onPress: () => navigation.navigate('SecurityScreen'),
    },
    {
      icon: <LogOutButtonIcon size={32} />,
      label: 'Cerrar sesión',
      labelColor: 'RED_500',
      arrow: false,
      onPress: () => signOut(),
    },
  ];

  const navigateToScreen = (screen: any) => {
    navigation.navigate(screen);
  };

  const diableEdit = user.onboardingStatus === 'approved';
  return (
    <View style={{ flex: 1, backgroundColor: color.WHITE, gap: 16 }}>
      <ScrollView>
        <View
          style={{
            backgroundColor: color.PRIMARY_500,
            padding: 32,
            borderBottomLeftRadius: 16,
            borderBottomRightRadius: 16,
            alignItems: 'center',
            gap: 4,
          }}
        >
          <UserIcon usernameInitials={user?.nombre[0] + user?.apellido[0]} />
          <Text variant="B6" color="WHITE">
            {user?.nombre + ' ' + user?.apellido}
          </Text>
          <Text variant="R7" color="WHITE">
            {user?.email}
          </Text>
        </View>

        <View style={{ padding: 8 }}>
          {quickButtons.map(
            ({ icon, labelColor, label, arrow, onPress }, index) => (
              <MoreButton
                icon={icon}
                label={label}
                labelColor={(labelColor as colors) || 'BLACK'}
                arrow={arrow}
                key={index}
                onPress={onPress}
              />
            ),
          )}
        </View>
        {/* <View
          style={{
            height: 8,
            backgroundColor: color.NEUTRALS_100,
          }}
        /> */}

        {/* {supportButtons.map(({ icon, label, arrow, onPress }, index) => (
          <MoreButton
            icon={icon}
            label={label}
            arrow={arrow}
            key={index}
            onPress={onPress}
          />
        ))}
        <View
          style={{
            height: 8,
            backgroundColor: color.NEUTRALS_100,
          }}
        /> */}
        {/* {sessionButtons.map(({ icon, label, arrow }, index) => (
          <MoreButton
            icon={icon}
            label={label}
            arrow={arrow}
            key={index}
            onPress={() => signOut()}
          />
        ))} */}
      </ScrollView>
    </View>
  );
}
