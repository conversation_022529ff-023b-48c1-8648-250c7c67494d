import React from 'react';
import { View } from 'react-native';
import { styles } from './styles';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import color from '../../../../theme/pallets/pallet';

export const Skeleton = () => {
  return (
    <View style={styles.skeleton}>
      <SkeletonPlaceholder
        backgroundColor={color.NEUTRALS_600}
        highlightColor={color.NEUTRALS_800}
      >
        <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
          <SkeletonPlaceholder.Item
            width={100}
            height={38}
            marginVertical={8}
            borderRadius={4}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  );
};
