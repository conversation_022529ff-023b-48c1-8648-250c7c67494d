import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const TrendingUpIcon: FC<IconProps> = ({ color = 'FF0033', size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M13.3334 5L15.2417 6.90833L11.1751 10.975L7.84175 7.64167L1.66675 13.825L2.84175 15L7.84175 10L11.1751 13.3333L16.4251 8.09167L18.3334 10V5H13.3334Z"
        fill={color}
      />
    </Svg>
  );
};

export default TrendingUpIcon;
