import Svg, {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Defs,
  <PERSON>lipP<PERSON>
} from "react-native-svg"
import { IconProps } from "../types";
import { FC } from "react";

const PrepaidCardFloating: FC<IconProps> = ({}) => {
  return (
    <Svg
      width={168}
      height={109}
      viewBox="0 0 168 109"
      fill="none"
    >
      <G clipPath="url(#clip0_9224_8952)">
        <Rect
          width={150.933}
          height={96.6337}
          rx={5.52192}
          transform="matrix(.99607 .08854 -.17503 .98456 17.287 .495)"
          fill="#841A0A"
        />
        <Path
          d="M27.98 20.817c-.535-.047-.99-.178-1.369-.39a2.768 2.768 0 01-.894-.818 2.958 2.958 0 01-.439-1.089 3.55 3.55 0 01.01-1.222l.033-.188a3.61 3.61 0 01.41-1.135c.203-.364.462-.685.777-.963a3.323 3.323 0 012.504-.784c.546.048 1.011.194 1.396.436.385.237.673.549.864.936.192.381.269.813.229 1.296l-1.22-.109a1.245 1.245 0 00-.177-.71 1.365 1.365 0 00-.526-.498 1.888 1.888 0 00-.766-.224 2.083 2.083 0 00-.82.083c-.252.082-.476.21-.675.383a2.284 2.284 0 00-.503.64 2.996 2.996 0 00-.283.86 2.755 2.755 0 00-.02.945c.047.282.142.527.284.736.143.204.33.368.562.493.233.119.506.192.819.22.415.037.794-.016 1.137-.16.344-.148.612-.355.803-.622l-.387.964.285-1.606 1.117.099-.246 1.384c-.339.38-.759.664-1.26.851-.5.181-1.048.245-1.645.192zm.235-2.443l.16-.906 3.395.302-.161.905-3.394-.301zM34.308 21.38c-.41-.037-.759-.134-1.048-.293a2.103 2.103 0 01-.7-.6 2.174 2.174 0 01-.357-.813 2.443 2.443 0 01-.004-.903l.032-.18a2.61 2.61 0 01.97-1.614c.263-.208.562-.36.897-.458.336-.103.7-.137 1.092-.102a2.58 2.58 0 011.023.29c.29.153.523.353.699.6.182.242.304.513.366.814.063.295.066.601.01.92l-.032.18a2.563 2.563 0 01-.329.873 2.612 2.612 0 01-.623.726 2.673 2.673 0 01-.887.459 2.885 2.885 0 01-1.11.1zm.18-1.017c.29.026.546-.015.767-.123.223-.113.404-.273.545-.48.142-.213.238-.46.287-.739.05-.284.041-.54-.028-.766a1.044 1.044 0 00-.368-.562c-.176-.148-.403-.235-.682-.26a1.401 1.401 0 00-1.313.612 1.802 1.802 0 00-.295.738c-.05.279-.043.534.02.765.07.226.192.413.367.562.177.143.41.227.7.253zM37.959 21.54l1.108-6.236 1.186.105-1.109 6.236-1.185-.106zm.37-5.4l.158-.888 1.766.157-.158.888-1.766-.157zM40.557 21.77l1.109-6.236 1.185.106-1.109 6.236-1.185-.106zm.37-5.399l.159-.888 1.765.157-.158.888-1.765-.157zM45.14 22.342c-.399-.035-.736-.134-1.013-.298a1.941 1.941 0 01-.645-.613 2.348 2.348 0 01-.305-.816 2.662 2.662 0 01.01-.884l.028-.163c.055-.307.156-.6.303-.875a2.67 2.67 0 01.58-.73c.24-.21.516-.367.83-.473.314-.11.661-.149 1.042-.115.5.044.899.192 1.195.444.302.247.507.558.614.931.108.369.125.758.052 1.168l-.076.427-4.102-.365.13-.726 3.36.299-.431.326a2 2 0 00.007-.772.907.907 0 00-.306-.521c-.15-.13-.356-.205-.617-.229a1.262 1.262 0 00-.697.12 1.298 1.298 0 00-.511.484c-.13.214-.224.478-.28.791-.05.29-.051.554 0 .79.05.23.159.419.323.566.165.142.393.226.683.252.29.026.536-.01.738-.108a.914.914 0 00.427-.404l1.092.097a2.18 2.18 0 01-1.382 1.291c-.319.105-.669.14-1.05.106zM50.386 22.705c-.426-.038-.77-.124-1.029-.257a1.056 1.056 0 01-.513-.618c-.09-.28-.094-.645-.014-1.095l.756-4.201 1.109.098-.77 4.279c-.04.228-.012.41.085.545.104.131.27.207.497.227l.725.064-.18 1.017-.666-.06zm-1.93-4.06l.156-.87 3.274.29-.154.872-3.275-.291z"
          fill="#fff"
        />
        <Path
          d="M56.134 16.814c-.463.968-1.6 1.447-2.54 1.072s-1.326-1.463-.863-2.43c.463-.967 1.6-1.447 2.54-1.072s1.326 1.463.863 2.43zM51.455 14.946c-.232.484-.8.723-1.27.536-.47-.188-.664-.732-.432-1.215.232-.484.8-.724 1.27-.536.47.187.663.732.432 1.215z"
          fill="#FDC228"
        />
        <Path
          d="M118.263 24.795l.083-.468.897.08a.984.984 0 00.492-.067.836.836 0 00.335-.28.995.995 0 00.174-.415.888.888 0 00-.023-.438.61.61 0 00-.227-.324.875.875 0 00-.454-.15l-.896-.08.083-.468.797.071c.33.03.596.106.797.231a.977.977 0 01.429.48c.08.199.097.427.052.684l-.016.087a1.403 1.403 0 01-.289.653c-.147.179-.342.31-.584.396-.238.082-.523.108-.853.079l-.797-.071zm-.675 1.342l.71-3.99.539.048-.71 3.99-.539-.047zm3.519.313l.709-3.99.539.048-.709 3.99-.539-.048zm2.48.22l-1.017-1.885.633.057 1.039 1.887-.655-.058zm-1.89-1.625l.084-.473 1.078.096a.914.914 0 00.453-.065.821.821 0 00.331-.258.954.954 0 00.174-.41.82.82 0 00-.026-.422.635.635 0 00-.229-.313.779.779 0 00-.415-.147l-1.079-.096.084-.468.979.088c.293.026.542.092.746.198a.943.943 0 01.44.448c.089.192.108.431.057.718l-.015.087c-.051.286-.155.514-.312.684a1.21 1.21 0 01-.583.357 2.13 2.13 0 01-.788.063l-.979-.087zm3.261 1.748l.705-3.97.539.049-.705 3.968-.539-.047zm.429.038l.085-.479 1.947.173-.085.479-1.947-.173zm.316-1.778l.085-.479 1.777.158-.085.479-1.777-.158zm.304-1.713l.085-.478 1.893.168-.086.479-1.892-.169zm2.923 2.403l.084-.468.896.08a.98.98 0 00.492-.067.83.83 0 00.335-.28.97.97 0 00.174-.414.889.889 0 00-.022-.439.617.617 0 00-.228-.324.872.872 0 00-.453-.15l-.897-.08.083-.468.798.071c.33.03.596.107.797.231a.986.986 0 01.429.48c.08.199.097.427.051.684l-.015.087a1.405 1.405 0 01-.29.654c-.147.178-.342.31-.584.395-.238.082-.522.108-.852.079l-.798-.071zm-.674 1.343l.709-3.99.539.047-.709 3.99-.539-.047zm2.804.249l2.153-3.84.913.081.785 4.101-.556-.05-.704-3.74.241.154-.649-.058.304-.105-1.948 3.505-.539-.048zm1.075-1.108l.266-.463 1.733.154.096.495-2.095-.186zm5.159 1.761a2.148 2.148 0 01-.82-.227 1.71 1.71 0 01-.579-.488 1.79 1.79 0 01-.302-.69 2.233 2.233 0 01-.001-.839l.021-.12c.045-.253.133-.494.263-.722.134-.23.302-.433.504-.607.207-.176.442-.308.707-.395.266-.09.553-.122.861-.095.334.03.619.12.857.27.239.146.417.339.534.577.119.236.165.501.138.797l-.55-.049a.954.954 0 00-.122-.569.99.99 0 00-.392-.371 1.511 1.511 0 00-1.15-.102c-.189.056-.36.148-.513.274a1.689 1.689 0 00-.379.469 2.033 2.033 0 00-.22.638 2.118 2.118 0 00-.017.727c.*************.**************.253.261.424.346.175.086.373.139.593.158.311.028.595-.01.852-.112.261-.106.47-.253.626-.441l-.313.629.21-1.18.506.045-.168.946a2.092 2.092 0 01-.79.482 2.41 2.41 0 01-1.008.11zm.137-1.589l.079-.446 2.151.192-.08.445-2.15-.19zm2.181 1.696l2.152-3.84.913.081.785 4.101-.556-.049-.704-************-.649-.057.304-.106-1.947 3.505-.539-.048zm1.074-1.108l.266-.463 1.733.154.096.495-2.095-.186z"
          fill="#fff"
        />
        <Path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M154.864 21.995c.25-.116.525-.01.613.239.589 1.655.729 3.485.405 5.307a11.755 11.755 0 01-2.25 5.072c-.173.224-.476.28-.677.123-.202-.156-.225-.465-.053-.69a10.628 10.628 0 002.036-4.589c.293-1.648.167-3.304-.367-4.802a.547.547 0 01.293-.66zm-2.27.903c.25-.116.524-.01.613.238.466 1.309.576 2.756.32 4.196a9.28 9.28 0 01-1.779 4.009c-.172.224-.475.28-.677.123-.201-.156-.225-.465-.052-.69a8.175 8.175 0 001.564-3.526 7.23 7.23 0 00-.282-3.69.547.547 0 01.293-.66zm-2.158.821c.251-.115.524-.007.612.241a6.2 6.2 0 01.231 3.132 6.996 6.996 0 01-1.32 2.993c-.172.226-.475.282-.677.127-.202-.156-.227-.465-.056-.69a5.869 5.869 0 001.109-2.514c.16-.903.093-1.81-.194-2.63a.547.547 0 01.295-.659zm-2.203.916c.245-.131.526-.043.629.198.278.648.359 1.377.229 2.105a4.216 4.216 0 01-.958 1.998c-.184.215-.488.252-.679.082-.191-.17-.196-.482-.012-.697.368-.428.611-.937.705-1.467a2.702 2.702 0 00-.169-1.546c-.103-.24.011-.542.255-.673z"
          fill="#F7F8FE"
        />
        <Path
          opacity={0.5}
          d="M88.555-.994C84.307 22.902 61.265 40.53 37.09 38.382 12.915 36.233-3.239 15.12 1.01-8.776 5.257-32.672 28.3-50.3 52.473-48.152 76.65-46.003 92.803-24.89 88.554-.994z"
          stroke="#FF0033"
          strokeWidth={0.46016}
        />
        <Ellipse
          opacity={0.5}
          cx={44.1754}
          cy={44.1754}
          rx={44.1754}
          ry={44.1754}
          transform="matrix(.99607 .08854 -.17503 .98456 71.085 54.06)"
          fill="#FF583F"
        />
        <Circle
          cx={37.273}
          cy={37.273}
          r={37.0429}
          transform="matrix(.99607 .08854 -.17503 .98456 115.534 42.363)"
          stroke="#FFE7E3"
          strokeWidth={0.46016}
        />
        <Path
          d="M16.838 74.556l-.85-.791.129-.723 1.1-.618.266.707-.84.248-.027.152.728.38-.506.645zm-.442-2.752l-.34 1.174-.7.306-1.017-.58.515-.625.577.567.148-.064.031-.835.786.057zm-2.248 1.971l1.19-.384.57.418-.082 1.198-.782-.076.262-.814-.12-.089-.752.45-.286-.703zm6.35 1.106l-.85-.79.128-.724 1.101-.617.265.706-.839.248-.027.152.728.38-.506.645zm-.443-2.752l-.339 1.175-.7.305-1.017-.58.515-.625.577.567.148-.064.031-.835.785.057zm-2.247 1.972l1.19-.384.57.418-.082 1.197-.782-.076.262-.814-.121-.088-.751.448-.286-.701zm6.35 1.105l-.85-.79.128-.724 1.1-.617.266.707-.84.247-.026.152.727.38-.505.645zm-.443-2.752l-.34 1.175-.699.305-1.018-.58.515-.624.577.566.149-.064.03-.835.786.057zm-2.247 1.972l1.189-.384.571.418-.083 1.198-.781-.076.262-.815-.121-.088-.752.449-.285-.702zm6.35 1.105l-.85-.79.128-.723 1.1-.618.266.707-.84.248-.026.152.727.38-.506.644zm-.443-2.751l-.34 1.174-.7.305-1.017-.58.515-.624.577.567.148-.064.031-.835.786.057zm-2.248 1.971l1.19-.384.57.418-.082 1.198-.782-.076.262-.814-.12-.089-.752.45-.286-.703zm4.644 3.293l.17-.951a1.4 1.4 0 01.188-.512c.095-.142.225-.263.39-.365.167-.105.38-.198.637-.278l.815-.256c.207-.064.365-.15.476-.26a.773.773 0 00.222-.439.591.591 0 00-.092-.472c-.095-.128-.253-.203-.476-.222-.222-.02-.402.026-.54.138a.767.767 0 00-.26.48l-1.021-.09c.052-.293.163-.549.332-.77.17-.226.394-.395.67-.508.277-.112.601-.152.974-.12.372.034.67.127.895.28.23.154.388.346.478.577.093.232.116.485.067.76l-.014.076c-.063.355-.214.64-.452.856-.237.21-.577.386-1.02.528l-.808.256a.936.936 0 00-.306.147.392.392 0 00-.12.234l-.056.317-.213-.231 2.566.228-.159.894-3.343-.297zm4.04-.543l.192-1.085 2.274-2.632.721.598-1.946 2.256.066.142 2.805.249-.147.825-3.966-.353zm2.3 1.106l.592-3.336.97.086-.594 3.337-.969-.087zm3.712.453c-.373-.033-.681-.132-.926-.295a1.393 1.393 0 01-.52-.633 1.497 1.497 0 01-.07-.818l1.015.09a.715.715 0 00.028.39c.05.112.129.205.237.279.11.07.241.111.395.125a.887.887 0 00.448-.063.85.85 0 00.327-.261.954.954 0 00.17-.404.827.827 0 00-.023-.42.609.609 0 00-.218-.317.737.737 0 00-.404-.145.953.953 0 00-.434.058.767.767 0 00-.32.216l-.987-.087.702-2.593 2.842.253-.157.888-2.426-.215.451-.23-.408 1.49-.233-.085c.156-.141.333-.254.53-.34.203-.09.452-.12.747-.094.342.03.62.126.836.286.22.157.373.358.46.602.092.24.113.502.062.785l-.014.082a1.71 1.71 0 01-.353.775 1.817 1.817 0 01-.729.54c-.296.129-.64.175-1.028.14zm3.188.16l.701-3.944.245.24-1.2-.106.16-.9 1.963.174-.823 4.63-1.046-.093z"
          fill="#fff"
        />
        <Path
          d="M12.084 85.473l.706-3.969.88.078 1.42 3.672.176.016-.128.088.638-3.588.517.046-.705 3.968-.891-.079-1.42-3.672-.176-.015.127-.088-.638 3.588-.506-.045zm6.449.673c-.36-.032-.658-.123-.896-.273a1.666 1.666 0 01-.554-.558 2.041 2.041 0 01-.262-.697 2.066 2.066 0 010-.69l.021-.12a2.294 2.294 0 01.754-1.319c.208-.184.45-.324.729-.421.281-.096.598-.13.95-.098.352.031.646.118.881.26.24.144.428.322.564.537.138.21.228.44.27.687a2.1 2.1 0 010 .723l-.022.12c-.039.217-.121.44-.247.668a2.438 2.438 0 01-.492.63 2.14 2.14 0 01-.733.443c-.283.104-.604.14-.963.108zm.087-.49c.235.021.453-.004.656-.074a1.63 1.63 0 00.547-.31 1.873 1.873 0 00.61-1.708 1.368 1.368 0 00-.212-.532 1.22 1.22 0 00-.422-.391c-.173-.1-.376-.16-.61-.181a1.576 1.576 0 00-1.2.374 1.715 1.715 0 00-.394.478c-.104.186-.175.39-.214.61-.038.21-.039.415-.003.613.036.195.107.374.211.538.106.16.245.293.416.396.175.104.38.166.615.187zm2.89.655l.706-3.969.715.064.718 2.753.088.008 1.653-2.543.72.064-.705 3.969-.54-.048.603-3.387.037.014-1.593 2.454-.66-.058-.69-2.657.046-.007-.602 3.387-.495-.044zm5.486.51l.083-.468 1.122.1c.216.019.394-.026.532-.135a.742.742 0 00.269-.473c.037-.207.007-.373-.09-.5-.096-.13-.254-.205-.474-.224l-1.122-.1.062-.348 1.044.093c.257.023.477.074.66.152.188.08.323.197.408.351.088.152.11.35.067.597l-.014.076a1.259 1.259 0 01-.248.58 1.06 1.06 0 01-.5.331 1.94 1.94 0 01-.754.06l-1.045-.093zm-.43-.039l.714-4.012.539.048-.713 4.012-.54-.048zm.767-1.859l.062-.348.973.087c.228.02.405-.025.533-.135a.749.749 0 00.248-.453c.034-.192.007-.353-.082-.482-.084-.133-.24-.21-.468-.23l-.973-.086.083-.468.88.078c.44.04.752.161.935.365.184.2.246.473.185.817l-.014.076c-.043.243-.133.432-.268.568-.135.131-.308.219-.516.263-.205.04-.438.05-.698.026l-.88-.078zm2.976 2.17l.71-3.99.538.047-.709 3.99-.539-.047zm2.48.22l-1.016-1.885.632.056 1.04 1.888-.655-.059zm-1.89-1.626l.085-.472 1.078.095a.915.915 0 00.453-.064.827.827 0 00.33-.258.95.95 0 00.174-.41.815.815 0 00-.026-.422.632.632 0 00-.229-.313.778.778 0 00-.415-.147L31.27 83.6l.083-.468.979.087c.293.026.542.093.747.2a.948.948 0 01.44.447c.089.192.107.431.057.717l-.016.087c-.05.287-.155.515-.312.685-.153.17-.347.29-.583.357a2.129 2.129 0 01-.787.063l-.98-.088zm3.261 1.748l.706-3.969.539.048-.706 3.97-.539-.049zm.43.038l.084-.478 1.947.173-.085.478-1.947-.173zm.315-1.777l.085-.479 1.777.158-.085.478-1.777-.157zm.305-1.713l.085-.478 1.892.168-.085.478-1.892-.168zm3.07 3.819l2.153-3.84.913.08.785 4.102-.556-.05-.704-3.74.241.154-.649-.058.304-.106-1.947 3.505-.54-.047zm1.075-1.109l.267-.462 1.732.154.097.494-2.096-.186zm4.187.173l.083-.467.896.08a.984.984 0 00.493-.067.834.834 0 00.334-.28.984.984 0 00.175-.415.89.89 0 00-.023-.438.617.617 0 00-.227-.324.876.876 0 00-.454-.15l-.897-.08.084-.468.797.07c.33.03.596.107.797.232a.978.978 0 01.429.48c.08.198.097.426.051.683l-.015.088a1.407 1.407 0 01-.29.653c-.147.178-.341.31-.584.395-.238.082-.522.109-.852.08l-.797-.072zm-.675 1.343l.71-3.99.539.048-.71 3.99-.539-.048zm3.518.313l.706-3.969.539.048-.706 3.969-.539-.048zm.43.038l.084-.478 1.947.173-.084.478-1.948-.173zm.315-1.778l.085-.478 1.777.158-.085.478-1.777-.158zm.305-1.712l.085-.479 1.892.169-.085.478-1.892-.168zm2.248 3.745l.706-3.969.539.048-.706 3.969-.539-.048zm.43.038l.084-.478 1.837.163-.084.478-1.838-.163zm2.562.228l.706-3.969.539.048-.706 3.969-.539-.048zm.43.038l.084-.478 1.837.163-.085.478-1.837-.163zm2.573.229l.706-3.969.539.048-.706 3.969-.539-.048zm2.144.213l.087-.49 1 .09c.243.02.464.002.665-.058a1.605 1.605 0 00.936-.74c.102-.178.172-.374.21-.588.04-.225.042-.43.004-.618a1.19 1.19 0 00-.223-.506 1.156 1.156 0 00-.428-.358 1.69 1.69 0 00-.624-.166l-1.001-.09.087-.489.946.084c.363.033.668.117.916.253.248.133.444.3.586.505.147.2.244.421.29.66.048.237.05.475.008.714l-.021.12c-.041.232-.127.46-.256.684a2.222 2.222 0 01-1.26.992 2.538 2.538 0 01-.976.085l-.946-.084zm-.43-.039l.714-4.012.539.048-.713 4.012-.54-.047zm6.136.623c-.359-.032-.657-.123-.895-.273a1.665 1.665 0 01-.555-.557 2.041 2.041 0 01-.261-.697 2.066 2.066 0 010-.69l.02-.12c.043-.236.127-.47.254-.701a2.248 2.248 0 011.23-1.04c.281-.096.598-.128.95-.097s.646.118.881.26c.24.143.427.322.564.536.138.212.227.44.269.687a2.1 2.1 0 010 .724l-.021.12c-.04.217-.121.44-.248.668a2.438 2.438 0 01-.492.63 2.14 2.14 0 01-.732.442c-.283.104-.604.14-.964.108zm.087-.49c.235.022.454-.003.656-.073.206-.07.389-.174.547-.31a1.873 1.873 0 00.61-1.708 1.366 1.366 0 00-.212-.533 1.219 1.219 0 00-.422-.39c-.172-.1-.376-.161-.61-.182a1.575 1.575 0 00-1.2.374 1.713 1.713 0 00-.393.479c-.104.186-.176.389-.215.61-.037.21-.038.415-.003.613.037.195.107.374.212.538.106.16.244.292.416.396.175.104.38.166.614.187zM74.755 88.973l-.737-.662.092-.522.946-.513.172.447-.775.245-.04.229.665.374-.323.402zm-.462-2.272l-.275 1.008-.508.22-.859-.513.322-.397.552.513.224-.096.048-.774.496.038zm-1.82 1.716l1.012-.347.416.302-.088 1.025-.494-.05.225-.758-.184-.132-.713.4-.173-.44zm5.403.833l-.738-.662.093-.522.946-.512.172.446-.775.246-.041.228.665.374-.322.402zm-.463-2.272l-.274 1.008-.508.22-.859-.513.322-.396.551.513.224-.096.049-.775.495.039zm-1.819 1.716l1.012-.347.415.303-.087 1.024-.494-.049.224-.759-.183-.132-.714.4-.173-.44zm2.512 2.979l1.934-4.467.495.044-1.934 4.467-.495-.044zm4.781-1.978l-.737-.661.093-.522.946-.513.172.446-.776.246-.04.228.665.374-.323.402zm-.462-2.272l-.274 1.009-.509.22-.858-.513.322-.397.55.513.225-.096.048-.774.496.038zm-1.82 1.716l1.012-.346.416.302-.088 1.025-.493-.05.224-.758-.183-.132-.714.4-.173-.44zm5.403.834l-.737-.662.092-.522.946-.512.172.446-.775.246-.04.228.665.374-.323.402zm-.462-2.272l-.275 1.008-.508.22-.859-.513.322-.396.552.513.223-.096.049-.775.496.039zm-1.82 1.716l1.012-.347.415.302-.087 1.025-.494-.05.224-.758-.183-.132-.713.4-.174-.44z"
          fill="#FFF2F0"
        />
        <Path
          d="M126.557 97.511l-2.376-.211 3.101-8.95 2.376.212-3.101 8.949zM136.747 89.418a5.625 5.625 0 00-2.063-.576c-2.347-.209-4.219.88-4.544 2.648-.251 1.302.823 2.134 1.645 2.648.841.526 1.101.844 1.031 1.24-.117.608-.901.823-1.585.762-.949-.084-1.431-.274-2.144-.68l-.287-.174-.695 2.01c.513.3 1.5.614 2.565.718 2.493.222 4.333-.851 4.687-2.733.193-1.033-.301-1.882-1.555-2.65-.758-.49-1.217-.816-1.141-1.24.078-.387.569-.745 1.508-.662a3.86 3.86 0 011.708.505l.198.116.672-1.932zM138.902 95.34c.289-.504 1.401-2.456 1.401-2.456-.014.019.289-.514.467-.842l.027.798s.063 2.213.08 2.676l-1.975-.176zm3.976-5.603l-1.838-.163c-.567-.05-1.027.075-1.376.643l-5.01 8.014 2.493.221.741-1.318 3.051.272c.012.324.041 1.387.041 1.387l2.201.195-.303-9.25zM123.81 88.042l-3.428 5.986-.031-1.279c-.172-1.487-1.242-3.181-2.608-4.098l.72 8.13 2.512.224 5.348-8.74-2.513-.223z"
          fill="#fff"
        />
        <Path
          d="M119.323 87.644l-3.824-.34-.071.18c2.848 1.018 4.5 3.01 4.924 5.266l-.09-4.248c-.033-.592-.425-.793-.939-.858z"
          fill="#fff"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_9224_8952">
          <Rect
            width={150.933}
            height={96.6337}
            rx={5.52192}
            transform="matrix(.99607 .08854 -.17503 .98456 17.287 .495)"
            fill="#fff"
          />
        </ClipPath>
      </Defs>
    </Svg>
  )
}

export default PrepaidCardFloating;
