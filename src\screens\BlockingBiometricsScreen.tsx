import React, { useCallback, useContext, useEffect, useState } from 'react';
import { View, Image } from 'react-native';
import { Button, Text, TextBase } from '../components/atoms';
import { StackScreenProps } from '@react-navigation/stack';
import { storage } from '../lib/mmkv';
import { promptBiometrics } from '../lib/biometrics';
import { getCredentials } from '../helpers/asyncStorage';
import { AuthContext } from '../context/AuthContext';
import { isNil } from 'lodash';
import { useBiometrics } from '../hooks/useBiometrics';
import { useIsFocused } from '@react-navigation/native';
import useDisableBackButton from '../hooks/utils/useDisableBackButton';
import color from '../theme/pallets/pallet';

interface Props extends StackScreenProps<any> { }

export default function BlockingBiometricsScreen({ navigation }: Props) {
  useDisableBackButton();

  const [loading, setLoading] = useState(false);
  const { signIn, authenticated, reauthWithBiometrics } =
    useContext(AuthContext);

  const [hasTriggeredBiometrics, setHasTriggeredBiometrics] = useState(false);

  const unlockMethodType = storage.getString('unlockMethodType');
  const { available } = useBiometrics();
  const isFocused = useIsFocused();

  const triggerBiometrics = useCallback(async () => {
    try {
      if (
        unlockMethodType === 'Biometrics' ||
        unlockMethodType === 'FaceID' ||
        unlockMethodType === 'TouchID'
      ) {
        const success = await promptBiometrics(unlockMethodType);
        setHasTriggeredBiometrics(true);

        if (success) {
          setLoading(true);

          if (authenticated && !reauthWithBiometrics) {
            setLoading(false);
            navigation.goBack();
            return;
          }

          const credentials = await getCredentials();

          if (isNil(credentials)) {
            setLoading(false);
            return;
          }

          const loginResponse = await signIn(
            credentials.username,
            credentials.password,
          );
          setLoading(false);

          if (loginResponse.data) {
            navigation.navigate('Home');
          }
        }
      }
    } catch (ex) {
      setLoading(false);
    }
  }, [
    authenticated,
    reauthWithBiometrics,
    unlockMethodType,
    signIn,
    navigation,
  ]);

  useEffect(() => {
    if (
      available &&
      !isNil(unlockMethodType) &&
      isFocused &&
      !hasTriggeredBiometrics
    ) {
      triggerBiometrics();
    }
  }, [
    available,
    triggerBiometrics,
    unlockMethodType,
    isFocused,
    hasTriggeredBiometrics,
  ]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: color.WHITE,
        padding: 16,
        justifyContent: 'space-between',
      }}
    >
      <View
        style={{
          alignItems: 'center',
          marginTop: 'auto',
          marginBottom: 'auto',
          gap: 32,
        }}
      >
        <Image source={require('../assets/ilustrations/Block/Block.png')} />
        <Text
          variant="B5"
          style={{ textAlign: 'center', paddingHorizontal: 16 }}
        >
          Utiliza tu huella para continuar
        </Text>
      </View>
      <Button
        loading={loading}
        text="Utilizar huella digital"
        onPress={triggerBiometrics}
      />
    </View>
  );
}
