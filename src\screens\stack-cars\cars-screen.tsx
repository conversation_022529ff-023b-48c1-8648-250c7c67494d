/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useState } from 'react';
import { Image, ScrollView, TouchableOpacity, View } from 'react-native';
import { StyleSheet } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import color from '../../theme/pallets/pallet';
import { Button, Text } from '../../components/atoms';
import HeaderCars from '../../components/organisms/Headers/Cars';
import { CarsStackParams } from '../../navigation/stack-cars/types';
import ArrowRightSmall from '../../components/atoms/Icons/ArrowRightSmall';
import ArrowLeftSmall from '../../components/atoms/Icons/ArrowLeftSmall';
import { ChipStateColor } from '../../components/molecules/ChipStateColor/ChipStateColor';
import { InvestmentDetails } from '../../components/organisms/Cards/InvestmentDetails/InvestmentDetails';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import { CarsActivity } from '../../components/organisms/Cards/CarsActivity/CarsActivity';
import { getMyInvestments } from '../../services/investments';
import { IMyInvestment } from '../../types/Investments';
import { formatCurrency } from '../../helpers/formatCurrency';

type Props = NativeStackScreenProps<CarsStackParams, 'CarsScreen'>;

export default function CarsScreen({ navigation }: Props) {
  const handleNavigateToActivity = () => {
    navigation.navigate('StackCars', { screen: 'CarsActivityScreen' });
  };
  const [investmentData, setInvestmentData] = useState<IMyInvestment[]>([]);
  const [itemIndex, setItemIndex] = useState<number>(0);

  const getInvestments = async () => {
    const response = await getMyInvestments();
    setInvestmentData(response?.data.items || []);
  };

  const navigateToInvestment = () => {
    navigation.navigate('StackInvestment', {
      screen: 'InvestmentScreen',
    });
  };
  useEffect(() => {
    getInvestments();
  }, []);

  const licensePlateToShow = (licensePlate: string) => {
    if (licensePlate.length < 3) {
      return licensePlate;
    }
    return licensePlate[0] + '**' + licensePlate.slice(3);
  };
  const engineNumberToShow = (licensePlate: string) => {
    if (licensePlate.length < 3) {
      return licensePlate;
    }
    return licensePlate.slice(0, 3) + '*******' + licensePlate.slice(-3);
  };

  const nextCarDetail = () => {
    setItemIndex(prev => prev + 1);
  };

  const prevCarDetail = () => {
    setItemIndex(prev => prev - 1);
  };
  return (
    <ScrollView contentContainerStyle={styles.containter}>
      <HeaderCars handlePress={handleNavigateToActivity} />

      <View style={styles.investmentBox}>
        <Text variant="R7" color="NEUTRALS_800">
          Tu inversión total
        </Text>
        <View style={styles.investmentNumbers}>
          <Text variant="B4">
            $
            {formatCurrency(
              investmentData[itemIndex]?.tokens.investedAmount || 0,
            )}
          </Text>
        </View>
      </View>
      <View style={{ position: 'relative' }}>
        <View style={styles.carContainer}>
          <TouchableOpacity onPress={prevCarDetail} disabled={itemIndex === 0}>
            <ArrowLeftSmall
              color={itemIndex === 0 ? color.NEUTRALS_400 : color.PRIMARY_500}
            />
          </TouchableOpacity>
          <Image
            source={require('../../assets/ilustrations/fiat-cronos/fiat_Cronos.png')}
            style={styles.carImage}
            resizeMode="contain"
          />
          <TouchableOpacity
            onPress={nextCarDetail}
            disabled={itemIndex >= investmentData.length - 1}
          >
            <ArrowRightSmall
              color={
                itemIndex >= investmentData.length - 1
                  ? color.NEUTRALS_400
                  : color.PRIMARY_500
              }
            />
          </TouchableOpacity>
        </View>
        <View style={styles.cardContainer}>
          <View style={styles.card}>
            <View style={{ display: 'flex', flexDirection: 'row' }}>
              <Text variant="B4" style={{ paddingRight: 3 }}>
                {investmentData[itemIndex]?.vehicle.name}
              </Text>
              <ChipStateColor
                title={investmentData[itemIndex]?.vehicle.state.name}
                primaryColor={investmentData[itemIndex]?.vehicle.state.color}
              />
            </View>
            <View
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                marginVertical: 4,
              }}
            >
              <View>
                <Text variant="R7" color="NEUTRALS_600">
                  Año
                </Text>
                <Text variant="R6">
                  {investmentData[itemIndex]?.vehicle.manufactureYear}
                </Text>
              </View>
              <View>
                <Text variant="R7" color="NEUTRALS_600">
                  Patente
                </Text>
                <Text variant="R6">
                  {licensePlateToShow(
                    investmentData[itemIndex]?.vehicle.licensePlate || '-',
                  )}
                </Text>
              </View>
              <View>
                <Text variant="R7" color="NEUTRALS_600">
                  Nº motor
                </Text>
                <Text variant="R6">
                  {engineNumberToShow(
                    investmentData[itemIndex]?.vehicle.engineNumber || '-',
                  )}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
      <InvestmentDetails investment={investmentData[itemIndex]} />
      <View style={{ flexDirection: 'row', gap: 8 }}>
        <View style={{ flex: 1 }}>
          <ButtonOutline variant="B6" onPress={() => { }} text="Vender" />
        </View>
        <View style={{ flex: 1 }}>
          <Button onPress={navigateToInvestment} text="Comprar más" />
        </View>
      </View>
      <ButtonOutline
        variant="B6"
        outline
        onPress={() => { }}
        text="Términos y condiciones"
      />
      <CarsActivity />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  investmentBox: {
    borderRadius: 16,
    backgroundColor: color.NEUTRALS_50,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    padding: 16,
  },
  investmentNumbers: {
    paddingTop: 4,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  containter: {
    backgroundColor: color.WHITE,
    padding: 16,
    gap: 24,
    flexGrow: 1,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: color.PRIMARY_700,
  },
  activeIndicator: {
    position: 'absolute',
    bottom: 0,
    height: 2,
    width: '100%',
  },
  ///////////////////////
  carContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 1000,
    position: 'absolute',
    top: -24,
    width: '100%',
  },
  cardContainer: {
    padding: 16,
    marginTop: 80,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
    borderRadius: 16,
    // backgroundColor: 'red',
    // marginTop: 48,
    zIndex: 1,
  },
  card: {
    borderRadius: 10,
    paddingTop: 20,
  },
  carImage: {
    width: 218,
    height: 139,
    alignSelf: 'center',
  },

  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#eee',
    borderRadius: 4,
    overflow: 'hidden',
    marginVertical: 8,
  },
  progressBar: {
    width: '20%',
    height: '100%',
    backgroundColor: '#D50000',
  },
  /////
  miniCard: {
    padding: 16,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
    borderRadius: 16,
    marginTop: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  miniImage: {
    width: 66,
    height: 42,
  },
});
