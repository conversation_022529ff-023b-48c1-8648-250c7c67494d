import React, { FC, useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  ActivityIndicator,
  StyleSheet,
  StatusBar,
  ScrollView,
  RefreshControl,
  AppState,
  Pressable,
  Image,
} from 'react-native';
import {
  HeaderHome,
  LastMovementsCard,
  TotalBalanceCard,
} from '../components/organisms';
import RecentTransferCard from '../components/organisms/Cards/RecentTransfer';
import OnboardingScreen from '../components/organisms/Cards/Onboarding';
import { getUserInfo } from '../services/users';
import { getTransactions } from '../services/transactions';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { useUserContext } from '../context/UserContext';
import { Transactions } from '../types/Transfer';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackHomeParams } from '../types/StackHome/StackHomeParams';
import { CvuAccount } from '../types/User';
import HoldOnboarding from '../components/organisms/Cards/HoldOnboarding';
import useDisableBackButton from '../hooks/utils/useDisableBackButton';
import { Text, TextBase } from '../components/atoms';
import AlertIcon from '../components/atoms/Icons/AlertIcon';
import CardsBanner from '../components/screens/home/<USER>';
import { Cards } from '../types/StackCards/Cards';
import color from '../theme/pallets/pallet';
import ArrowRightSmall from '../components/atoms/Icons/ArrowRightSmall';
import ButtonOutline from '../components/atoms/ButtonOutline';
import { CashInIcon, TransferIcon } from '../components/atoms/Icons';
import { HomeCard } from '../assets/img/homeCard';

type HomeProps = NativeStackScreenProps<StackHomeParams, 'Home'>;

const HomeScreen: FC<HomeProps> = ({ navigation }: HomeProps) => {
  const isFocused = useIsFocused();

  const { user, setUser } = useUserContext();
  const [transfers, setTransfers] = useState<Transactions[]>();
  const [cvuUser, setCvuUser] = useState<CvuAccount[]>();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [cuitUser, setCuitUser] = useState<any>();
  const [onboardingStatus, setOnboardingStatus] = useState<any>();
  const [cards, setCards] = useState<Cards>();
  useDisableBackButton();
  const appState = useRef(AppState.currentState);

  const navigateToScreen = (screen: any) => {
    navigation.navigate(screen);
  };

  const navigateToTransfer = () => {
    navigation.navigate('StackTransfer', {
      screen: 'TransferScreen',
    });
  };

  const navigateToLoansHome = () => {
    navigation.navigate('StackLoan', {
      screen: 'LoansHomeScreen',
    });
  };

  const navigateToCashIn = () => {
    navigation.navigate('StackCashIn', {
      screen: 'CashInScreen',
    });
  };

  const navigateToQr = () => {
    navigation.navigate('StackQrPayment', {
      screen: 'QrScreen',
    });
  };

  const navigateToRequestCardScreen = () => {
    const hasCards =
      cards?.card_list !== undefined && cards?.card_list?.length > 0;
    const hasCardsShipping =
      user.cards?.shipping !== undefined && user.cards?.shipping.length > 0;

    if (hasCards) {
      navigation.navigate('StackCards', {
        screen: 'CardDetail',
        params: { hasCardsShipping },
      });
    } else {
      navigation.navigate('StackCards', {
        screen: 'RequestCardIntroScreen',
      });
    }
  };

  const navigateToOnboarding = () => {
    navigation.navigate('WebViewScreen', { link: user?.linkOnboarding! });
  };

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const items = await getTransactions(5);
      setTransfers(items);

      const userInfo = await getUserInfo();

      setUser({
        ...user,
        nombre: userInfo?.userDetails.nombre,
        apellido: userInfo?.userDetails.apellido,
        balance: userInfo?.balance,
        onboardingCompleted: userInfo?.userOnboarding.onboardingCompleted,
        linkOnboarding: userInfo?.linkOnboarding,
        onboardingStatus: userInfo?.userOnboarding?.onboardingStatus,
        cuit: userInfo?.userDetails?.cuit,
        cvu: userInfo?.cvuUser,
        cards: userInfo?.cards,
        email: userInfo?.userDetails.email,
      });
      setCvuUser(userInfo?.userDetails?.cvuAccounts);
      setCuitUser(userInfo?.userDetails?.cuit);
      setOnboardingStatus(userInfo?.userOnboarding?.onboardingStatus);
      setCards(userInfo?.cards);
      setLoading(false);
      setRefreshing(false);
    } catch (error) {
      console.error('Error fetching data:', error);
      setLoading(false);
      setRefreshing(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  useEffect(() => {
    const subscription = AppState.addEventListener('change', nextAppState => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active'
      ) {
        fetchData();
      }
      appState.current = nextAppState;
    });

    return () => {
      subscription.remove();
    };
  }, [fetchData]);

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [fetchData]),
  );

  const approved = onboardingStatus === 'approved';
  const pending = onboardingStatus === 'to check';
  const rejected =
    onboardingStatus === 'rejected' ||
    onboardingStatus === 'cancelled' ||
    onboardingStatus === 'pending' ||
    onboardingStatus === 'not verified' ||
    onboardingStatus === 'in progress';
  return (
    <View style={styles.containter}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            colors={[color.WHITE]}
            onRefresh={onRefresh}
            progressBackgroundColor={color.PRIMARY_500}
          />
        }
      >
        {isFocused && (
          <StatusBar
            backgroundColor={color.PRIMARY_500}
            barStyle="light-content"
          />
        )}
        <View
          style={{
            backgroundColor: color.NEUTRALS_50,
            borderBottomStartRadius: 16,
            borderBottomEndRadius: 16,
            borderColor: color.NEUTRALS_200,
          }}
        >
          <HeaderHome
            nombre={user.nombre}
            apellido={user.apellido}
            loading={loading}
            navigate={navigateToScreen}
            disabled={pending || rejected}
          />
          <View style={styles.investmentContainer}>
            <TotalBalanceCard
              balance={user.balance}
              loading={loading}
              navigateToTransfer={navigateToTransfer}
              navigateToCashIn={navigateToCashIn}
              navigateToQr={navigateToQr}
              onboardingStatus={onboardingStatus}
              user={user}
              cvuUser={cvuUser}
              cuitUser={cuitUser}
            />
          </View>
        </View>
        {approved ? (
          <>
            {!loading && (
              <>
                {/* <CardsBanner
                  cards={cards?.card_list}
                  navigateToRequestCardScreen={navigateToRequestCardScreen}
                /> */}
                {/* <RecentTransferCard /> */}
                <View
                  style={{
                    padding: 16,
                    gap: 16,
                    borderBottomStartRadius: 16,
                    borderBottomEndRadius: 16,
                    borderWidth: 0.3,
                    borderColor: color.NEUTRALS_200,
                    marginBottom: 12,
                    borderTopWidth: 0,
                  }}
                >
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <Text variant="R7" color="NEUTRALS_600">
                      Tu dinero disponible
                    </Text>
                    <ArrowRightSmall size={18} />
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <Text variant="B6" color="NEUTRALS_600">
                      Pesos argentinos
                    </Text>
                    <Text variant="B6">0,00</Text>
                  </View>
                  <View
                    style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <Text variant="B6" color="NEUTRALS_600">
                      Dólares estadounidenses
                    </Text>
                    <Text variant="B6">0,00</Text>
                  </View>
                  <View style={{ flexDirection: 'row', gap: 8 }}>
                    <View style={{ flex: 1 }}>
                      <ButtonOutline
                        icon={
                          <CashInIcon color={color.PRIMARY_500} size={13} />
                        }
                        neutralBorder
                        onPress={navigateToCashIn}
                        text="Ingresar dinero"
                      />
                    </View>
                    <View style={{ flex: 1 }}>
                      <ButtonOutline
                        icon={
                          <TransferIcon color={color.PRIMARY_500} size={20} />
                        }
                        neutralBorder
                        onPress={navigateToTransfer}
                        text="Transferir"
                      />
                    </View>
                  </View>
                </View>
                <View>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    <View style={styles.shadowCard}>
                      <Image
                        source={require('../assets/img/homeCard1.png')}
                        style={styles.image}
                      />
                    </View>
                    <View style={styles.shadowCard}>
                      <Image
                        source={require('../assets/img/homeCard1.png')}
                        style={styles.image}
                      />
                    </View>
                  </ScrollView>
                </View>
                <View style={{ padding: 16 }}>
                  <LastMovementsCard
                    navigationToActivity={navigateToScreen}
                    transfers={transfers!}
                    loading={loading}
                  />
                </View>
              </>
            )}
          </>
        ) : pending ? (
          <HoldOnboarding />
        ) : (
          rejected && (
            <OnboardingScreen navigateToOnboarding={navigateToOnboarding} />
          )
        )}
      </ScrollView>
    </View>
  );
};

export default HomeScreen;

const styles = StyleSheet.create({
  containter: {
    flex: 1,
    backgroundColor: color.WHITE,
  },
  investmentContainer: {
    alignSelf: 'center',
    width: '100%',
  },
  indicator: {
    height: 100,
    justifyContent: 'center',
  },
  loanCard: {
    backgroundColor: '#F7F8FE',
    borderRadius: 10,
    borderColor: '#E1E3ED',
    borderWidth: 1,
    borderStyle: 'solid',
    margin: 10,
    padding: 15,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  shadowCard: {
    paddingBottom: 2,
    marginLeft: 10,
    marginBottom: 12,
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8, // para Android
  },
  image: {
    borderRadius: 12,
  },
});
