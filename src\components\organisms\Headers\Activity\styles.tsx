import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: color.WHITE,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  containerFilter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  filter: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
    height: 40,
    borderRadius: 4,
    paddingHorizontal: 12,
  },
  flex1: {
    flex: 1,
  },
  gap8: {
    gap: 8,
  },
});
