import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    padding: 24,
    backgroundColor: color.NEUTRALS_50,
    borderBottomStartRadius: 16,
    borderBottomEndRadius: 16,
    borderWidth: 0.3,
    borderColor: color.NEUTRALS_200,
  },

  mv8: {
    marginVertical: 8,
  },
  mt18: {
    marginBottom: 18,
  },
  itemsCenter: {
    alignItems: 'center',
  },
  flexRow: {
    flexDirection: 'row',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 24,
  },
  skeleton: {
    height: 40,
  },
  separator: {
    height: 24,
    width: 1,
    backgroundColor: color.PRIMARY_500,
  },
  textTop: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cvuText: {
    padding: 5,
    backgroundColor: color.PRIMARY_900,
    color: color.PRIMARY_50,
    fontFamily: 'Satoshi-Bold',
    borderRadius: 5,
    paddingHorizontal: 10,
  },
});
