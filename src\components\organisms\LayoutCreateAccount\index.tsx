import {
  View,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import { styles } from './styles';
import { useNavigation } from '@react-navigation/native';
import KeyboardAvoidingComponent from '../../molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { Button, Text } from '../../atoms';

interface CheckboxProps {
  checked: boolean;
  onChange: () => void;
  labelT: string;
  labelP: string;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  labelT,
  labelP,
}) => {
  const navigation: any = useNavigation();
  const goToTerms = () => {
    navigation.navigate('TermsScreen');
  };
  return (
    <View style={styles.termsContainer}>
      <TouchableOpacity onPress={() => onChange()}>
        <View style={[styles.checkbox, checked && styles.checked]}>
          {checked && <View style={styles.checkmark} />}
        </View>
      </TouchableOpacity>
      <View style={styles.termsTextContainer}>
        <Text style={styles.termsTxt}>Acepto </Text>
        <TouchableOpacity onPress={() => goToTerms()}>
          <Text style={styles.terms}>{labelT}</Text>
        </TouchableOpacity>
        <Text style={styles.termsTxt}> y la </Text>
        <TouchableOpacity onPress={() => goToTerms()}>
          <Text style={styles.terms}>{labelP}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

type Props = {
  children: React.JSX.Element;
  tyc?: {
    isChecked: boolean;
    check: () => void;
  };
  buttons: {
    primary: {
      text: string;
      onPress: () => void;
      loading?: boolean;
      disabled?: boolean;
    };
  };
};

export default function Layout({ children, buttons, tyc }: Props) {
  return (
    <KeyboardAvoidingComponent collapse>
      <ScrollView contentContainerStyle={styles.container}>
        <View>
          <Text variant="B3">Crea una cuenta</Text>
          <Text variant="R6">Solo te llevará algunos minutos</Text>
          {children}
        </View>
        <View>
          {tyc && (
            <View style={styles.checkboxStyle}>
              <Checkbox
                labelT="términos y condiciones"
                labelP="política de privacidad"
                checked={tyc.isChecked}
                onChange={tyc.check}
              />
            </View>
          )}
          {buttons && (
            <Button
              text={buttons.primary.text}
              onPress={buttons.primary.onPress}
              loading={buttons.primary.loading}
              disabled={buttons.primary.disabled}
            />
          )}
        </View>
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
}
