import React, { useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  FlatList,
  LogBox,
  Pressable,
  Modal,
} from 'react-native';
import { TextBase } from '../../../atoms';
import { styles } from './styles';
import color from '../../../../theme/pallets/pallet';

interface Props {
  label?: string;
  data: string[];
  onChange: (key: string, value: string) => void;
  name?: string;
  showDropdown: boolean;
  setShowDropdown: (value: boolean) => void;
}

export default function SelectCodIos({
  label,
  data,
  onChange,
  name,
  showDropdown,
  setShowDropdown,
}: Props) {
  const onChangeInput = (value: string) => {
    setShowDropdown(false);
    onChange(name!, value);
  };

  useEffect(() => {
    LogBox.ignoreLogs(['VirtualizedLists should never be nested']);
  }, []);

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={showDropdown}
      onRequestClose={() => setShowDropdown(false)}
    >
      <Pressable
        style={{
          gap: 6,
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: 8,
        }}
        onPress={() => setShowDropdown(true)}
      >
        {label && <TextBase size="s">{label}</TextBase>}

        {showDropdown && (
          <View
            style={{
              height: '80%',
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: 4,
              borderWidth: 1,
              borderColor: color.NEUTRALS_100,
            }}
          >
            <FlatList
              data={data}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.modalOption}
                  onPress={() => onChangeInput(item)}
                >
                  <Text
                    style={{
                      textAlign: 'left',
                      fontFamily: 'Satoshi-Regular',
                      fontSize: 12,
                      color: color.NEUTRALS_800,
                    }}
                  >
                    {item}
                  </Text>
                </TouchableOpacity>
              )}
              keyExtractor={(item, index) => index.toString()}
            />
          </View>
        )}
      </Pressable>
    </Modal>
  );
}
