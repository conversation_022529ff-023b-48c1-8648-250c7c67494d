/* eslint-disable react-native/no-inline-styles */
import {
  Animated,
  Image,
  NativeScrollEvent,
  NativeSyntheticEvent,
  Pressable,
  ScrollView,
  StyleSheet,
  View,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Text } from '../../components/atoms';
import color from '../../theme/pallets/pallet';
import TrendingUpIcon from '../../components/atoms/Icons/TrendingUpIcon';
import DolarIcon from '../../components/atoms/Icons/DolarIcon';
import CalendarIcon from '../../components/atoms/Icons/CalendarIcon';
import StarCheckIcon from '../../components/atoms/Icons/StarCheckIcon';
import CardDolarIcon from '../../components/atoms/Icons/CardDolarIcon';
import ListIcon from '../../components/atoms/Icons/ListIcon';
import {
  InvestmentBidding,
  InvestmentDetailData,
} from '../../types/Investments';
import { getFaq, getInvestmentDetail } from '../../services/investments';
import { formatCurrency } from '../../helpers/formatCurrency';
import { formatDateToNaturalLanguage } from '../../helpers/dateHelper';
import { SimpleAccordion } from '../../components/molecules/Accordion/Accordion';
import { Faq } from '../../types/Faq';
import GoBack from '../../components/organisms/Buttons/GoBackButton';
import ExpandIcon from '../../components/atoms/Icons/ExpandIcon';

const faqActive = 'Activo';

export default function InvestmentDetailScreen({ navigation, route }: any) {
  const param = route.params as InvestmentBidding;
  const [detail, setDetail] = useState<InvestmentDetailData>();
  const [faqs, setFaqs] = useState<Faq[]>([]);
  const [activeTab, setActiveTab] = useState<number>(0);
  const tabs = [
    'Descripción general',
    'Tokens disponibles',
    'Preguntas frecuentes',
  ];

  const getDetail = async () => {
    const investment = await getInvestmentDetail(param.id);
    setDetail(investment?.data);
    const faq = await getFaq();
    setFaqs(faq?.data.items || []);
  };

  useEffect(() => {
    getDetail();
  }, []);
  const scrollViewRef = useRef<ScrollView>(null);

  const infoData = [
    {
      icon: <CalendarIcon size={20} color={color.PRIMARY_500} />,
      title: 'Disponible hasta',
      description: detail?.biddingEndDate
        ? formatDateToNaturalLanguage(detail?.biddingEndDate)
        : '-',
    },
    {
      icon: <DolarIcon size={20} color={color.PRIMARY_500} />,
      title: 'Ganancias estimadas',
      description: formatCurrency(detail?.goal) || '-',
    },
    {
      icon: <TrendingUpIcon size={20} color={color.PRIMARY_500} />,
      title: 'Frecuencia de pago',
      description: 'Mensualmente',
    },
  ];

  const tokenData = [
    {
      icon: <StarCheckIcon size={20} color={color.PRIMARY_500} />,
      title: 'Total de tokens',
      description: detail?.tokens?.totalTokenQuantity || '-',
    },
    {
      icon: <CardDolarIcon size={20} color={color.PRIMARY_500} />,
      title: 'Valor del token',
      description: detail?.tokens?.unitValue
        ? `ARS ${detail?.tokens?.unitValue.toLocaleString('es-AR')}`
        : '-',
    },
    {
      icon: <ListIcon size={20} color={color.PRIMARY_500} />,
      title: 'Titularidad',
      description:
        'Cobras ganancias hasta que lo vendas o transfieras a otra persona',
    },
  ];

  const handleTabPress = (index: number) => {
    setActiveTab(index);

    // Asegúrate de centrar la pestaña activa
    scrollViewRef.current?.scrollTo({
      x: index * 100 - (150 - 100), // Ajusta el cálculo según el tamaño de la pestaña
      animated: true,
    });
  };

  const handleNavigateToInvestAmount = () => {
    navigation.navigate('InvestmentAmountScreen');
  };

  const [imageY, setImageY] = useState(0);
  const [showHeaderImage, setShowHeaderImage] = useState(false);
  const scrollY = useRef(new Animated.Value(0)).current;

  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    {
      useNativeDriver: false,
      listener: (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        const y = event.nativeEvent.contentOffset.y;
        if (y > imageY + 5) {
          setShowHeaderImage(true);
        }
      },
    },
  );

  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'space-between',
      }}
    >
      <View style={{ padding: 16 }}>
        <GoBack />
      </View>
      <Pressable
        style={{
          paddingHorizontal: 16,
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'flex-end',
        }}
        onPress={() => setShowHeaderImage(false)}
      >
        <Text variant="B2">{param.vehicle.name}</Text>
        {showHeaderImage && (
          <View>
            <ExpandIcon size={18} color={color.BLACK} />

            <View style={{ position: 'relative', width: 108, marginLeft: 30 }}>
              <Image
                source={{ uri: param.vehicle.model.imageUrl }}
                style={{
                  position: 'absolute',
                  bottom: 0,
                  right: 0,
                  width: 108,
                  height: 69,
                }}
                resizeMode="contain"
              />
            </View>
          </View>
        )}
      </Pressable>
      <ScrollView
        onScroll={handleScroll}
        style={{ gap: 24, marginHorizontal: 16, flex: 1 }}
      >
        {!showHeaderImage && (
          <View
            onLayout={e => {
              const layout = e.nativeEvent.layout;
              setImageY(layout.y);
            }}
            style={{ alignItems: 'center', paddingTop: 24 }}
          >
            <Image
              source={{ uri: param.vehicle.model.imageUrl }}
              style={{ width: 218, height: 139 }}
              resizeMode="contain"
            />
          </View>
        )}
        <View style={{ paddingVertical: 24 }}>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.scrollContainer}
          >
            {tabs.map((tab, index) => (
              <Pressable
                key={index}
                onPress={() => handleTabPress(index)}
                style={[
                  styles.tabButton,
                  activeTab === index && styles.underline,
                ]}
              >
                <Text
                  variant={activeTab === index ? 'B6' : 'R6'}
                  color={activeTab === index ? 'PRIMARY_700' : 'NEUTRALS_800'}
                >
                  {tab}
                </Text>
                {/* {activeTab === index && <View style={styles.underline} />} */}
              </Pressable>
            ))}
          </ScrollView>
        </View>
        <View style={{ gap: 8, paddingBottom: 16 }}>
          {activeTab === 0 && (
            <>
              <Text variant="B6">Invierte colectivamente</Text>
              <Text variant="R7">
                Sumate a invertir en la industria de transporte mediante la
                compra de tokens de este vehículo.
              </Text>
              {infoData.map((info, index) => (
                <View
                  key={index}
                  style={{
                    padding: 8,
                    borderBottomColor: color.NEUTRALS_200,
                    borderBottomWidth: 1,
                    flexDirection: 'row',
                    gap: 8,
                    alignItems: 'center',
                  }}
                >
                  {info.icon && <>{info.icon}</>}
                  <View>
                    <Text variant="R7" color="NEUTRALS_800">
                      {info.title}
                    </Text>
                    <Text variant="B7" color="NEUTRALS_800">
                      {info.description}
                    </Text>
                  </View>
                </View>
              ))}
              <Text variant="R7" color="NEUTRALS_600">
                (*) Puedes vender tu token en el marketplace para obtener
                liquidez en el momento que lo necesites.
              </Text>
            </>
          )}
          {activeTab === 1 && (
            <>
              <View style={[styles.tokenBadge, { alignSelf: 'flex-start' }]}>
                <Text variant="R7" color="PRIMARY_700">
                  {detail?.tokens?.availableTokenQuantity || '-'} tokens
                  disponibles
                </Text>
              </View>
              <Text variant="R7">
                Cada token te da la posibilidad de acceder a un rendimiento
                mensual de las ganancias del alquiler del vehículo en Cabify.
                ¡Cuanto más tokens más rendimiento!
              </Text>
              {tokenData.map((info, index) => (
                <View
                  key={index}
                  style={{
                    padding: 8,
                    borderBottomColor: color.NEUTRALS_200,
                    borderBottomWidth: 1,
                    flexDirection: 'row',
                    gap: 8,
                    alignItems: 'center',
                  }}
                >
                  {info.icon && <>{info.icon}</>}
                  <View style={{ width: '70%' }}>
                    <Text variant="R7" color="NEUTRALS_800">
                      {info.title}
                    </Text>
                    <Text variant="B7" color="NEUTRALS_800">
                      {info.description}
                    </Text>
                  </View>
                </View>
              ))}
            </>
          )}
          {activeTab === 2 && (
            <View style={{ gap: 16 }}>
              {faqs.map(
                (faq, index) =>
                  faq.status === faqActive && (
                    <SimpleAccordion
                      key={`${index}-${faq.id}`}
                      title={faq.question}
                      content={faq.answer}
                    />
                  ),
              )}
              <Text variant="R7">
                Si aún tenés dudas o consultas, estamos para ayudarte a tomar la
                mejor decisión de inversión.
              </Text>
              <Text variant="B6" color="PRIMARY_500">
                Contactar a un asesor
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
      <View
        style={{
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderWidth: 1,
          borderColor: color.NEUTRALS_200,
          padding: 16,
          gap: 16,
        }}
      >
        <View>
          <View
            style={{ flexDirection: 'row', justifyContent: 'space-between' }}
          >
            <View style={{ flexDirection: 'row' }}>
              <Text variant="R7">Invertidos: </Text>
              <Text variant="B7" color="PRIMARY_500">
                ARS {formatCurrency(detail?.tokens?.soldTokenAmount)}
              </Text>
            </View>
            <View style={{ flexDirection: 'row' }}>
              <Text variant="R7">Meta: </Text>
              <Text variant="B7" color="PRIMARY_500">
                ARS {formatCurrency(detail?.goal)}
              </Text>
            </View>
          </View>
          <View style={styles.progressBarContainer}>
            <View
              style={{
                ...styles.progressBar,
                width: `${detail?.tokens?.soldTokenPercentage || 0}%`,
              }}
            />
          </View>
          <Text variant="R7">
            <Text variant="B7" color="PRIMARY_500">
              {detail?.tokens?.soldTokenPercentage || '-'}%
            </Text>{' '}
            invertido{' '}
          </Text>
          <View style={styles.row}>
            <View style={[styles.tokenBadge, { alignSelf: 'flex-end' }]}>
              <Text variant="R7" color="PRIMARY_700">
                {detail?.tokens?.availableTokenQuantity || '-'} tokens
                disponibles
              </Text>
            </View>
            <View>
              <Text variant="B5" align="center" color="PRIMARY_500">
                {detail?.returnRangeMin}% - {detail?.returnRangeMax}%
              </Text>
              <Text variant="R7">Rendimiento mensual</Text>
            </View>
          </View>
        </View>
        <Button
          text="Invertir en este vehículo"
          onPress={handleNavigateToInvestAmount}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  scrollContainer: {
    flexDirection: 'row',
  },
  tabButton: {
    padding: 11,
    alignItems: 'center',
  },
  activeTabText: {
    color: '#D50000',
    fontWeight: 'bold',
  },
  underline: {
    borderBottomWidth: 2,
    borderBottomColor: color.PRIMARY_700,
  },

  progressBarContainer: {
    height: 8,
    backgroundColor: '#eee',
    borderRadius: 4,
    overflow: 'hidden',
    marginVertical: 8,
  },
  progressBar: {
    width: '20%',
    height: '100%',
    backgroundColor: color.PRIMARY_500,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  tokenBadge: {
    backgroundColor: color.PRIMARY_50,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
});
