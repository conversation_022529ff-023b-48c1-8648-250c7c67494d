import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const ArrowLeft: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 8 11">
      <Path
        d="M3.02329 5.49999L7.14829 9.62499L5.96996 10.8033L0.666626 5.49999L5.96996 0.196655L7.14829 1.37499L3.02329 5.49999Z"
        fill={color}
      />
    </Svg>
  );
};

export default ArrowLeft;
