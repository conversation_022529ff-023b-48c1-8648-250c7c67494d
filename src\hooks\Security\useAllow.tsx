/* eslint-disable @typescript-eslint/no-shadow */
import DeviceInfo from 'react-native-device-info';
import Config from 'react-native-config';
import { useEffect, useState } from 'react';
import JailMonkey from 'jail-monkey';

const useAllow = () => {
  const [isEmulator, setIsEmulator] = useState(false);
  const [isDebugMode, setIsDebugMode] = useState(false);
  const [isJailBroken, setIsJailBroken] = useState(false);
  const [isAdbEnabled, setIsAdbEnabled] = useState(false);
  const [isAllowed, setIsAllowed] = useState(true);
  const [isLoadingAllowing, setIsLoadingAllowing] = useState(true);

  const allowEmulator = Config.ALLOW_EMULATOR === 'true';

  useEffect(() => {
    const verify = async () => {
      setIsLoadingAllowing(true);

      const isEmulator = DeviceInfo.isEmulator();
      const isDebugMode = JailMonkey.isDebuggedMode();
      const isJailBroken = JailMonkey.isJailBroken();
      const isAdbEnabled = JailMonkey.AdbEnabled();
      Promise.all([isEmulator, isDebugMode, isJailBroken, isAdbEnabled])
        .then(permissions => {
          const [isEmulator, isDebugMode, isJailBroken, isAdbEnabled] =
            permissions;
          setIsEmulator(isEmulator);
          setIsDebugMode(isDebugMode);
          setIsJailBroken(isJailBroken);
          setIsAdbEnabled(isAdbEnabled);
          if (
            (isEmulator || isDebugMode || isJailBroken || isAdbEnabled) &&
            !allowEmulator
          ) {
            setIsAllowed(false);
          }
        })
        .catch(error => {
          console.error('error verifying device', error);
        })
        .finally(() => {
          setIsLoadingAllowing(false);
        });
    };

    verify();
  }, [allowEmulator]);

  return {
    isAllowed,
    isLoadingAllowing,
    isDebugMode,
    isEmulator,
    isJailBroken,
    isAdbEnabled,
  };
};

export default useAllow;
