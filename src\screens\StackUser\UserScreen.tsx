import React, { useCallback, useState } from 'react';
import { View, ScrollView, Pressable, StyleSheet } from 'react-native';
import { TextBase, UserIcon } from '../../components/atoms';
import ButtonWithIcon from '../../components/atoms/ButtonWithIcon';
import QrIconButoon from '../../components/atoms/Icons/QrIconButoon';
import AddFriendIcon from '../../components/atoms/Icons/AddFriendIcon';
import { ArrowRightLarge } from '../../components/atoms/Icons';
import EditUserIcon from '../../components/atoms/Icons/EditUserIcon';
import ChangePasswordIcon from '../../components/atoms/Icons/ChangePasswordIcon';
import SetBankIcon from '../../components/atoms/Icons/SetBankIcon';
import ConfigNotificationsIcon from '../../components/atoms/Icons/ConfigNotificationsIcon';
import HelpIcon from '../../components/atoms/Icons/HelpIcon';
import LogOutIcon from '../../components/atoms/Icons/LogOutIcon';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { getUserInfo } from '../../services/users';
import { UserDetail } from '../../types/User';
import { useFocusEffect } from '@react-navigation/native';
import { StackUserParams } from '../../types/StackUser/StackUserParams';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<StackUserParams, 'EditProfile'>;

export const UserScreen = ({ navigation }: Props) => {
  const [user, setUser] = useState<UserDetail>();

  const getUser = useCallback(async () => {
    const res = await getUserInfo();
    setUser(res?.userDetails);
  }, []);

  useFocusEffect(
    useCallback(() => {
      getUser();
    }, [getUser]),
  );

  const list = [
    {
      id: 1,
      title: 'Editar perfil',
      icon: <EditUserIcon size={24} />,
      onPress: () => navigation.navigate('EditProfile', { user: user! }),
    },
    {
      id: 2,
      title: 'Cambiar Contraseña',
      icon: <ChangePasswordIcon size={24} />,
    },
    {
      id: 3,
      title: 'Vincular Banco',
      icon: <SetBankIcon size={24} />,
    },
    {
      id: 4,
      title: 'Configurar notificaciones',
      icon: <ConfigNotificationsIcon size={24} />,
    },
    {
      id: 5,
      title: 'Centro de Ayuda',
      icon: <HelpIcon size={24} />,
    },
    {
      id: 6,
      title: 'Cerrar sesión',
      icon: <LogOutIcon size={24} />,
    },
  ];

  return (
    <ScrollView>
      <View style={styles.topContainer}>
        <UserIcon usernameInitials={'LM'} />
        <TextBase color={color.WHITE} type="Bold">
          {user?.nombre} {user?.apellido}
        </TextBase>
        <TextBase color={color.WHITE} size="s" type="Bold">
          {user?.email}
        </TextBase>
        <View style={styles.buttonContainer}>
          <ButtonWithIcon
            bgColor={color.PRIMARY_700}
            color={color.WHITE}
            icon={<AddFriendIcon size={24} color={color.WHITE} />}
            text="Invitar amigos"
            onPress={() => {}}
            extraStyles={styles.flex1}
          />
          <ButtonWithIcon
            bgColor={color.PRIMARY_700}
            color={color.WHITE}
            icon={<QrIconButoon size={24} color={color.WHITE} />}
            text="Compartir QR"
            onPress={() => {}}
            extraStyles={styles.flex1}
          />
        </View>
      </View>
      <View style={styles.botContainer}>
        {list.map(item => (
          <Pressable
            key={item.id}
            style={styles.itemContainer}
            onPress={item.onPress}
          >
            <View style={styles.item}>
              {item.icon && item.icon}
              <TextBase color={color.NEUTRALS_800} size="s" type="Bold">
                {item.title}
              </TextBase>
            </View>
            <ArrowRightLarge size={16} color={color.NEUTRALS_800} />
          </Pressable>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  topContainer: {
    alignItems: 'center',
    backgroundColor: color.PRIMARY_500,
    gap: 16,
    paddingTop: 16,
    paddingBottom: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 16,
    marginHorizontal: 16,
    flex: 1,
  },
  botContainer: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  itemContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
    borderRadius: 8,
    marginBottom: 16,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  flex1: {
    flex: 1,
  },
});
