import React, { createContext, useContext, useState } from 'react';
import { Cards } from '../types/StackCards/Cards';

type Props = { children?: React.ReactNode };

interface UserContext {
  user: {
    userId: string;
    nombre: string;
    apellido: string;
    balance: number;
    onboardingCompleted: boolean;
    linkOnboarding?: string;
    onboardingStatus?: string;
    cuit: any;
    cvu: any;
    cards: Cards;
    email: string;
  };
  setUser: (user: any) => void;
}

const UserContext = createContext<UserContext>({} as UserContext);

export const useUserContext = () => useContext(UserContext);

export function UserProvider({ children }: Props) {
  const initialState = {
    userId: '',
    nombre: '',
    apellido: '',
    balance: 0,
    onboardingCompleted: false,
    linkOnboarding: '',
    cuit: '',
    cvu: '',
    email: '',
  };

  const [user, setUser] = useState(initialState);

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
      }}
    >
      {children}
    </UserContext.Provider>
  );
}
