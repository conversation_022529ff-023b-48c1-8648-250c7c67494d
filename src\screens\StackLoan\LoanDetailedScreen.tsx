import React, { FC } from 'react';
import {
  View,
  Text,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import { TextBase } from '../../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { colors } from '../../constants/colors';
import LoanTypeIcon from '../../components/atoms/Icons/LoanTypeIcon';
import {
  getLoanTypeTranslation,
  LoanType,
} from '../../types/StackLoan/LoanTypes';
import { formatCurrency } from '../../helpers/formatCurrency';
import { useLoanDetail } from '../../hooks/Loans/useLoanDetail';
import {
  calculateDaysUntil,
  formatDateToDDMMYYYY,
  getCurrentMonthDays,
  isDateBeyondNow,
} from '../../helpers/dateHelper';
import GreenCheck from '../../components/atoms/Icons/GreenCheck';
import AlertRounded from '../../components/atoms/Icons/AlertRounded';
import WatchGrey from '../../components/atoms/Icons/WatchGrey';
import DollarRoundedYellow from '../../components/atoms/Icons/DollarRoundedYellow';
import { LoanDetailedScreenStyles as styles } from './styles';
import LoanStatusStack from './LoanStatutsStack';
import QuotaProgressBar from './QuotaProgressBar';
import { InstallmentStatus } from '../../types/StackLoan/LoanInstallment';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const LoanDetailedScreen = ({ navigation, route }: Props) => {
  const { loanId } = route.params;
  const { loan, getLoanDetailedFromApi, loading } = useLoanDetail(loanId);

  const Installment: FC<{
    installmentNumber: number;
    status: InstallmentStatus;
    totalInstallments: number;
    amount: number;
    estimatedPaymentDate: Date;
    nextToBePaid: boolean;
  }> = ({
    amount,
    status,
    estimatedPaymentDate,
    installmentNumber,
    totalInstallments,
    nextToBePaid,
  }) => {
    let StateIcon = DollarRoundedYellow;
    let stateText = 'Vence';

    switch (status) {
      case 'pending':
        StateIcon = WatchGrey;
        stateText = 'Vence';
        break;
      case 'paid':
        StateIcon = GreenCheck;
        stateText = 'Venció';
        break;
      case 'expired':
        StateIcon = AlertRounded;
        stateText = 'Venció';
        break;
      default:
    }

    return (
      <View style={styles.cuotaContainer}>
        <View style={{ flexDirection: 'row', gap: 4 }}>
          <StateIcon />
          <View style={{ flexDirection: 'column' }}>
            <TextBase type="Bold" size="s">
              Cuota {installmentNumber} de {totalInstallments}
            </TextBase>
            <TextBase color={color.NEUTRALS_600}>
              {stateText} el {formatDateToDDMMYYYY(estimatedPaymentDate)}
            </TextBase>
          </View>
        </View>
        <TextBase size="s">$ {formatCurrency(amount)}</TextBase>
      </View>
    );
  };

  const LoadingIndicator = () => (
    <View style={styles.loadingIndicator}>
      <ActivityIndicator size={64} color={color.PRIMARY_700} />
    </View>
  );

  return loading ? (
    <LoadingIndicator />
  ) : (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={loading}
          colors={[color.WHITE]}
          onRefresh={getLoanDetailedFromApi}
          progressBackgroundColor={color.PRIMARY_500}
        />
      }
    >
      <Text style={styles.title}>Préstamos</Text>

      <View>
        <View style={{ gap: 10, marginBottom: 16 }}>
          <View style={styles.containerRow}>
            <View
              style={{ flexDirection: 'row', alignItems: 'center', gap: 6 }}
            >
              <LoanTypeIcon loanType={loan?.loanType as LoanType} />
              <Text style={styles.loanType}>
                {getLoanTypeTranslation(loan?.loanType as LoanType)}
              </Text>
            </View>
            <LoanStatusStack
              finished={!!loan?.finishDate}
              expiredInstallments={loan?.expired}
            />
          </View>
          {loan && (
            <QuotaProgressBar
              duesPaid={loan.duesPaid}
              currentInstallment={loan.nextPayment.index + 1}
              totalInstallments={loan.totalInstallments}
            />
          )}
        </View>

        <View style={styles.containerRow}>
          <View style={{ flexDirection: 'column', gap: 4 }}>
            <TextBase color={color.NEUTRALS_800}>Préstamo</TextBase>
            <TextBase color={color.NEUTRALS_600} size="s">
              Solcitado el{' '}
              {loan?.createdAt && formatDateToDDMMYYYY(loan.createdAt)}
            </TextBase>
          </View>
          <TextBase type="Bold">
            ${formatCurrency(Number(loan?.requestAmount))}
          </TextBase>
        </View>
        <View style={styles.divider} />

        <View style={{ gap: 6 }}>
          <View style={styles.containerRow}>
            <TextBase color={color.NEUTRALS_800}>Importe cuota</TextBase>
            <TextBase type="Bold">
              ${formatCurrency(Number(loan?.cuoteAmount))}
            </TextBase>
          </View>

          <View style={styles.containerRow}>
            <TextBase color={color.NEUTRALS_800}>Próximo vencimiento</TextBase>
            <TextBase type="Bold">
              {loan?.nextPayment &&
                formatDateToDDMMYYYY(loan?.nextPayment.date)}
            </TextBase>
          </View>

          <View style={styles.containerRow}>
            <TextBase color={color.NEUTRALS_800}>Pendientes</TextBase>
            <TextBase type="Bold">{loan?.pendingInstallments} cuotas</TextBase>
          </View>
        </View>
        <View style={styles.divider} />
      </View>

      <TextBase
        style={{ alignSelf: 'center' }}
        type="Bold"
        color={colors.negative}
      >
        Ver contrato
      </TextBase>

      <View style={{ marginTop: 24 }}>
        {loan?.installments &&
          loan.installments.map((quota, index) => (
            <View key={index}>
              <Installment
                installmentNumber={index + 1}
                status={quota.status}
                totalInstallments={loan.installments.length}
                amount={Number(quota.amount)}
                completedPaymentDate={quota.completedPaymentDate}
                estimatedPaymentDate={quota.estimatedPaymentDate}
                nextToBePaid={index === loan.nextPayment.index}
              />
              {loan.installments.length - 1 !== index && (
                <View style={styles.cuotaDivider} />
              )}
            </View>
          ))}
      </View>
    </ScrollView>
  );
};
