import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';

import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { WebView } from 'react-native-webview';
import { StackHomeParams } from '../../types/StackHome/StackHomeParams';

type Props = NativeStackScreenProps<StackHomeParams, 'WebViewScreen'>;

export const WebViewScreen = ({ navigation, route }: Props) => {
  const { link } = route.params;
  const [redirectToHome, setRedirectToHome] = useState(false);

  useEffect(() => {
    const handleNavigation = () => {
      navigation.navigate('Home');
    };

    if (redirectToHome) {
      handleNavigation();
    }
  }, [navigation, redirectToHome]);

  return (
    <View style={{ flex: 1 }}>
      <WebView
        source={{ uri: link }}
        allowsInlineMediaPlayback
        mediaPlaybackRequiresUserAction={false}
        style={{ flex: 1 }}
        onMessage={event => {
          setRedirectToHome(!!event.nativeEvent.data);
        }}
      />
    </View>
  );
};
