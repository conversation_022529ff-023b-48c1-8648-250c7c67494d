package com.toshify;

import android.provider.MediaStore;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.facebook.react.bridge.Callback;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

import org.jetbrains.annotations.NotNull;

public class SecretModule extends ReactContextBaseJavaModule {
    public SecretModule (@Nullable ReactApplicationContext reactContext){
        super(reactContext);
    }
    @Override
    public String getName() {
        return "SecretManager";
    }

    static {
        System.loadLibrary("native-lib");
    }
    public native String getSecretC();

    @ReactMethod
    public void getSecret(Callback cb) {
        try {
            String secret = getSecretC();
            cb.invoke(null, secret);
        } catch (Exception e) {
            cb.invoke(e.toString(), null);
        }
}
}
