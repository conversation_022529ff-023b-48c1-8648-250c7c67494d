import { View } from 'react-native';
import React from 'react';
import { Button, Text } from '../../components/atoms';
import color from '../../theme/pallets/pallet';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { CarsStackParams } from '../../navigation/stack-cars/types';

type Props = NativeStackScreenProps<CarsStackParams, 'CarsConfirmScreen'>;

export default function CarsConfirmScreen({ route, navigation }: Props) {
  const { type } = route.params;
  const handleNavigateToSuccess = () => {
    navigation.navigate('CarsSuccessScreen');
  };
  return (
    <View
      style={{
        flex: 1,
        paddingHorizontal: 16,
        justifyContent: 'space-between',
      }}
    >
      <View style={{ gap: 24 }}>
        <Text variant="B2">
          Confirma la {type === 'buy' ? 'compra' : 'venta'}
        </Text>
        <View
          style={{
            borderColor: color.NEUTRALS_200,
            borderWidth: 1,
            borderRadius: 16,
            padding: 16,
            flexDirection: 'row',
            gap: 60,
          }}
        >
          <View
            style={{
              borderLeftColor: color.PRIMARY_500,
              borderLeftWidth: 2,
              paddingHorizontal: 8,
            }}
          >
            <Text variant="R7">
              {type === 'buy' ? 'Compra' : 'Vender'} total
            </Text>
            <Text variant="B4" color="PRIMARY_500">
              1 Token
            </Text>
          </View>
          <View
            style={{
              borderLeftColor: color.PRIMARY_500,
              borderLeftWidth: 2,
              paddingHorizontal: 8,
            }}
          >
            <Text variant="R7">Total a recibir</Text>
            <Text variant="B4" color="PRIMARY_500">
              $ 500.000
            </Text>
          </View>
        </View>
      </View>
      <View
        style={{
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderWidth: 1,
          borderColor: color.NEUTRALS_200,
          padding: 16,
          marginHorizontal: -16,
          gap: 16,
        }}
      >
        <Button
          text={type === 'buy' ? 'Confirmar compra' : 'Confirmar venta'}
          onPress={handleNavigateToSuccess}
        />
      </View>
    </View>
  );
}
