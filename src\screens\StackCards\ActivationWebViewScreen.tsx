import React, { useEffect, useState } from 'react';
import { Alert, Linking, Pressable, StyleSheet, View } from 'react-native';

import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { WebView } from 'react-native-webview';
import { StackCardsParams } from '../../types/StackCards/StackCardsParams';
import { TextBase } from '../../components/atoms';

type Props = NativeStackScreenProps<
  StackCardsParams,
  'ActivationWebViewScreen'
>;

export const ActivationWebViewScreen = ({ route, navigation }: Props) => {
  const { link } = route.params;

  const handleNavigateToResultScreen = (result: 'success' | 'error') => {
    navigation.navigate('ActivationCardResultScreen', {
      result,
    });
  };

  const handleMessage = event => {
    const data = event.nativeEvent.data;
    console.log('🚀 ~ handleMessage ~ data:', data);
    if (data === 'Activated') {
      return handleNavigateToResultScreen('success');
    }
    if (data !== 'Activate') {
      return handleNavigateToResultScreen('error');
    }
  };

  // JavaScript para obtener el texto del cuerpo de la página
  const injectedJavaScript = `
    setInterval(() => {
      const textContent = document.body.innerText || document.body.textContent;
      window.ReactNativeWebView.postMessage(textContent);
    }, 3000); // Ejecuta cada 5 segundos
  `;
  return (
    <View style={{ flex: 1, gap: 24 }}>
      <View style={{ paddingHorizontal: 16 }}>
        <Pressable onPress={() => handleNavigateToResultScreen('error')}>
          <TextBase size="xxl" type="Bold">
            Activá tu tarjeta física
          </TextBase>
        </Pressable>
      </View>
      <WebView
        source={{ uri: link }}
        javaScriptEnabled={true}
        onMessage={handleMessage}
        injectedJavaScript={injectedJavaScript}
        style={{ flex: 1 }}
      />
    </View>
  );
};
