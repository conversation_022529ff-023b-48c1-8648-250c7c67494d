import React from 'react';
import { View } from 'react-native';
import { TextBase } from '../../atoms';
import { getFormattedDateNotifications } from '../../../helpers/transfer';
import { getIcon } from '../../../helpers/icons';
import { styles } from './styles';
import { Notification } from '../../../types/Notifications';
import color from '../../../theme/pallets/pallet';

export const NotificationItem = ({ item }: { item: Notification }) => {
  return (
    <View key={item._id} style={styles.container}>
      <View style={styles.infoContainer}>
        {getIcon(item.notificationType)}
        <View style={styles.info}>
          <TextBase type="Bold">{item.title}</TextBase>
          <TextBase size="s">{item.description}</TextBase>
        </View>
        <View style={styles.date}>
          <TextBase size="s" color={color.NEUTRALS_800}>
            {getFormattedDateNotifications(item.createdAt)}
          </TextBase>
        </View>
      </View>
    </View>
  );
};
