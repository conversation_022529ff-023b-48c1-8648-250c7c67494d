import moment from 'moment';
import 'moment/locale/es';

export const getFormattedDate = (date: string) => {
  const formattedDate = date

  const today = moment(new Date()).utc().locale('es').format('MMMM D, YYYY');

  const yesterday = moment(new Date())
    .utc()
    .subtract(1, 'days')
    .locale('es')
    .format('MMMM D, YYYY');

  if (formattedDate === today) {
    return 'Hoy';
  } else if (formattedDate === yesterday) {
    return 'Ayer';
  } else {
    return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);
  }
};

export const getFormattedDateNotifications = (date: string) => {
  const formattedDate = moment(date).locale('es').format('MMMM D, YYYY');

  const today = moment().locale('es').format('MMMM D, YYYY');
  const yesterday = moment()
    .subtract(1, 'days')
    .locale('es')
    .format('MMMM D, YYYY');

  if (formattedDate === today) {
    return 'Hoy';
  } else if (formattedDate === yesterday) {
    return 'Ayer';
  } else {
    return moment(date).format('DD/MM');
  }
};
