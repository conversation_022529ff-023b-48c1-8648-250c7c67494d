import React, { createContext, useContext, useEffect } from 'react';

import useToggle from '../hooks/use-toggle';
import ScreenLoader from '../components/screens/screen-loader';

type Props = { children?: React.ReactNode };
type ScreenLoaderProps = (value?: boolean) => void;

const ScreenLoaderContext = createContext<ScreenLoaderProps>(() => {});

/**Sets loaderContext state, toggles the state if no params are sended */
export const useScreenLoaderContext = (value?: boolean) => {
  const setIsLoading = useContext(ScreenLoaderContext);

  useEffect(() => {
    if (value === true || value === false) {
      setIsLoading(value);
    }
  }, [value]); // eslint-disable-line react-hooks/exhaustive-deps

  return setIsLoading;
};

export function ScreenLoaderProvider({ children }: Readonly<Props>) {
  const [isLoading, setIsLoading] = useToggle();

  return (
    <ScreenLoaderContext.Provider value={setIsLoading}>
      <>
        {children}
        {isLoading && <ScreenLoader />}
      </>
    </ScreenLoaderContext.Provider>
  );
}
