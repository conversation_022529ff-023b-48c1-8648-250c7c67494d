import { api } from '../../services/apiService';
import { useState } from 'react';

export const useGetCountryCodes = () => {
  const [prefixes, setPrefixes] = useState<string[]>([]);
  const [error, setError] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [isLoaded, setIsLoaded] = useState<boolean>(false);

  const getCodes = async () => {
    try {
      setIsLoaded(true);
      const response = await api.get('/digital/utils/countries');
      if (response.status === 200) {
        const allPrefixes = response?.data?.data.map((item: any) => item);
        setPrefixes(allPrefixes);
        setSuccess(true);
        setIsLoaded(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
        setIsLoaded(false);
      }
    } catch (err: any) {
      console.error('Error:', err);
      setError(true);
      setErrorMessage(err.response.data.message);
      setIsLoaded(false);
    }
  };

  return {
    getCodes,
    successData: prefixes,
    error,
    success,
    errorMessage,
    isLoaded,
  };
};
