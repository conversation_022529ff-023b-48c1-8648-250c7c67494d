import axios from 'axios';
import Config from 'react-native-config';
import { getDecryptedData } from '../helpers/decryptCBC';

const getApiKey = async () => {
  const dataDecrypted = await getDecryptedData(Config.API_KEY ?? '');
  return dataDecrypted;
}
const baseUrl = Config.API_URL;
const headers = { 'x-api-key': Config.API_KEY, 'x-environment': Config.ENVIRONMENT?.toLowerCase() };

const axiosRequestConfig = {
  baseURL: baseUrl,
  headers,
};

const api = axios.create(axiosRequestConfig);

export const setAxiosInterceptorRequest = (logout = () => { }, checkBiometricsAndSignOut = () => { }) => {
  api.interceptors.request.use(
    async config => {
      const apiKey = await getApiKey();
      config.headers['x-api-key'] = apiKey ?? '';
      return config;
    },
    error => Promise.reject(error),
  );

  api.interceptors.response.use(
    response => response,
    error => {
      const statusCode = error.response?.status;
      const message = error.response?.data?.message;

      if (statusCode === 403 && message === 'Access Token has expired') {
        checkBiometricsAndSignOut();
      }

      if (statusCode === 403 && message === 'Invalid or missing apiKey') {
        logout();
      }

      return Promise.reject(error);
    }
  );
};

export { api };
