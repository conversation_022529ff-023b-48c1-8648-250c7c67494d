import { NativeModules, Platform } from 'react-native';
import CryptoAesCbc from 'react-native-crypto-aes-cbc';
import base64 from 'react-native-base64';

const SecretManager = Platform.select({
  ios: NativeModules.SecretManager,
  android: NativeModules.SecretManager,
});

export const getDecryptedData = async (data: string, iv?: string) => {
  const secret = await getSecret();
  let ivBase64 = '';
  let defaultIV = '0000000000000000';
  ivBase64 = base64.encode(iv ? iv : defaultIV);

  const keyBase64 = base64.encode(secret);

  const dataDecrypted = await CryptoAesCbc.decryptByBase64(
    ivBase64,
    keyBase64,
    data,
    '256',
  );
  return dataDecrypted as string;
};

export const getSecret = async () => {
  let secret = '';
  if (Platform.OS === 'android') {
    try {
      secret = await new Promise((resolve, reject) => {
        SecretManager.getSecret((error: Error, secret: string) => {
          if (error) {
            reject(new Error('Error getting secret'));
          } else {
            resolve(secret);
          }
        });
      });
    } catch (error) {
      throw new Error('Error getting secret');
    }
  } else {
    secret = await new Promise(resolve => {
      SecretManager.getSecret((secret: string) => {
        resolve(secret);
      });
    });
  }
  return secret;
};
