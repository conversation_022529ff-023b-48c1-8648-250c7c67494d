import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Keyboard,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
  TouchableWithoutFeedback,
  Text,
} from 'react-native';
import { Button } from '../../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { isEmpty } from 'lodash';
import { InputAmount } from '../../components/molecules/InputAmount';
import { getLoanTypeTranslation } from '../../types/StackLoan/LoanTypes';
import LoanTypeIcon from '../../components/atoms/Icons/LoanTypeIcon';
import { formatCurrency } from '../../helpers/formatCurrency';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const SimulateLoanScreen = ({ navigation, route }: Props) => {
  const { loanTypeId, loanType, limitAmount } = route.params;

  const [amount, onChangeValue] = useState(0);

  const inputRef = useRef<TextInput>(null);
  const [keyboardOpen, setKeyboardOpen] = useState(false);

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardOpen(true);
    });
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardOpen(false);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  const handleAmount = useCallback(
    (value: string) => {
      onChangeValue(value);
    },
    [onChangeValue],
  );

  const handleOnPress = () => {
    if (keyboardOpen) {
      inputRef.current?.blur();
    }
    inputRef.current?.focus();
  };

  const handleKeyPress = (
    event: NativeSyntheticEvent<TextInputKeyPressEventData>,
  ) => {
    const key = event.nativeEvent.key;

    let newValue = amount;
    const maxDecimals = 2;

    const sanitizedKey = key.replace(',', '.');
    const validKeyRegExp = /^[0-9.]$/;
    const decimalRegExp = /^\./;
    const validNumberRegExp = new RegExp(`^\\d+(\\.\\d{0,${maxDecimals}})?$`);

    if (key === 'Backspace' && newValue.length > 0) {
      newValue = newValue.slice(0, -1);
      handleAmount(newValue);
      return;
    }
    if (isEmpty(newValue) && decimalRegExp.test(newValue + sanitizedKey)) {
      newValue = '0' + sanitizedKey;
      handleAmount(newValue);
      return;
    }
    if (isEmpty(newValue) && sanitizedKey === '0') {
      newValue = sanitizedKey + '.';
      handleAmount(newValue);
      return;
    }

    if (!validKeyRegExp.test(sanitizedKey)) {
      return;
    }
    if (!validNumberRegExp.test(newValue + sanitizedKey)) {
      return;
    }

    newValue += sanitizedKey;

    handleAmount(newValue);
  };

  const keyboardDismiss = () => {
    Keyboard.dismiss();
  };

  const navigateToSelectQuota = () => {
    navigation.navigate('StackLoan', {
      screen: 'SelectPaymentScreen',
      params: { loanTypeId, amount, loanType },
    });
  };

  const outOfRange = amount < 150 || amount > limitAmount;

  return (
    <TouchableWithoutFeedback accessible={false} onPress={keyboardDismiss}>
      <View style={styles.container}>
        <Text style={styles.title}>Simular préstamo</Text>
        <View style={styles.header}>
          <View
            style={{
              flexDirection: 'row',
              gap: 8,
              alignItems: 'center',
              marginVertical: 30,
            }}
          >
            <LoanTypeIcon loanType={loanType} />
            <Text
              style={{
                fontSize: 16,
                fontWeight: '700',
                color: color.TEXT_PRIMARY,
              }}
            >
              {getLoanTypeTranslation(loanType)}
            </Text>
          </View>
          <Text style={styles.txt}>Ingresar importe</Text>
          <InputAmount
            amount={amount}
            handleOnPress={handleOnPress}
            inputRef={inputRef}
            handleKeyPress={handleKeyPress}
            error={outOfRange}
          />
          <Text
            style={[
              styles.txt,
              { color: outOfRange && amount !== 0 ? color.RED_700 : '#78838D' },
            ]}
          >
            Entre $150 y ${formatCurrency(limitAmount)}
          </Text>
        </View>

        <View style={styles.button}>
          <Button
            text="Simular préstamo"
            onPress={navigateToSelectQuota}
            disabled={outOfRange}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    color: color.BLACK,
    fontWeight: 'bold',
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  button: {
    marginVertical: 32,
    width: '100%',
  },
  txt: {
    fontSize: 15,
    fontFamily: 'Satoshi-Regular',
    color: '#78838D',
    fontWeight: '400',
    marginVertical: 6,
  },
});
