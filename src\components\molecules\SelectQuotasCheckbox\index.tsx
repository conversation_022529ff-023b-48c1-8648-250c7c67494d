import React, { useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import RadioButton from '../../atoms/RadioButton/RadioButton';
import { useState } from 'react';
import { Payment } from '../../../types/StackLoan/SimulateLoanResponse';
import { formatCurrency } from '../../../helpers/formatCurrency';
import color from '../../../theme/pallets/pallet';

type Props = {
  payments: Payment[] | undefined;
  setPaymentSelected: (payment: Payment) => void;
};

export const SelectPayments = ({ payments, setPaymentSelected }: Props) => {
  const [paymentTermSelected, setPaymentTermSelected] = useState<number>();

  useEffect(() => {
    if (payments && payments.length > 0 && payments[0].term) {
      setPaymentTermSelected(payments[0].term);
      setPaymentSelected(payments[0]);
    } else {
      setPaymentTermSelected(undefined);
    }
  }, [payments]);

  const getPaymentsMaxTerms = () => {
    const lastPayment = payments?.at(payments.length - 1);

    return lastPayment?.term;
  };

  const handleSelection = (payment: Payment) => {
    setPaymentTermSelected(payment.term);
    setPaymentSelected(payment);
  };

  return (
    <>
      <Text style={{ color: color.TEXT_PRIMARY, marginBottom: 30 }}>
        Elige un plazo de hasta {getPaymentsMaxTerms()} cuotas:
      </Text>
      {payments?.map((payment, index) => (
        <View key={index}>
          <View style={styles.optionsContainer}>
            <RadioButton
              value={payment.term}
              label={`${payment.term} cuota${
                payment.term > 1 ? 's' : ''
              } de $${formatCurrency(payment.monthlyPayment)}`}
              selected={paymentTermSelected === payment.term}
              onPress={() => handleSelection(payment)}
            />
            <Text
              style={{
                color: color.NEUTRALS_800,
                fontSize: 17,
                fontWeight: '700',
              }}
            >
              ${formatCurrency(payment.totalPayment)}
            </Text>
          </View>
          <View style={styles.hr} />
        </View>
      ))}
    </>
  );
};

const styles = StyleSheet.create({
  optionsContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  hr: {
    borderBottomColor: 'grey',
    borderBottomWidth: 0.5,
    marginVertical: 10,
  },
});
