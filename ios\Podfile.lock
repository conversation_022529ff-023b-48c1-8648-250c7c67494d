PODS:
  - BEM<PERSON>heckBox (1.4.1)
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - CryptoSwift (1.6.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.8)
  - FBReactNativeSpec (0.72.8):
    - RCT-<PERSON>olly (= 2021.07.22.00)
    - RCTRequired (= 0.72.8)
    - RCTTypeSafety (= 0.72.8)
    - React-Core (= 0.72.8)
    - React-jsi (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
  - Firebase (10.20.0):
    - Firebase/Core (= 10.20.0)
  - Firebase/Core (10.20.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.20.0)
  - Firebase/CoreOnly (10.20.0):
    - FirebaseCore (= 10.20.0)
  - Firebase/Messaging (10.20.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.20.0)
  - FirebaseAnalytics (10.20.0):
    - FirebaseAnalytics/AdIdSupport (= 10.20.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.20.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.20.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flipper (0.182.0):
    - Flipper-Folly (~> 2.6)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - FlipperKit (0.182.0):
    - FlipperKit/Core (= 0.182.0)
  - FlipperKit/Core (0.182.0):
    - Flipper (~> 0.182.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.182.0):
    - Flipper (~> 0.182.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.182.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.182.0)
  - FlipperKit/FKPortForwarding (0.182.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.182.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.182.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.182.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.182.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - GoogleAppMeasurement (10.20.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.20.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.20.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.72.8):
    - hermes-engine/Pre-built (= 0.72.8)
  - hermes-engine/Pre-built (0.72.8)
  - jail-monkey (2.8.0):
    - React-Core
  - libevent (2.1.12)
  - MMKV (2.0.0):
    - MMKVCore (~> 2.0.0)
  - MMKVCore (2.0.0)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - OpenSSL-Universal (1.1.1100)
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.8)
  - RCTTypeSafety (0.72.8):
    - FBLazyVector (= 0.72.8)
    - RCTRequired (= 0.72.8)
    - React-Core (= 0.72.8)
  - React (0.72.8):
    - React-Core (= 0.72.8)
    - React-Core/DevSupport (= 0.72.8)
    - React-Core/RCTWebSocket (= 0.72.8)
    - React-RCTActionSheet (= 0.72.8)
    - React-RCTAnimation (= 0.72.8)
    - React-RCTBlob (= 0.72.8)
    - React-RCTImage (= 0.72.8)
    - React-RCTLinking (= 0.72.8)
    - React-RCTNetwork (= 0.72.8)
    - React-RCTSettings (= 0.72.8)
    - React-RCTText (= 0.72.8)
    - React-RCTVibration (= 0.72.8)
  - React-callinvoker (0.72.8)
  - React-Codegen (0.72.8):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.8)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.8)
    - React-Core/RCTWebSocket (= 0.72.8)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.8)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.8)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.8)
    - React-Codegen (= 0.72.8)
    - React-Core/CoreModulesHeaders (= 0.72.8)
    - React-jsi (= 0.72.8)
    - React-RCTBlob
    - React-RCTImage (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.8):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.8)
    - React-debug (= 0.72.8)
    - React-jsi (= 0.72.8)
    - React-jsinspector (= 0.72.8)
    - React-logger (= 0.72.8)
    - React-perflogger (= 0.72.8)
    - React-runtimeexecutor (= 0.72.8)
  - React-debug (0.72.8)
  - React-hermes (0.72.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.8)
    - React-jsi
    - React-jsiexecutor (= 0.72.8)
    - React-jsinspector (= 0.72.8)
    - React-perflogger (= 0.72.8)
  - React-jsi (0.72.8):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.8)
    - React-jsi (= 0.72.8)
    - React-perflogger (= 0.72.8)
  - React-jsinspector (0.72.8)
  - React-logger (0.72.8):
    - glog
  - react-native-biometrics (3.0.1):
    - React-Core
  - react-native-config (1.5.1):
    - react-native-config/App (= 1.5.1)
  - react-native-config/App (1.5.1):
    - React-Core
  - react-native-crypto-aes-cbc (1.1.3):
    - CryptoSwift (~> 1.6.0)
    - React-Core
  - react-native-encrypted-storage (4.0.3):
    - React-Core
  - react-native-html-to-pdf (0.12.0):
    - React-Core
  - react-native-mmkv (2.12.2):
    - MMKV (>= 1.3.3)
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-orientation-locker (1.7.0):
    - React-Core
  - react-native-pager-view (6.3.3):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-safe-area-context (4.9.0):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-webview (13.8.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-NativeModulesApple (0.72.8):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.8)
  - React-RCTActionSheet (0.72.8):
    - React-Core/RCTActionSheetHeaders (= 0.72.8)
  - React-RCTAnimation (0.72.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.8)
    - React-Codegen (= 0.72.8)
    - React-Core/RCTAnimationHeaders (= 0.72.8)
    - React-jsi (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
  - React-RCTAppDelegate (0.72.8):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.8):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.8)
    - React-Core/RCTBlobHeaders (= 0.72.8)
    - React-Core/RCTWebSocket (= 0.72.8)
    - React-jsi (= 0.72.8)
    - React-RCTNetwork (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
  - React-RCTImage (0.72.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.8)
    - React-Codegen (= 0.72.8)
    - React-Core/RCTImageHeaders (= 0.72.8)
    - React-jsi (= 0.72.8)
    - React-RCTNetwork (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
  - React-RCTLinking (0.72.8):
    - React-Codegen (= 0.72.8)
    - React-Core/RCTLinkingHeaders (= 0.72.8)
    - React-jsi (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
  - React-RCTNetwork (0.72.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.8)
    - React-Codegen (= 0.72.8)
    - React-Core/RCTNetworkHeaders (= 0.72.8)
    - React-jsi (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
  - React-RCTSettings (0.72.8):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.8)
    - React-Codegen (= 0.72.8)
    - React-Core/RCTSettingsHeaders (= 0.72.8)
    - React-jsi (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
  - React-RCTText (0.72.8):
    - React-Core/RCTTextHeaders (= 0.72.8)
  - React-RCTVibration (0.72.8):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.8)
    - React-Core/RCTVibrationHeaders (= 0.72.8)
    - React-jsi (= 0.72.8)
    - ReactCommon/turbomodule/core (= 0.72.8)
  - React-rncore (0.72.8)
  - React-runtimeexecutor (0.72.8):
    - React-jsi (= 0.72.8)
  - React-runtimescheduler (0.72.8):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.8):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.8)
    - React-cxxreact (= 0.72.8)
    - React-jsi (= 0.72.8)
    - React-logger (= 0.72.8)
    - React-perflogger (= 0.72.8)
  - ReactCommon/turbomodule/core (0.72.8):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.8)
    - React-cxxreact (= 0.72.8)
    - React-jsi (= 0.72.8)
    - React-logger (= 0.72.8)
    - React-perflogger (= 0.72.8)
  - RNCAsyncStorage (1.22.3):
    - React-Core
  - RNCCheckbox (0.5.17):
    - BEMCheckBox (~> 1.4)
    - React-Core
  - RNCClipboard (1.13.2):
    - React-Core
  - RNCMaskedView (0.3.1):
    - React-Core
  - RNCPicker (2.6.1):
    - React-Core
  - RNDeviceInfo (10.13.1):
    - React-Core
  - RNFBApp (18.9.0):
    - Firebase/CoreOnly (= 10.20.0)
    - React-Core
  - RNFBMessaging (18.9.0):
    - Firebase/Messaging (= 10.20.0)
    - FirebaseCoreExtension (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNFlashList (1.6.3):
    - React-Core
  - RNGestureHandler (2.15.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNScreens (3.29.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNShare (10.1.0):
    - React-Core
  - RNSVG (14.1.0):
    - React-Core
  - SocketRocket (0.6.1)
  - TrustKit (3.0.4)
  - VisionCamera (3.9.0):
    - React
    - React-callinvoker
    - React-Core
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - FirebaseCore
  - Flipper (= 0.182.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - FlipperKit (= 0.182.0)
  - FlipperKit/Core (= 0.182.0)
  - FlipperKit/CppBridge (= 0.182.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.182.0)
  - FlipperKit/FBDefines (= 0.182.0)
  - FlipperKit/FKPortForwarding (= 0.182.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.182.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.182.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.182.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.182.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.182.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.182.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.182.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - jail-monkey (from `../node_modules/jail-monkey`)
  - libevent (~> 2.1.12)
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-biometrics (from `../node_modules/react-native-biometrics`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-crypto-aes-cbc (from `../node_modules/react-native-crypto-aes-cbc`)
  - react-native-encrypted-storage (from `../node_modules/react-native-encrypted-storage`)
  - react-native-html-to-pdf (from `../node_modules/react-native-html-to-pdf`)
  - react-native-mmkv (from `../node_modules/react-native-mmkv`)
  - react-native-orientation-locker (from `../node_modules/react-native-orientation-locker`)
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCCheckbox (from `../node_modules/@react-native-community/checkbox`)"
  - "RNCClipboard (from `../node_modules/@react-native-clipboard/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPicker (from `../node_modules/@react-native-picker/picker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - "RNFlashList (from `../node_modules/@shopify/flash-list`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNShare (from `../node_modules/react-native-share`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - TrustKit
  - VisionCamera (from `../node_modules/react-native-vision-camera`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - BEMCheckBox
    - CocoaAsyncSocket
    - CryptoSwift
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - FlipperKit
    - fmt
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - libevent
    - MMKV
    - MMKVCore
    - nanopb
    - OpenSSL-Universal
    - PromisesObjC
    - SocketRocket
    - TrustKit
    - YogaKit

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2023-08-07-RNv0.72.4-813b2def12bc9df02654b3e3653ae4a68d0572e0
  jail-monkey:
    :path: "../node_modules/jail-monkey"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-biometrics:
    :path: "../node_modules/react-native-biometrics"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-crypto-aes-cbc:
    :path: "../node_modules/react-native-crypto-aes-cbc"
  react-native-encrypted-storage:
    :path: "../node_modules/react-native-encrypted-storage"
  react-native-html-to-pdf:
    :path: "../node_modules/react-native-html-to-pdf"
  react-native-mmkv:
    :path: "../node_modules/react-native-mmkv"
  react-native-orientation-locker:
    :path: "../node_modules/react-native-orientation-locker"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCCheckbox:
    :path: "../node_modules/@react-native-community/checkbox"
  RNCClipboard:
    :path: "../node_modules/@react-native-clipboard/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNCPicker:
    :path: "../node_modules/@react-native-picker/picker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNFlashList:
    :path: "../node_modules/@shopify/flash-list"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNShare:
    :path: "../node_modules/react-native-share"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  VisionCamera:
    :path: "../node_modules/react-native-vision-camera"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  BEMCheckBox: 5ba6e37ade3d3657b36caecc35c8b75c6c2b1a4e
  boost: 57d2868c099736d80fcd648bf211b4431e51a558
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  CryptoSwift: 562f8eceb40e80796fffc668b0cad9313284cfa6
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: df077ae30c8148bcfdc8f886450eb2eefb94a5be
  FBReactNativeSpec: ca2f1cd161c72659f294fadd27b1517cab6728fb
  Firebase: 10c8cb12fb7ad2ae0c09ffc86cd9c1ab392a0031
  FirebaseAnalytics: a2731bf3670747ce8f65368b118d18aa8e368246
  FirebaseCore: 28045c1560a2600d284b9c45a904fe322dc890b6
  FirebaseCoreExtension: 0659f035b88c5a7a15a9763c48c2e6ca8c0a2977
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 06c414a21b122396a26847c523d5c370f8325df5
  Flipper: 6edb735e6c3e332975d1b17956bcc584eccf5818
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  FlipperKit: 2efad7007d6745a3f95e4034d547be637f89d3f6
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  GoogleAppMeasurement: bb3c564c3efb933136af0e94899e0a46167466a8
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: 32c50dea8bf400d1414d26cb22a5e8a1ceb66a5e
  jail-monkey: a71b35d482a70ecba844a90f002994012cf12a5d
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  MMKV: f7d1d5945c8765f97f39c3d121f353d46735d801
  MMKVCore: c04b296010fcb1d1638f2c69405096aac12f6390
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 182d3e71f3ee4edfa87d4bf82eeea61510922b2a
  RCTTypeSafety: 61b90f18bbc961c01ae4cfcb80830953eca73573
  React: 1beea0f4f564f96b94159811340b6c09dc61530b
  React-callinvoker: 2e055aa95f8169c20fade24f82c49a190ab7de45
  React-Codegen: b9803882fc2541fb48167c9cf34c9c7354b12b17
  React-Core: 1d8665e11f7ea4f0cd82aea8eab6a5c53e597cbd
  React-CoreModules: 6e57bbb2ac83e5dd4c960ce3218f4fb3ac923bb9
  React-cxxreact: d0ef80b5be65c1aad01e9413539315b5ddd26b15
  React-debug: 0ee4c115f009d421088b781241080fa9b9e9c9de
  React-hermes: ab440bb0114ffb24fcb83f1f7d4a0b183e33bd27
  React-jsi: 0921d5fe8094d75d472b146137bb5f46102d7974
  React-jsiexecutor: 03baea362e7475543e7cd091cb4fcf2bd78e041e
  React-jsinspector: fdf0a09bddecf9ce8da830bcb89a9814230c04a4
  React-logger: c2e7bb772d6e9fc3d0109d1243b81546a9c93c0f
  react-native-biometrics: 352e5a794bfffc46a0c86725ea7dc62deb085bdc
  react-native-config: 86038147314e2e6d10ea9972022aa171e6b1d4d8
  react-native-crypto-aes-cbc: 9836f55117ef19172e2d9a88ae63515504f9d946
  react-native-encrypted-storage: db300a3f2f0aba1e818417c1c0a6be549038deb7
  react-native-html-to-pdf: 4c5c6e26819fe202971061594058877aa9b25265
  react-native-mmkv: 38d484ccd8f5760fe0e46d206ed07add5ddda1fc
  react-native-orientation-locker: 5819fd23ca89cbac0d736fb4314745f62716d517
  react-native-pager-view: 2447850409b3311900b6e3273f679ee4c465e576
  react-native-safe-area-context: b97eb6f9e3b7f437806c2ce5983f479f8eb5de4b
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  react-native-webview: bdc091de8cf7f8397653e30182efcd9f772e03b3
  React-NativeModulesApple: f6153767facad322a10b07fa2dbd747abbe10302
  React-perflogger: 2a7d221549cd5b69e95c5afa2e8d336f0465e1ec
  React-RCTActionSheet: 449042d31545790748a97d9b35d4acb683e2ad00
  React-RCTAnimation: 1f4c8ce38087dbbb7f11159d8ba4eacd4fa6f06a
  React-RCTAppDelegate: 6e095f409ff43b9065249d569e3f6bb5807be08b
  React-RCTBlob: b63c4ddf33347557a4adaba553b02a23f4b49e44
  React-RCTImage: 62a897828f129e2220d27b51ce9d965efed547ab
  React-RCTLinking: 465ad65daf762d5b336973c988bdea0e51697467
  React-RCTNetwork: 33b47ffe3b4192097e787a23cbbb1b5f495337ff
  React-RCTSettings: 37d04a01ef8806ceeb281d7b90c85d8600a0642f
  React-RCTText: 9a811835f8888d34aa9377333ca5d33ea46b3006
  React-RCTVibration: 9f4d7f480b79141e1bb16ad76d46068e5ac4d378
  React-rncore: 3b6f48ed29e8abc04495386797164c30e870e561
  React-runtimeexecutor: 4e97887d9cf661def3ffdb0f48c41d4e2afb31d0
  React-runtimescheduler: 607a5832f8025dfe0d1178e78b447dd6d11bc012
  React-utils: 96f4588532614099a6d67a2b7c5912e1a35d6403
  ReactCommon: c111aa0c8006650d77a651e155f4112f97761dfd
  RNCAsyncStorage: 10591b9e0a91eaffee14e69b3721009759235125
  RNCCheckbox: a3ca9978cb0846b981d28da4e9914bd437403d77
  RNCClipboard: 60fed4b71560d7bfe40e9d35dea9762b024da86d
  RNCMaskedView: 090213d32d8b3bb83a4dcb7d12c18f0152591906
  RNCPicker: b18aaf30df596e9b1738e7c1f9ee55402a229dca
  RNDeviceInfo: 4f9c7cfd6b9db1b05eb919620a001cf35b536423
  RNFBApp: 9b25191f7a5e72c185b7e43fffb0d906869c4659
  RNFBMessaging: 99621af036e0aa85506390f77d79c9e6353aacca
  RNFlashList: 4b4b6b093afc0df60ae08f9cbf6ccd4c836c667a
  RNGestureHandler: 7909c50383a18f0cb10ce1db7262b9a6da504c03
  RNScreens: 3c5b9f4a9dcde752466854b6109b79c0e205dad3
  RNShare: b674d9f1cb0dc11116983bebd8712908a226a3ee
  RNSVG: ba3e7232f45e34b7b47e74472386cf4e1a676d0a
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  TrustKit: 403e8d2cfbff2abfc37cd41acc558dab8ec78e59
  VisionCamera: 7a5c87805b13adaaa557132b44879ed1e1e39786
  Yoga: 11d7931afb89721f39cf553a78767b81c7fbea1f
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: d18a0d465d999c3a668a5a1bea502a495529fdc5

COCOAPODS: 1.14.0
