import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import StickLoader from '../../components/atoms/Loaders/StickLoader';
import TextBase from '../../components/atoms/TextBase';
import { createUserLoan } from '../../services/loanServices';
import { UserLoan } from '../../types/StackLoan/UserLoansResponse';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const RequestingNewLoanScreen = ({ navigation, route }: Props) => {
  const {
    loanTypeId,
    loanType,
    terms,
    amount,
    totalPayment,
    monthlyPayment,
    firstPayment,
    tna,
    tea,
    ctf,
  } = route.params;
  const [loan, setLoan] = useState<UserLoan | undefined>();
  const [error, setError] = useState<boolean>(false);
  const [requesting, setRequesting] = useState<boolean>(true);

  const navigateToNewLoanSuccessScreen = (loan: UserLoan) => {
    navigation.navigate('StackLoan', {
      screen: 'NewLoanSuccessScreen',
      params: { loanId: loan.loanId, requestAmount: Number(amount) },
    });
  };

  const createUserLoanInApi = async () => {
    try {
      const loanResponse = await createUserLoan({
        loanTypeId: loanTypeId,
        loanType: loanType,
        totalInstallments: terms,
        requestAmount: Number(amount),
        cuoteAmount: monthlyPayment,
        totalPayment: totalPayment,
        firstPayment: firstPayment,
        tna: tna,
        tea: tea,
        ctf: ctf,
      });

      setLoan(loanResponse);
    } catch (err: any) {
      setError(true);
    } finally {
      setRequesting(false);
    }
  };

  useEffect(() => {
    createUserLoanInApi();
  }, []);

  useEffect(() => {
    if (!error && !requesting && loan) {
      navigateToNewLoanSuccessScreen(loan);
    }
  }, [error, requesting, loan]);

  return (
    <View style={styles.container}>
      <StickLoader color={color.PRIMARY_700} />
      <TextBase size="l">Aguarda, estamos solicitando tu préstamo...</TextBase>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
  },
});
