import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../../types';

interface Props extends IconProps {
  bgColor: string;
}

const StoreIcon: FC<Props> = ({ size, color, bgColor }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 28 28" fill="none">
      <Rect width={size} height={size} rx="4" fill={bgColor} />
      <Path d="M22.2483 11.4083L21.3733 7.76667C21.19 7.01667 20.54 6.5 19.7816 6.5H8.20663C7.45663 6.5 6.7983 7.025 6.6233 7.76667L5.7483 11.4083C5.5483 12.2583 5.73163 13.125 6.26496 13.8083C6.33163 13.9 6.4233 13.9667 6.4983 14.05V19.8333C6.4983 20.75 7.2483 21.5 8.16496 21.5H19.8316C20.7483 21.5 21.4983 20.75 21.4983 19.8333V14.05C21.5733 13.975 21.665 13.9 21.7316 13.8167C22.265 13.1333 22.4566 12.2583 22.2483 11.4083ZM19.7566 8.15833L20.6316 11.8C20.715 12.15 20.64 12.5 20.4233 12.775C20.3066 12.925 20.0566 13.1667 19.64 13.1667C19.1316 13.1667 18.69 12.7583 18.6316 12.2167L18.1483 8.16667L19.7566 8.15833ZM14.8316 8.16667H16.465L16.915 11.9333C16.9566 12.2583 16.8566 12.5833 16.64 12.825C16.4566 13.0417 16.19 13.1667 15.8483 13.1667C15.29 13.1667 14.8316 12.675 14.8316 12.075V8.16667ZM11.0733 11.9333L11.5316 8.16667H13.165V12.075C13.165 12.675 12.7066 13.1667 12.09 13.1667C11.8066 13.1667 11.5483 13.0417 11.3483 12.825C11.14 12.5833 11.04 12.2583 11.0733 11.9333ZM7.36496 11.8L8.20663 8.16667H9.8483L9.36496 12.2167C9.2983 12.7583 8.86496 13.1667 8.35663 13.1667C7.9483 13.1667 7.68996 12.925 7.58163 12.775C7.35663 12.5083 7.28163 12.15 7.36496 11.8ZM8.16496 19.8333V14.8083C8.23163 14.8167 8.28996 14.8333 8.35663 14.8333C9.08163 14.8333 9.73996 14.5333 10.2233 14.0417C10.7233 14.5417 11.39 14.8333 12.1483 14.8333C12.8733 14.8333 13.5233 14.5333 14.0066 14.0583C14.4983 14.5333 15.165 14.8333 15.915 14.8333C16.615 14.8333 17.2816 14.5417 17.7816 14.0417C18.265 14.5333 18.9233 14.8333 19.6483 14.8333C19.715 14.8333 19.7733 14.8167 19.84 14.8083V19.8333H8.16496Z" fill={color} />
    </Svg>
  );
};

export default StoreIcon;