import { AuthContext } from '../../context/AuthContext';
import { api } from '../../services/apiService';
import { useContext, useState } from 'react';

export const useDeleteAccountContact = () => {
  const [successData, setSuccessData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [successDeleteAccount, setSuccess] = useState<boolean>(false);
  const [errorDeleteAccount, setError] = useState<boolean>(false);

  const { userData } = useContext(AuthContext);

  const deleteAccountContact = async ({ contactId, accountId }: any) => {
    try {
      setLoading(true);
      const response = await api.delete(
        `/contacts/account/${userData}/${contactId}/${accountId}`,
      );
      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
        setLoading(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
      }
      setLoading(false);
    } catch (err: any) {
      console.error('Error:', JSON.stringify(err.response, null, 4));
      setError(true);
      setErrorMessage(err.response.data.message);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return {
    deleteAccountContact,
    successData,
    errorMessage,
    loading,
    successDeleteAccount,
    errorDeleteAccount,
  };
};
