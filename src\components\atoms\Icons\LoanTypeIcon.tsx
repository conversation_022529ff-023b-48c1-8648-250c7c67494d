import React, { FC } from 'react';
import CashRounded from './CashRounded';
import PhoneRounded from './PhoneRounded';
import HouseRounded from './HouseRounded';
import CarRounded from './CarRounded';
import { LoanType } from '../../../types/StackLoan/LoanTypes';
import BackgroundWrapper from './BackgroundWrapper';
import color from '../../../theme/pallets/pallet';

const DEFAULT_SIZE = 32;
const DEFAULT_ICON_COLOR = color.PRIMARY_500;
const DEFAULT_BACKBGROUND_COLOR = color.PRIMARY_50;
const DEFAULT_BACKBGROUND_SHAPE = 'circle';

interface LoanTypeIconProps {
  loanType: LoanType;
  size?: number;
  iconColor?: string;
  backgroundColor?: string;
  shape?: 'circle' | 'square' | 'roundedSquare';
}

const LoanTypeIcon: FC<LoanTypeIconProps> = ({
  loanType,
  size = DEFAULT_SIZE,
  iconColor = DEFAULT_ICON_COLOR,
  backgroundColor = DEFAULT_BACKBGROUND_COLOR,
  shape = DEFAULT_BACKBGROUND_SHAPE,
}) => {
  const Icon = () => {
    switch (loanType) {
      case 'pledge':
        return <CarRounded size={size} iconColor={iconColor} />;
      case 'personal':
        return <CashRounded size={size} iconColor={iconColor} />;
      case 'express':
        return <PhoneRounded size={size} iconColor={iconColor} />;
      case 'mortgage':
        return <HouseRounded size={size} iconColor={iconColor} />;
      default:
        return null;
    }
  };

  return (
    <BackgroundWrapper
      backgroundColor={backgroundColor}
      shape={shape}
      size={size}
    >
      <Icon />
    </BackgroundWrapper>
  );
};

export default LoanTypeIcon;
