import React, { useContext, useEffect, useState } from 'react';
import { Pressable, View } from 'react-native';
import { TextBase } from '../../components/atoms';
import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import { styles } from './styles';
import moment from 'moment';
import CopyIcon from '../../components/atoms/Icons/CopyIcon';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import Share from 'react-native-share';
import createPDF from '../../hooks/Transfers/pdft';
import { formatCurrency } from '../../helpers/formatCurrency';
import { getTransactionById } from '../../services/transactions';
import { Transactions } from '../../types/Transfer';
import { Skeleton } from '../../components/atoms/Skeleton';
import { AuthContext } from '../../context/AuthContext';
import { useCopyCustomToast } from '../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';
import color from '../../theme/pallets/pallet';

interface Props {
  route: any;
  operationId: number;
  formattedDate?: any;
  amount: any;
  motive: string;
  from: string;
  fromCuit: number;
  fromCVU: number;
  toName: string;
  toCuit: number;
  toCvu: number;
  idCoelsa?: any;
}

const exportPDF = async (
  operationId: number,
  formattedDate: any,
  amount: any,
  motive: string,
  from: string,
  fromCuit: number,
  fromCVU: number,
  toName: string,
  toCuit: number,
  toCvu: number,
  idCoelsa?: any,
  description?: any,
) => {
  try {
    const html = createPDF(
      operationId,
      formattedDate,
      amount,
      motive,
      from,
      fromCuit,
      fromCVU,
      toName,
      toCuit,
      toCvu,
      idCoelsa,
      description,
    );
    const options = {
      html,
      fileName: 'Comprobante_Toshify',
      directory: 'Documents',
    };
    const file = await RNHTMLtoPDF.convert(options);
    return file.filePath;
  } catch (error) {
    console.error('Error en la creación del PDF:', error);
    return null;
  }
};
const shareOrder = async (filePath: string) => {
  try {
    await Share.open({
      title: 'Orden',
      url: `file://${filePath}`,
    });
  } catch (error) {
    console.error('Error al compartir el PDF:', error);
  }
};

export const ReceiptScreen = (props: Props) => {
  useDisableBackButton();
  const { disableScreenLock } = useContext(AuthContext);
  const {
    operationId,
    amount,
    motive,
    from,
    fromCuit,
    fromCVU,
    toName,
    toCuit,
    toCvu,
  } = props.route.params;
  const [transaction, setTransaction] = useState<Transactions>();
  const [loading, setLoading] = useState(false);

  const getTransaction = async () => {
    try {
      setLoading(true);
      const transaction = await getTransactionById(operationId);
      setTransaction(transaction);
      setLoading(false);
    } catch (error) {
      console.error('Error', error);
      setLoading(false);
    }
  };
  useEffect(() => {
    getTransaction();
  }, []);

  const description = transaction?.description;

  const formattedDate = moment(transaction?.smImpactedOn).format(
    'DD/MM/YYYY [-] HH:mm [hs]',
  );
  const { showToast, toastConfig } = useCopyCustomToast();

  const handleShareOrder = async () => {
    disableScreenLock();
    try {
      const filePath = await exportPDF(
        operationId,
        formattedDate,
        formatCurrency(Math.abs(amount)),
        motive === '' ? 'Varios' : motive,
        from,
        fromCuit,
        fromCVU,
        toName,
        toCuit,
        toCvu,
        transaction?.sgInfo.idCoelsa,
        description,
      );
      if (filePath) {
        await shareOrder(filePath);
      }
    } catch (error) {
      console.error('Error al exportar y compartir el PDF:', error);
    }
  };
  return (
    <KeyboardAvoidingComponent>
      <View style={styles.container}>
        <TextBase type="SemiBold" size="xl">
          Comprobante de operación
        </TextBase>
        <View style={styles.date}>
          {loading ? (
            <Skeleton
              backgroundColor="#F7F8FE"
              highlightColor="#e8e9ed"
              width={160}
              height={30}
            />
          ) : (
            <>
              <TextBase size="m">{description}</TextBase>
              <TextBase>{formattedDate}</TextBase>
            </>
          )}
        </View>
        <View>
          {loading ? (
            <Skeleton
              width={120}
              backgroundColor="#F7F8FE"
              highlightColor="#e8e9ed"
              height={30}
            />
          ) : (
            <TextBase size="xxxl">${formatCurrency(Math.abs(amount))}</TextBase>
          )}
          {loading ? (
            <Skeleton
              width={120}
              backgroundColor="#F7F8FE"
              highlightColor="#e8e9ed"
              height={30}
            />
          ) : (
            <TextBase>
              Motivo: <TextBase>{motive === '' ? 'Varios' : motive}</TextBase>
            </TextBase>
          )}
        </View>
        {loading ? (
          <View style={styles.detailSkeleton}>
            <Skeleton
              width={450}
              backgroundColor="#F7F8FE"
              highlightColor="#e8e9ed"
              height={120}
            />
          </View>
        ) : (
          <View style={styles.detail}>
            <TextBase type="Light">De</TextBase>
            <TextBase type="Bold">{from}</TextBase>
            <TextBase type="Light">
              CUIT/CUIL: <TextBase>{fromCuit}</TextBase>
            </TextBase>
            <View style={styles.detailBot}>
              <TextBase>Billetera Toshify</TextBase>
              <TextBase type="Light">
                CVU <TextBase>{fromCVU}</TextBase>
              </TextBase>
            </View>
          </View>
        )}

        {loading ? (
          <View style={styles.detailSkeletonBot}>
            <Skeleton
              width={450}
              backgroundColor="#F7F8FE"
              highlightColor="#e8e9ed"
              height={120}
            />
          </View>
        ) : (
          <View style={styles.detail}>
            <TextBase type="Light">Para</TextBase>
            <TextBase type="Bold">{toName}</TextBase>
            <TextBase type="Light">
              CUIT/CUIL: <TextBase>{toCuit}</TextBase>
            </TextBase>
            <TextBase type="Light">
              CVU <TextBase>{toCvu}</TextBase>
            </TextBase>
          </View>
        )}

        <View style={styles.idCoelsa}>
          {loading ? (
            <View style={styles.idSkeleton}>
              <Skeleton
                backgroundColor="#F7F8FE"
                highlightColor="#e8e9ed"
                width={200}
                height={35}
              />
            </View>
          ) : (
            <View style={styles.nroComprobante}>
              <TextBase type="Light">Código de identificación</TextBase>
              <View style={styles.copyText}>
                <TextBase type="SemiBold">{transaction?.operationId}</TextBase>
                <Pressable onPress={() => showToast(transaction?.operationId!)}>
                  <CopyIcon size={24} color={color.PRIMARY_500} />
                </Pressable>
              </View>
            </View>
          )}

          {loading ? (
            <Skeleton
              backgroundColor="#F7F8FE"
              highlightColor="#e8e9ed"
              width={200}
              height={35}
            />
          ) : (
            <>
              <>
                <TextBase type="Light">ID Coelsa</TextBase>
                <View style={styles.copyText}>
                  {transaction?.sgInfo.idCoelsa !== 'n/a' &&
                  transaction?.sgInfo.idCoelsa !== '' ? (
                    <>
                      <TextBase type="SemiBold">
                        {transaction?.sgInfo.idCoelsa}
                      </TextBase>
                      <Pressable
                        onPress={() => showToast(transaction?.sgInfo.idCoelsa!)}
                      >
                        <CopyIcon size={24} color={color.PRIMARY_500} />
                      </Pressable>
                    </>
                  ) : (
                    <TextBase type="SemiBold">No aplica</TextBase>
                  )}
                </View>
              </>
            </>
          )}
        </View>
      </View>
      <View style={styles.button}>
        <ButtonOutline
          onPress={handleShareOrder}
          text="Compartir comprobante"
          disabled={loading}
        />
      </View>
      <Toast config={toastConfig} />
    </KeyboardAvoidingComponent>
  );
};
