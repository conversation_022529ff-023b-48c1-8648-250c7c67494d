import React, { FC, useCallback, useState } from 'react';
import {
  ActivityIndicator,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import TextBase from '../components/atoms/TextBase';
import { colors } from '../constants/colors';
import PlusIcon from '../components/atoms/Icons/PlusIcon';
import StoreIcon from '../components/atoms/Icons/shared/cards/StoreIcon';
import BookIcon from '../components/atoms/Icons/shared/cards/BookIcon';
import PrepaidCardFloating from '../components/atoms/Icons/Cards/PrepaidCardFloating';
import { Button } from '../components/atoms';
import { useUserContext } from '../context/UserContext';
import CreditCard from '../components/molecules/GolletCreditCard';
import { getUserInfo } from '../services/users';
import { useFocusEffect } from '@react-navigation/native';
import color from '../theme/pallets/pallet';

const CardsScreen: FC<any> = ({ navigation }) => {
  const { user, setUser } = useUserContext();
  const [loading, setLoading] = useState(false);
  const cards = user.cards?.card_list;
  const hasCardsShipping =
    user.cards?.shipping !== undefined && user.cards?.shipping.length > 0;
  const hasCards = cards !== undefined && cards.length > 0;

  const fetchData = useCallback(async () => {
    setLoading(true);
    const userInfo = await getUserInfo();

    setUser({
      ...user,
      cards: userInfo?.cards,
    });
    setLoading(false);
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [fetchData]),
  );

  const navigateToCardDetail = () => {
    navigation.navigate('StackCards', {
      screen: 'CardDetail',
      params: { hasCardsShipping },
    });
  };

  const navigateToRequestCardIntroScreen = () => {
    navigation.navigate('StackCards', {
      screen: 'RequestCardIntroScreen',
    });
  };

  const GetCardButton = () => {
    return (
      <TouchableOpacity onPress={() => { }} style={styles.row}>
        <PlusIcon
          size={28}
          bgColor={colors.transparent}
          color={color.PRIMARY_700}
        />
        <TextBase type="Bold" color={color.PRIMARY_700}>
          Añadir nueva
        </TextBase>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <TextBase style={styles.title}>Tarjetas</TextBase>
        <GetCardButton />
      </View>
      {loading && (
        <View style={styles.indicator}>
          <ActivityIndicator size={64} color={color.PRIMARY_700} />
        </View>
      )}
      {!loading && !hasCards && (
        <View style={{ justifyContent: 'center', height: '100%' }}>
          <View style={styles.noCardsBannerContainer}>
            <View style={{ marginTop: -70, marginBottom: 8 }}>
              <PrepaidCardFloating />
            </View>
            <TextBase type="Bold" size="l">
              ¡Accedé hoy a tu Tarjeta Prepaga!
            </TextBase>
            <TextBase style={{ textAlign: 'center' }}>
              Solicitala sin cargo y utiliza el dinero de tu cuenta donde
              quieras.
            </TextBase>
            <View
              style={{
                alignItems: 'flex-start',
                gap: 10,
                width: '50%',
                marginTop: 8,
              }}
            >
              <View style={styles.bannerIcons}>
                <StoreIcon
                  size={28}
                  bgColor={color.PRIMARY_700}
                  color="white"
                />
                <TextBase size="s">
                  Usala en comercios y tiendas online
                </TextBase>
              </View>
              <View style={styles.bannerIcons}>
                <BookIcon size={28} bgColor={color.PRIMARY_700} color="white" />
                <TextBase size="s">
                  Obtené la Tarjeta Virtual y recibí también la física.
                </TextBase>
              </View>
              <View style={styles.bannerIcons}>
                <PlusIcon size={28} bgColor={color.PRIMARY_700} color="white" />
                <TextBase size="s">
                  Sumala a tu Wallet de Google y Apple
                </TextBase>
              </View>
            </View>
            <View style={{ width: '100%', marginTop: 10 }}>
              <Button
                text="Solicitar Tarjeta"
                onPress={navigateToRequestCardIntroScreen}
              />
            </View>
          </View>
        </View>
      )}
      {!loading && hasCards && (
        <Pressable onPress={navigateToCardDetail}>
          <CreditCard
            cardNumber={cards[0].last_four}
            cardHolder="Nombre Apellido"
          />
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 16,
    paddingHorizontal: 16,
    height: '100%',
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  noCardsBannerContainer: {
    borderColor: '#E1E3ED',
    borderRadius: 8,
    borderWidth: 1,
    backgroundColor: '#F7F8FE',
    alignItems: 'center',
    gap: 6,
    padding: 16,
  },
  bannerIcons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  indicator: {
    height: 100,
    justifyContent: 'center',
  },
});

export default CardsScreen;
