import { StyleSheet, View } from 'react-native';
import ProgressBar from '../../components/atoms/ProgressBar';
import { TextBase } from '../../components/atoms';
import { FC } from 'react';

interface QuotaProgressBarProps {
  currentInstallment: number;
  totalInstallments: number;
  duesPaid: number;
}

const QuotaProgressBar: FC<QuotaProgressBarProps> = ({
  duesPaid,
  currentInstallment,
  totalInstallments,
}) => (
  <View style={styles.containerRow}>
    <View style={{ flex: 0.9 }}>
      <ProgressBar percentage={(duesPaid / totalInstallments) * 100} />
    </View>
    <TextBase size="s">
      Cuota {currentInstallment} de {totalInstallments}
    </TextBase>
  </View>
);

const styles = StyleSheet.create({
  containerRow: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
});

export default QuotaProgressBar;
