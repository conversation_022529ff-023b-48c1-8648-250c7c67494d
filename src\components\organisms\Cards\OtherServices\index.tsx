import React, { useEffect, useState } from 'react';
import { View, FlatList, TouchableOpacity, Image } from 'react-native';
import { TextBase } from '../../../atoms';
import { Account, AccountResponse } from '../../../../types/Account';
import { useNavigation } from '@react-navigation/native';
import { LoaderModal } from '../../Modals/Loader';
import { styles } from './styles';
import { Skeleton } from '../../../atoms/Skeleton';
import ChevronIcon from '../../../atoms/Icons/Chevron';
import BankGo from '../../../atoms/Icons/BankGo';
import color from '../../../../theme/pallets/pallet';

const renderItem = ({ item }: any) => {
  return (
    <View style={styles.containerContact}>
      <TouchableOpacity onPress={() => {}}>
        <View style={styles.leftContent}>
          <View style={styles.circle}>{item.item.img}</View>
          <View>
            <TextBase
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.name}
              color={color.TEXT_PRIMARY}
              type="Bold"
              size="l"
            >
              {item.item.servicio}
            </TextBase>
          </View>
        </View>
      </TouchableOpacity>
      <View style={styles.flexRow}>
        <ChevronIcon />
      </View>
    </View>
  );
};

const OtherServices = () => {
  const navigation: any = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const data = [
    {
      id: '1',
      servicio: 'Claro',
      monto: 137.591,
      fecha_vencimiento: '11/4',
      num_service: '**********',
      img: <Image source={require('../../../../assets/img/claro.png')} />,
    },
    {
      id: '2',
      servicio: 'Movistar',
      monto: 7.846,
      fecha_vencimiento: '15/4',
      num_service: '*************',
      img: <Image source={require('../../../../assets/img/movistar.png')} />,
    },
    {
      id: '3',
      servicio: 'Personal',
      monto: 7.846,
      fecha_vencimiento: '20/4',
      num_service: '****************',
      img: <BankGo />,
    },
  ];

  const navigateToAmount = (accountDetails: Account[]) => {
    const selectedAccountData = accountDetails[0];
    setModalVisible(false);
    navigation.navigate('StackTransfer', {
      screen: 'AmountScreen',
      params: {
        nombre: selectedAccountData?.titulares[0].nombre,
        banco: '',
        cuit: selectedAccountData?.titulares[0].cuit,
        cvu: selectedAccountData?.cvu,
      },
    });
  };

  return (
    <>
      <LoaderModal modalVisible={loading} />

      <>
        <View style={styles.container}>
          <TextBase size="xl" color={color.TEXT_PRIMARY} style={styles.txt}>
            Telefonía / TV / Internet
          </TextBase>
          {loading ? (
            <>
              <Skeleton
                width={400}
                backgroundColor="#F7F8FE"
                highlightColor="#e8e9ed"
                height={40}
              />
            </>
          ) : (
            <FlatList
              data={data}
              renderItem={item =>
                renderItem({
                  item,
                  navigateToAmount,
                })
              }
              keyExtractor={item => item.id}
            />
          )}
        </View>
      </>
    </>
  );
};

export default OtherServices;
