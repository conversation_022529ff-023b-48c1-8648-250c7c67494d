import React, { FC } from 'react';
import { Pressable, View } from 'react-native';
import { ArrowRight } from '../../../atoms/Icons';
import { styles } from './styles';
import { Text } from '../../../atoms';
import { colors } from '../../../atoms/Text';

type Props = {
  label: string;
  labelColor?: colors;
  icon: React.ReactNode;
  arrow?: boolean;
  onPress?: () => void;
};

const MoreButton: FC<Props> = ({
  label,
  labelColor,
  icon,
  arrow = true,
  onPress,
}) => {
  return (
    <Pressable onPress={onPress}>
      <View style={styles.container} key={label}>
        <View style={styles.mr8}>{icon}</View>
        <View style={styles.flex1}>
          <Text variant="B7" color={labelColor}>
            {label}
          </Text>
        </View>
        {arrow && <ArrowRight size={16} color="#535D66" />}
      </View>
    </Pressable>
  );
};

export default MoreButton;
