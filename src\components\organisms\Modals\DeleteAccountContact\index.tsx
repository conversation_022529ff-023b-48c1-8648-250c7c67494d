import React from 'react';
import { Modal, View } from 'react-native';
import { Button, TextBase } from '../../../atoms';
import { styles } from './styles';
import { Account } from '../../../../types/Account';
import ButtonOutline from '../../../atoms/ButtonOutline';
import WarningIcon from '../../../atoms/Icons/Warning';
import color from '../../../../theme/pallets/pallet';

type Props = {
  modalView: boolean;
  setModalView: (value: boolean) => void;
  deleteAccount: () => void;
  accountData?: Account;
  name?: string;
  loaderModal?: boolean;
};

export const DeletecontactAccount = ({
  modalView,
  setModalView,
  deleteAccount,
  loaderModal,
}: Props) => {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={modalView}
      onRequestClose={() => {
        setModalView(!modalView);
      }}
    >
      <View style={styles.overlay}>
        <View style={styles.modalView}>
          <View style={styles.containerAccount}>
            <WarningIcon />
            <TextBase
              size="l"
              color={color.TEXT_PRIMARY}
              type="Bold"
              style={styles.textModal}
            >
              ¿Deseas eliminar la cuenta?
            </TextBase>
          </View>
          <View style={styles.mt24}>
            <Button
              onPress={deleteAccount}
              text="Si, eliminar"
              loading={loaderModal}
            />
            <ButtonOutline
              onPress={() => setModalView(false)}
              text="Cancelar"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};
