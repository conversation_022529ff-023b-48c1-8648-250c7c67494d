import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { stylesName } from './styles';
import { useNavigation } from '@react-navigation/native';
import { InputForm } from '../../components/molecules';
import { usePostUser } from '../../hooks/SignUp/SignUp';
import { usePasswordValidation } from '../../hooks/use-password-validation';
import { PasswordRepeat } from '../../components/organisms';
import Layout from '../../components/organisms/LayoutCreateAccount';

type Register = {
  nombre: string;
  apellido: string;
  numeroTelefono: string;
  paisResidencia: string;
  email: string;
  password: string;
  repeatPassword: string;
  codigoAreaTelefono: string;
  prefijoPais: string;
  cuit: number;
};

const isValidEmail = (email: string) =>
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

export default function EmailScreen({ route }: any) {
  const {
    firstName,
    lastName,
    phoneNumber,
    country,
    codigoAreaTelefono,
    prefijoPais,
    cuitCuil,
  } = route.params;
  const {
    password,
    repeatPassword,
    passwordValidation,
    passwordMatch,
    passwordValid,
    handlePasswordChange,
    handleRepeatPasswordChange,
  } = usePasswordValidation();

  const navigation: any = useNavigation();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);
  const [errorEmail, setErrorEmail] = useState<boolean>(false);
  const [errorPass, setErrorPass] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);

  const [register, setRegister] = useState<Register>({
    nombre: firstName,
    apellido: lastName,
    numeroTelefono: phoneNumber,
    paisResidencia: country,
    email: '',
    password: '',
    repeatPassword: '',
    codigoAreaTelefono: codigoAreaTelefono,
    prefijoPais: prefijoPais,
    cuit: Number(cuitCuil),
  });

  const { postUser } = usePostUser({
    setLoading,
    setError,
    setSuccess,
  });

  useEffect(() => {
    if (register.email.length > 0) {
      setError(!isValidEmail(register.email));
    }
    if (register.email.trim() === '') {
      setError(false);
    }
  }, [register.email]);

  const handleSubmit = async () => {
    if (!passwordValid) {
      setErrorPass(true);
      return;
    }

    register.password = password;
    register.repeatPassword = repeatPassword;
    await postUser(register);

    setSuccess(false);
  };

  if (success) {
    navigation.navigate('VerifyScreen', {
      email: register.email,
      password: register.password,
    });
  }

  const handleEmailChange = (email: string) => {
    setRegister(prev => ({ ...prev, email }));
    setErrorEmail(!isValidEmail(email));
  };

  const isDisabled = () => {
    return (
      !Object.values(passwordValidation).every(Boolean) ||
      !isValidEmail(register.email) ||
      password !== repeatPassword
    );
  };

  return (
    <Layout
      buttons={{
        primary: {
          onPress: handleSubmit,
          text: 'Siguiente',
          loading,
          disabled: isDisabled(),
        },
      }}
    >
      <>
        <View style={stylesName.containerInputs}>
          <Text style={stylesName.topText}>Correo electrónico</Text>
          <InputForm
            placeholder="ej. <EMAIL>"
            text={register.email}
            setText={handleEmailChange}
            error={errorEmail || error}
            errorText={
              errorEmail
                ? 'Ingresa un correo electrónico válido.'
                : error
                  ? 'El correo electrónico ya se encuentra registrado.'
                  : ''
            }
            autoCapitalize="none"
          />
          <Text style={stylesName.topText}>Contraseña</Text>
          <PasswordRepeat
            {...{
              password,
              repeatPassword,
              passwordValidation,
              passwordMatch,
              errorPass,
              handlePasswordChange,
              handleRepeatPasswordChange,
            }}
          />
        </View>
      </>
    </Layout>
  );
}
