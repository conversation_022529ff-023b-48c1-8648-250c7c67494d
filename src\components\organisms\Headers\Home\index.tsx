import { Pressable, View } from 'react-native';
import React, { FC } from 'react';
import { Text, TextBase, UserIcon } from '../../../atoms';
import { styles } from './styles';
import NotificationIcon from '../../../atoms/Icons/NotificationIcon';
import { Skeleton } from './Skeleton';
import { useNavigation } from '@react-navigation/native';
import color from '../../../../theme/pallets/pallet';

type HeaderHomeProps = {
  nombre: string;
  apellido: string;
  loading: boolean;
  navigate: (screen: string) => void;
  disabled?: boolean;
};

const HeaderHome: FC<HeaderHomeProps> = ({
  nombre,
  apellido,
  loading,
  navigate,
  disabled,
}) => {
  const navigation = useNavigation();

  if (loading) {
    return <Skeleton />;
  }
  return (
    <View style={styles.container}>
      <View style={styles.body}>
        <Pressable
          style={styles.flexRow}
          onPress={() =>
            navigation.navigate('StackUser', { screen: 'EditProfile' })
          }
          disabled={disabled}
        >
          {nombre && <UserIcon usernameInitials={nombre[0] + apellido[0]} />}
          <Text color={'WHITE'} variant="B6">
            ¡ Hola, {nombre} !
          </Text>
        </Pressable>
        <Pressable
          onPress={() => {
            navigate('Notifications');
          }}
          disabled={disabled}
        >
          <NotificationIcon color={color.WHITE} size={20} />
        </Pressable>
      </View>
    </View>
  );
};

export default HeaderHome;
