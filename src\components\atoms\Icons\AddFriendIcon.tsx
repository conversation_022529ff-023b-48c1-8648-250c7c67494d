import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const AddFriendIcon: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M21.4062 18.0938H19.5312V16.2188C19.5312 16.1156 19.4468 16.0312 19.3437 16.0312H18.2187C18.1156 16.0312 18.0312 16.1156 18.0312 16.2188V18.0938H16.1562C16.0531 18.0938 15.9687 18.1781 15.9687 18.2812V19.4062C15.9687 19.5094 16.0531 19.5938 16.1562 19.5938H18.0312V21.4688C18.0312 21.5719 18.1156 21.6562 18.2187 21.6562H19.3437C19.4468 21.6562 19.5312 21.5719 19.5312 21.4688V19.5938H21.4062C21.5093 19.5938 21.5937 19.5094 21.5937 19.4062V18.2812C21.5937 18.1781 21.5093 18.0938 21.4062 18.0938ZM9.25388 11.6813C9.23279 11.4773 9.22107 11.2711 9.22107 11.0625C9.22107 10.6898 9.25622 10.3266 9.32185 9.97266C9.33825 9.88828 9.29372 9.80156 9.21638 9.76641C8.89763 9.62344 8.60466 9.42656 8.35154 9.17813C8.05327 8.88892 7.81856 8.54072 7.66239 8.15574C7.50622 7.77076 7.43201 7.35745 7.4445 6.94219C7.4656 6.18984 7.76794 5.475 8.29529 4.93594C8.87419 4.34297 9.65232 4.01953 10.4797 4.02891C11.2273 4.03594 11.9492 4.32422 12.4953 4.83516C12.6804 5.00859 12.8398 5.20078 12.9734 5.40703C13.0203 5.47969 13.1117 5.51016 13.1914 5.48203C13.6039 5.33906 14.0398 5.23828 14.4875 5.19141C14.6187 5.17734 14.6937 5.03672 14.6351 4.91953C13.8734 3.4125 12.3172 2.37188 10.5172 2.34375C7.92029 2.30391 5.75466 4.43438 5.75466 7.03125C5.75466 8.50313 6.432 9.81563 7.49372 10.6758C6.74841 11.0203 6.06169 11.4961 5.46638 12.0914C4.182 13.3734 3.45544 15.0656 3.40857 16.8727C3.40794 16.8977 3.41233 16.9226 3.42147 16.9459C3.43061 16.9691 3.44433 16.9904 3.4618 17.0083C3.47927 17.0262 3.50015 17.0404 3.52321 17.0501C3.54627 17.0599 3.57104 17.0649 3.59607 17.0648H4.91091C5.01169 17.0648 5.09607 16.9852 5.09841 16.8844C5.14294 15.525 5.69372 14.2523 6.66169 13.2867C7.35075 12.5977 8.1945 12.1195 9.1156 11.8875C9.20466 11.8617 9.2656 11.775 9.25388 11.6813ZM19.8125 11.0625C19.8125 8.49844 17.7523 6.41484 15.1976 6.375C12.6008 6.33516 10.4375 8.46563 10.4375 11.0625C10.4375 12.5344 11.1172 13.8469 12.1765 14.707C11.4235 15.0561 10.738 15.5353 10.1515 16.1227C8.86716 17.4047 8.1406 19.0969 8.09372 20.9016C8.0931 20.9266 8.09749 20.9515 8.10663 20.9748C8.11577 20.998 8.12948 21.0193 8.14696 21.0372C8.16443 21.0551 8.18531 21.0693 8.20837 21.079C8.23143 21.0888 8.2562 21.0938 8.28122 21.0938H9.59372C9.6945 21.0938 9.77888 21.0141 9.78122 20.9133C9.82575 19.5539 10.3765 18.2812 11.3445 17.3156C12.3547 16.3055 13.6953 15.75 15.125 15.75C17.7125 15.75 19.8125 13.6523 19.8125 11.0625ZM17.2461 13.1836C16.6789 13.7508 15.9265 14.0625 15.125 14.0625C14.3234 14.0625 13.5711 13.7508 13.0039 13.1836C12.7208 12.902 12.4972 12.5665 12.3462 12.1969C12.1953 11.8274 12.12 11.4312 12.125 11.032C12.132 10.2633 12.439 9.52031 12.9758 8.96953C13.5383 8.39297 14.2906 8.07188 15.0945 8.0625C15.889 8.05547 16.6601 8.36484 17.2273 8.92031C17.8086 9.48984 18.1273 10.2516 18.1273 11.0625C18.125 11.8641 17.8133 12.6164 17.2461 13.1836Z"
        fill={color}
      />
    </Svg>
  );
};

export default AddFriendIcon;
