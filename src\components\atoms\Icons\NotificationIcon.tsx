import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const NotificationIcon: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width="28" height="29" viewBox="0 0 28 29" fill="none">
      <Path
        d="M13.9998 25.875C15.2832 25.875 16.3332 24.825 16.3332 23.5417L11.6665 23.5417C11.6665 24.825 12.7165 25.875 13.9998 25.875ZM20.9998 18.875L20.9998 13.0417C20.9998 9.46 19.0982 6.46167 15.7498 5.66833V4.875C15.7498 3.90667 14.9682 3.125 13.9998 3.125C13.0315 3.125 12.2498 3.90667 12.2498 4.875V5.66833C8.91317 6.46167 6.99984 9.44833 6.99984 13.0417L6.99984 18.875L4.6665 21.2083V22.375L23.3332 22.375V21.2083L20.9998 18.875ZM18.6665 20.0417L9.33317 20.0417L9.33317 13.0417C9.33317 10.1483 11.0948 7.79167 13.9998 7.79167C16.9048 7.79167 18.6665 10.1483 18.6665 13.0417L18.6665 20.0417Z"
        fill="white"
      />
    </Svg>
  );
};

export default NotificationIcon;
