import React from 'react';
import { Text, View } from 'react-native';
import { Image } from 'react-native';
import { styles } from './styles';

type ServiceInfoProps = {
  nombre: string;
  type?: string;
};

export const ServiceInfo = ({ nombre, type }: ServiceInfoProps) => {
  return (
    <View style={styles.container}>
      <Image
        source={require('../../../../assets/img/clarobig.png')}
        style={styles.mr12}
      />
      <View>
        <Text numberOfLines={2} style={styles.name}>
          {nombre}
        </Text>
        {type && <Text style={styles.cuenta}>{type}</Text>}
      </View>
    </View>
  );
};
