import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const BillsButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#E6DDFF" />

      <Path
        d="M14.3108 21H17.6892C17.7992 19.9983 18.31 19.1717 19.1392 18.2692C19.2333 18.1675 19.8325 17.5467 19.9033 17.4583C20.4919 16.723 20.8608 15.8364 20.9676 14.9006C21.0744 13.9649 20.9147 13.0179 20.5069 12.169C20.0991 11.32 19.4598 10.6034 18.6626 10.1018C17.8654 9.60023 16.9428 9.33401 16.0009 9.3338C15.059 9.3336 14.1363 9.59943 13.3389 10.1007C12.5415 10.6019 11.9019 11.3182 11.4937 12.167C11.0856 13.0158 10.9254 13.9627 11.0318 14.8985C11.1382 15.8344 11.5067 16.7211 12.095 17.4567C12.1667 17.5458 12.7675 18.1675 12.86 18.2683C13.69 19.1717 14.2008 19.9983 14.3108 21ZM17.6667 22.6667H14.3333V23.5H17.6667V22.6667ZM10.795 18.5C10.0102 17.5194 9.51829 16.3371 9.37605 15.0891C9.23381 13.8412 9.447 12.5785 9.99104 11.4465C10.5351 10.3144 11.3879 9.35906 12.4511 8.69049C13.5144 8.02191 14.7449 7.6673 16.0009 7.6675C17.2569 7.6677 18.4873 8.02271 19.5503 8.69162C20.6134 9.36054 21.4659 10.3162 22.0095 11.4484C22.5532 12.5806 22.766 13.8434 22.6234 15.0913C22.4807 16.3391 21.9885 17.5213 21.2033 18.5017C20.6867 19.145 19.3333 20.1667 19.3333 21.4167V23.5C19.3333 23.942 19.1577 24.366 18.8452 24.6785C18.5326 24.9911 18.1087 25.1667 17.6667 25.1667H14.3333C13.8913 25.1667 13.4674 24.9911 13.1548 24.6785C12.8423 24.366 12.6667 23.942 12.6667 23.5V21.4167C12.6667 20.1667 11.3125 19.145 10.795 18.5ZM16.8333 14.3367H18.9167L15.1667 19.3367V16.0033H13.0833L16.8333 11V14.3375V14.3367Z"
        fill="#5732BF"
      />
    </Svg>
  );
};

export default BillsButtonIcon;
