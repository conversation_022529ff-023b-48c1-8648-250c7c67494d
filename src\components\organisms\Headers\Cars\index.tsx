/* eslint-disable react-native/no-inline-styles */
import { View, TouchableOpacity } from 'react-native';
import React from 'react';
import { Text } from '../../../atoms';
import UpdateIcon from '../../../atoms/Icons/UpdateIcon';
import color from '../../../../theme/pallets/pallet';

type Props = {
  handlePress: () => void;
};
const HeaderCars = ({ handlePress }: Props) => {
  return (
    <View
      style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-end',
      }}
    >
      <Text variant="B2">Mis vehículos</Text>
      <TouchableOpacity onPress={handlePress}>
        <UpdateIcon color={color.PRIMARY_700} size={24} />
      </TouchableOpacity>
    </View>
  );
};

export default HeaderCars;
