import React, { <PERSON> } from 'react';
import Svg, { Path, Circle } from 'react-native-svg';
import { IconProps } from './types';

const SearchRounded: FC<IconProps> = ({ width, height }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 48 48" fill="none">
      <Circle cx="24" cy="24" r="24" fill="#FFE7E3" />
      <Path
        d="M27.755 26.255H26.965L26.685 25.985C27.665 24.845 28.255 23.365 28.255 21.755C28.255 18.165 25.345 15.255 21.755 15.255C18.165 15.255 15.255 18.165 15.255 21.755C15.255 25.345 18.165 28.255 21.755 28.255C23.365 28.255 24.845 27.665 25.985 26.685L26.255 26.965V27.755L31.255 32.745L32.745 31.255L27.755 26.255ZM21.755 26.255C19.265 26.255 17.255 24.245 17.255 21.755C17.255 19.265 19.265 17.255 21.755 17.255C24.245 17.255 26.255 19.265 26.255 21.755C26.255 24.245 24.245 26.255 21.755 26.255Z"
        fill="#FF0033"
      />
    </Svg>
  );
};

export default SearchRounded;
