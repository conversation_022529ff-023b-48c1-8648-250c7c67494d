import React from 'react';

import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Home } from '../screens';
import CardsScreen from '../screens/CardsScreen';
import { Platform } from 'react-native';
import { tabBarIcons } from '../helpers/tabBarIcons';
import { useUserContext } from '../context/UserContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import color from '../theme/pallets/pallet';
import {
  InvestmentButton,
  InvestmentIcon,
  InvestmentNullComponent,
} from '../components/atoms/InvestmentButton';
import { CarsScreen } from '../screens/stack-cars';
import ProfileScreen from '../screens/profile-screen';
import { Text } from '../components/atoms';

const Tab = createBottomTabNavigator();

const InvestmentButtonComponent = (
  props: React.JSX.IntrinsicAttributes & { children: React.JSX.Element },
) => <InvestmentButton {...props} />;
const InvestmentIconComponent = () => <InvestmentIcon />;

export default function TabBar() {
  const { user } = useUserContext();
  const insets = useSafeAreaInsets();

  const userOnboarding = user.onboardingStatus;

  const styleTabBar =
    insets.bottom === 0
      ? {
        borderTopWidth: 1,
        paddingBottom: 5,
        paddingTop: 7,
        height: 63,
      }
      : {
        height: 86,
        paddingTop: 6,
      };

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused }) => {
          const activeIcon = color.PRIMARY_500;
          const disabledIcon = color.NEUTRALS_800;
          let iconName = tabBarIcons(
            route.name,
            focused,
            activeIcon,
            disabledIcon,
            userOnboarding,
          );
          return iconName;
        },
        tabBarStyle: styleTabBar,
        tabBarActiveTintColor: color.PRIMARY_500,
        tabBarInactiveTintColor: color.NEUTRALS_800,
        tabBarHideOnKeyboard: Platform.OS === 'android',
        tabBarLabelStyle: {
          fontFamily: 'Satoshi-Bold',
          fontSize: 10,
          marginBottom: 5,
          // color: route.name === 'Cards' && '#BAC2C7',
        },
      })}
    >
      <Tab.Screen name="Home" options={{ title: 'Inicio' }} component={Home} />
      <Tab.Screen
        name="Cars"
        options={{ title: 'Mis vehículos' }}
        component={CarsScreen}
      />

      <Tab.Screen
        name="Invertir"
        component={InvestmentNullComponent}
        options={{
          tabBarIcon: InvestmentIconComponent,
          tabBarButton: InvestmentButtonComponent,
        }}
      />

      <Tab.Screen
        name="Cards"
        options={{
          tabBarLabel: () => (
            <Text
              // eslint-disable-next-line react-native/no-inline-styles
              style={{
                fontFamily: 'Satoshi-Bold',
                fontSize: 10,
                marginBottom: 5,
                color: '#BAC2C7',
              }}
            >
              Billetera
            </Text>
          ),
        }}
        component={CardsScreen}
        listeners={{
          tabPress: e => {
            e.preventDefault();
          },
        }}
      />
      <Tab.Screen
        name="More"
        options={{ title: 'Mi perfil' }}
        component={ProfileScreen}
      />
    </Tab.Navigator>
  );
}
