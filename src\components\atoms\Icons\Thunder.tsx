import Svg, { Path } from 'react-native-svg'
import { LoanIconTypeColor } from './types';
import { FC } from 'react';

const Thunder: FC<LoanIconTypeColor> = ({ size, iconColor }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20" fill="none">
      <Path d="M5.83337 1.66669V10.8334H8.33337V18.3334L14.1667 8.33335H10.8334L13.3334 1.66669H5.83337Z" fill={iconColor} />
    </Svg>
  );
};

export default Thunder;