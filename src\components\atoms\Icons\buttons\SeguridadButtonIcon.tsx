import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const SeguridadButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#F7F8FE" />

      <Path
        d="M9.75016 22.6663V14.333H19.7502V15.1663C20.3335 15.1663 20.8918 15.2497 21.4168 15.408V14.333C21.4168 13.4163 20.6668 12.6663 19.7502 12.6663H18.9168V10.9997C18.9168 8.69967 17.0502 6.83301 14.7502 6.83301C12.4502 6.83301 10.5835 8.69967 10.5835 10.9997V12.6663H9.75016C8.8335 12.6663 8.0835 13.4163 8.0835 14.333V22.6663C8.0835 23.583 8.8335 24.333 9.75016 24.333H14.9668C14.6168 23.833 14.3418 23.2663 14.1585 22.6663H9.75016ZM12.2502 10.9997C12.2502 9.61634 13.3668 8.49967 14.7502 8.49967C16.1335 8.49967 17.2502 9.61634 17.2502 10.9997V12.6663H12.2502V10.9997Z"
        fill="#535D66"
      />

      <Path
        d="M19.7502 16.833C17.4502 16.833 15.5835 18.6997 15.5835 20.9997C15.5835 23.2997 17.4502 25.1663 19.7502 25.1663C22.0502 25.1663 23.9168 23.2997 23.9168 20.9997C23.9168 18.6997 22.0502 16.833 19.7502 16.833ZM19.7502 18.4997C20.4418 18.4997 21.0002 19.058 21.0002 19.7497C21.0002 20.4413 20.4418 20.9997 19.7502 20.9997C19.0585 20.9997 18.5002 20.4413 18.5002 19.7497C18.5002 19.058 19.0585 18.4997 19.7502 18.4997ZM19.7502 23.4997C18.8918 23.4997 18.1335 23.0663 17.6835 22.3997C18.2918 22.0497 18.9918 21.833 19.7502 21.833C20.5085 21.833 21.2085 22.0497 21.8168 22.3997C21.3668 23.0663 20.6085 23.4997 19.7502 23.4997Z"
        fill="#535D66"
      />
    </Svg>
  );
};

export default SeguridadButtonIcon;
