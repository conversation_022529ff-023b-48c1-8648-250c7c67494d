
import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const LoansButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32" fill="none">
      <Rect width="32" height="32" rx="8" fill="#FFFAEC"/>
      <Path d="M16.325 15.0833C14.4333 14.5917 13.825 14.0833 13.825 13.2917C13.825 12.3833 14.6667 11.75 16.075 11.75C17.5583 11.75 18.1083 12.4583 18.1583 13.5H20C19.9417 12.0667 19.0667 10.75 17.325 10.325V8.5H14.825V10.3C13.2083 10.65 11.9083 11.7 11.9083 13.3083C11.9083 15.2333 13.5 16.1917 15.825 16.75C17.9083 17.25 18.325 17.9833 18.325 18.7583C18.325 19.3333 17.9167 20.25 16.075 20.25C14.3583 20.25 13.6833 19.4833 13.5917 18.5H11.7583C11.8583 20.325 13.225 21.35 14.825 21.6917V23.5H17.325V21.7083C18.95 21.4 20.2417 20.4583 20.2417 18.75C20.2417 16.3833 18.2167 15.575 16.325 15.0833Z" fill="#DDA921"/>
    </Svg>
  );
};

export default LoansButtonIcon;
