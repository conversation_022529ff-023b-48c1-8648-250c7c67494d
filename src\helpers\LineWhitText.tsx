import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import color from '../theme/pallets/pallet';

const LineWithText = ({ text }: { text: string }) => {
  return (
    <View style={styles.container}>
      <View style={styles.line} />
      <Text style={styles.text}>{text}</Text>
      <View style={styles.line} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 10,
  },
  text: {
    textAlign: 'center',
    fontFamily: 'Satoshi-Regular',
    color: '#78838D',
    fontSize: 14,
    marginHorizontal: 16,
    marginBottom: 4,
  },
  line: {
    flex: 1,
    height: 1,
    backgroundColor: color.NEUTRALS_100,
  },
});

export default LineWithText;
