import React, { FC } from 'react';
import Svg, { Circle, Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const ErrorImage: FC<IconProps> = ({ width, height }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 164 126" fill="none">
      <Circle cx="82.5" cy="63" r="63" fill="#E05555" />
      <Rect x="46.5" y="40" width="72" height="86" rx="8" fill="white" />
      <Path
        d="M82.5002 67.6433L90.7502 59.3933L93.1069 61.75L84.8569 70L93.1069 78.25L90.7502 80.6066L82.5002 72.3566L74.2502 80.6066L71.8936 78.25L80.1436 70L71.8936 61.75L74.2502 59.3933L82.5002 67.6433Z"
        fill="#E05555"
      />
      <Path
        d="M66.5 98H98.5"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Path
        d="M63.5 107H101.5"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Path
        d="M66.5 116H98.5"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Circle cx="13.5" cy="8" r="8" fill="#FFD6D6" />
      <Circle cx="155.5" cy="100" r="8" fill="#FFD6D6" />
      <Circle cx="5" cy="99.5" r="4.5" fill="#FFD6D6" />
      <Circle cx="155" cy="12.5" r="4.5" fill="#FFD6D6" />
    </Svg>
  );
};

export default ErrorImage;
