import React from 'react';
import { View } from 'react-native';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
const n = 5;

export const Skeleton = () => {
  return [...Array(n)].map((_, index) => (
    <View key={index}>
      <SkeletonPlaceholder>
        <SkeletonPlaceholder.Item
          flexDirection="row"
          alignItems="center"
          justifyContent="space-between"
        >
          <SkeletonPlaceholder.Item flexDirection="row" alignItems="center">
            <SkeletonPlaceholder.Item
              width={30}
              height={30}
              borderRadius={2}
              marginVertical={8}
            />

            <SkeletonPlaceholder.Item
              marginLeft={8}
              width={100}
              height={20}
              borderRadius={2}
            />
          </SkeletonPlaceholder.Item>
          <SkeletonPlaceholder.Item
            marginLeft={8}
            width={70}
            height={20}
            borderRadius={2}
          />
        </SkeletonPlaceholder.Item>
      </SkeletonPlaceholder>
    </View>
  ));
};
