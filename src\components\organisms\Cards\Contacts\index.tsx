import React, { useEffect, useState } from 'react';
import { View, FlatList, TouchableOpacity } from 'react-native';
import { useRecentTransfer } from '../../../../hooks/Transfers/recentTransfer';
import { Text, TextBase } from '../../../atoms';
import { Account, AccountResponse } from '../../../../types/Account';
import { api } from '../../../../services/apiService';
import { useNavigation } from '@react-navigation/native';
import { LoaderModal } from '../../Modals/Loader';
import { Accounts } from '../../Modals/Accounts';
import { styles } from './styles';
import { InputSearch } from '../../../molecules';
import DotsIcon from '../../../atoms/Icons/DotsIcon';
import { InfoContact } from '../../Modals/InfoContact';
import StarColor from '../../../atoms/Icons/StarColor';
import { useTransferContext } from '../../../../context/TransferContext';
import useContactStore from '../../../../context/UserZustand';
import { Skeleton } from '../../../atoms/Skeleton';
import color from '../../../../theme/pallets/pallet';

const renderItem = ({
  item,
  validateAccount,
  setModalContact,
  setDataContact,
}: any) => {
  const firstLetter = item.item.contactName?.charAt(0).toUpperCase();
  const secondLetter =
    item.item.contactName?.split(' ')[1]?.charAt(0).toUpperCase() || '';
  const numAccounts = item.item.accounts.length;
  return (
    <View style={styles.containerContact}>
      <TouchableOpacity
        onPress={() => validateAccount(item.item.accounts, numAccounts === 1)}
      >
        <View style={styles.leftContent}>
          <View style={styles.circle}>
            <Text variant="R6" color={'PRIMARY_700'}>
              {firstLetter}
              {secondLetter}
            </Text>
          </View>
          <View>
            <Text variant="B7" color="NEUTRALS_800">
              {item.item.contactName}
            </Text>
            {numAccounts > 1 ? (
              <Text variant="R7" color="NEUTRALS_800">
                {numAccounts} cuentas
              </Text>
            ) : (
              <Text variant="R7" color="NEUTRALS_800">
                Toshify
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>
      {/* <View style={styles.flexRow}>
        <View style={styles.star}>{item.item.isFavorite && <StarColor />}</View>
        <TouchableOpacity
          style={styles.flexRow}
          onPress={() => {
            setModalContact(true);
            setDataContact(item.item);
          }}
        >
          <DotsIcon />
        </TouchableOpacity>
      </View> */}
    </View>
  );
};

const ContactCard = () => {
  const navigation: any = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [accountData, setAccountData] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalContact, setModalContact] = useState(false);
  const [dataContact, setDataContact] = useState();
  const [searchText, setSearchText] = useState('');
  const setNameChanged = useContactStore(state => state.setNameChanged);
  const isNameChanged = useContactStore(state => state.isNameChanged);
  const isDeleteAccount = useContactStore(state => state.isDeleteAccount);
  const setDeleteAccount = useContactStore(state => state.setDeleteAccount);
  const {
    successData,
    recentTransfer,
    loading: loadingContacts,
  } = useRecentTransfer();

  const { setReloadContactsCallback } = useTransferContext();

  useEffect(() => {
    recentTransfer();
    setReloadContactsCallback(recentTransfer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (isNameChanged || isDeleteAccount) {
      recentTransfer();
      setNameChanged(false);
      setDeleteAccount(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNameChanged, isDeleteAccount]);

  const filteredData = successData?.filter(item =>
    item.contactName.toLowerCase().includes(searchText.toLowerCase()),
  );

  const validateAccount = async (
    accounts: Account[],
    hasOneDestinyAcc: boolean,
  ) => {
    setLoading(true);
    try {
      const accountDetails = await Promise.all(
        accounts?.map(async account => {
          const response = await api.get<AccountResponse>(
            `/digital/utils/validCvuAlias/${account.cbu}`,
          );
          return response.data.data;
        }),
      );

      setAccountData(accountDetails);
      setLoading(false);
      if (hasOneDestinyAcc) {
        navigateToAmount(accountDetails);
        return;
      }
      setModalVisible(true);
    } catch (err) {
      console.error(err);
      setLoading(false);
    }
  };

  const navigateToAmount = (accountDetails: Account[]) => {
    const selectedAccountData = accountDetails[0];
    setModalVisible(false);
    navigation.navigate('StackTransfer', {
      screen: 'AmountScreen',
      params: {
        nombre: selectedAccountData?.titulares[0].nombre,
        banco: '',
        cuit: selectedAccountData?.titulares[0].cuit,
        cvu: selectedAccountData?.cvu,
      },
    });
  };

  return (
    <>
      <LoaderModal modalVisible={loading} />

      <>
        <View style={styles.container}>
          <TextBase size="m" color={color.TEXT_PRIMARY} style={styles.txt}>
            Cuentas frecuentes
          </TextBase>
          <View style={styles.search}>
            <InputSearch
              placeholder={'Busca un contacto'}
              setText={setSearchText}
              text={searchText}
            />
          </View>
          {loadingContacts ? (
            <>
              <Skeleton
                width={400}
                backgroundColor="#F7F8FE"
                highlightColor="#e8e9ed"
                height={40}
              />
            </>
          ) : (
            <FlatList
              data={filteredData}
              renderItem={item =>
                renderItem({
                  item,
                  validateAccount,
                  setModalContact,
                  setDataContact,
                  navigateToAmount,
                })
              }
              keyExtractor={item => item._id}
            />
          )}
        </View>
        <Accounts
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          accountData={accountData}
        />
        <InfoContact
          modalContact={modalContact}
          setModalContact={setModalContact}
          accountData={dataContact}
        />
      </>
    </>
  );
};

export default ContactCard;
