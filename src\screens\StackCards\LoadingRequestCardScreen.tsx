import React, { FC, useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import StickLoader from '../../components/atoms/Loaders/StickLoader';
import TextBase from '../../components/atoms/TextBase';
import {
  createPhysicalCard,
  createVirutalCard,
} from '../../services/cardService';
import { getUserBasicInfo } from '../../services/users';
import color from '../../theme/pallets/pallet';

export const LoadingRequestCardScreen: FC<any> = ({ route, navigation }) => {
  const { virtualCardSelected, addsPhysicalCard, address } = route.params;
  const [error, setError] = useState<boolean>(false);
  const [requesting, setRequesting] = useState<boolean>(true);

  const navigateToRequestCardResultScreen = () => {
    navigation.navigate('StackCards', {
      screen: 'RequestCardResultScreen',
      params: { error },
    });
  };

  const createCards = async () => {
    try {
      if (virtualCardSelected) {
        await createVirutalCard({
          legalAddress: {
            additionalInfo:
              address.additionalInfo === '' ? null : address.additionalInfo,
            apartment: address.apartment === '' ? null : address.apartment,
            floor: address.floor === '' ? null : address.floor,
            city: address.city,
            neighborhood: address.neighborhood,
            region: address.region,
            streetName: address.streetName,
            streetNumber: address.streetNumber,
            zipCode: Number(address.zipCode),
          },
        });
      }

      if (addsPhysicalCard) {
        const loggedUserInfo = await getUserBasicInfo();

        await createPhysicalCard({
          address: {
            additionalInfo:
              address.additionalInfo === '' ? null : address.additionalInfo,
            apartment: address.apartment === '' ? null : address.apartment,
            floor: address.floor === '' ? null : address.floor,
            city: address.city,
            neighborhood: address.neighborhood,
            region: address.region,
            streetName: address.streetName,
            streetNumber: address.streetNumber,
            zipCode: Number(address.zipCode),
          },
          receiver: {
            fullName: loggedUserInfo?.fullName,
            documentNumber: loggedUserInfo?.documentNumber,
            documentType: loggedUserInfo?.documentType,
            email: loggedUserInfo?.email,
            taxIdentificationNumber: loggedUserInfo?.taxIdentificationNumber,
            telephoneNumber: loggedUserInfo?.telephoneNumber,
          },
        });
      }
    } catch (err: any) {
      setError(true);
    } finally {
      setRequesting(false);
    }
  };

  useEffect(() => {
    createCards();
  }, []);

  useEffect(() => {
    if (!requesting) {
      navigateToRequestCardResultScreen();
    }
  }, [error, requesting]);

  return (
    <View style={styles.container}>
      <StickLoader color={color.PRIMARY_700} />
      <TextBase size="l" style={styles.txtCenter}>
        Aguarda, estamos solicitando tu tarjeta prepaga...
      </TextBase>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    paddingHorizontal: 16,
  },
  txtCenter: {
    textAlign: 'center',
  },
});
