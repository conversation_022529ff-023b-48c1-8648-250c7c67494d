import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const ArrowRightSmall: FC<IconProps> = ({ size = 24, color = '#0068FF' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        d="M6.11523 20.23L7.88523 22L17.8852 12L7.88523 2L6.11523 3.77L14.3452 12L6.11523 20.23Z"
        fill={color}
      />
    </Svg>
  );
};

export default ArrowRightSmall;
