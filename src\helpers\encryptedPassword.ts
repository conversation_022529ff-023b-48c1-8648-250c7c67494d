import ReactNativeBiometrics from 'react-native-biometrics';

import { storeCredentials } from './asyncStorage';
const ReactNativeBiometricsInstance = new ReactNativeBiometrics();

const getIsSensorAvailable = async () => {
  const biometricSensor =
    await ReactNativeBiometricsInstance.isSensorAvailable();
  return biometricSensor.available;
};

export const checkCredentials = async (username: string, password: string) => {
  try {
    const isSensorAvailable = await getIsSensorAvailable();
    if (!isSensorAvailable) return;
    await storeCredentials(username, password);
  } catch (error) {
    console.error('Error checkCredentials:', error);
  }
};
