import { AuthContext } from '../../context/AuthContext';
import { api } from '../../services/apiService';
import { useContext, useState } from 'react';

export const useDeleteContact = () => {
  const [successData, setSuccessData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [success, setSuccess] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  const { userData } = useContext(AuthContext);

  const deleteContact = async ({ contactId }: any) => {
    try {
      setLoading(true);
      const response = await api.delete(`/contacts/${userData}/${contactId}`);
      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
        setLoading(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
      }
      setLoading(false);
    } catch (err: any) {
      console.error('Error:', JSON.stringify(err.response, null, 4));
      setError(true);
      setErrorMessage(err.response.data.message);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return { deleteContact, successData, errorMessage, loading, success, error };
};
