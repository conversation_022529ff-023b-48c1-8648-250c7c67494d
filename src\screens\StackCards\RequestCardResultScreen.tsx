import React, { FC } from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '../../constants/colors';
import TextBase from '../../components/atoms/TextBase';
import { Button } from '../../components/atoms';
import SuccessScreenCheck from '../../components/shared/Icons/SuccessScreenCheck';
import FailScreenCheck from '../../components/shared/Icons/FailScreenCheck';
import color from '../../theme/pallets/pallet';

export const RequestCardResultScreen: FC<any> = ({ navigation, route }) => {
  const { error } = route.params;
  const navigateToCardsScreen = () => {
    navigation.navigate('Cards');
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: error ? colors.backgroundError : color.WHITE },
      ]}
    >
      {!error && (
        <View style={styles.centeredView}>
          <SuccessScreenCheck />
          <TextBase type="Bold" size="l" style={styles.textCenter}>
            ¡Listo, ya estamos preparando tu tarjeta!
          </TextBase>
          <TextBase style={styles.textCenter}>
            En breve estará disponible en la sección Tarjetas, que la disfrutes.
          </TextBase>
        </View>
      )}
      {error && (
        <View style={styles.centeredView}>
          <FailScreenCheck />
          <TextBase type="Bold" size="l" style={styles.textCenter}>
            No pudimos generar la solicitud
          </TextBase>
          <TextBase style={styles.textCenter}>
            Intenta nuevamente o contacta a Atención al cliente para recibir más
            información.
          </TextBase>
        </View>
      )}
      <View style={styles.buttonsContainer}>
        <Button text="Volver a Tarjetas" onPress={navigateToCardsScreen} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: '100%',
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 20,
  },
  buttonsContainer: {
    paddingVertical: 20,
  },
  textCenter: {
    textAlign: 'center',
  },
});
