import React from 'react';
import { TextInput, View } from 'react-native';

import { yupResolver } from '@hookform/resolvers/yup';
import { StackScreenProps } from '@react-navigation/stack';
import { isNil } from 'lodash';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { Button, TextBase } from '../../components/atoms';
import { InputPin } from '../../components/molecules/InputPin';
import color from '../../theme/pallets/pallet';

type LoginFormInputs = {
  pin: string;
};
interface Props
  extends StackScreenProps<UnlockStackParams, 'CrearClaveScreen'> {}

const schema = yup
  .object({
    pin: yup
      .string()
      .required('La clave es requerida')
      .test(
        'len',
        'Tu clave debe tener 6 dígitos',
        value => value?.length === 6,
      ),
  })
  .required();

export default function CreatePINScreen({ navigation }: Props) {
  const inputRef = React.useRef<TextInput>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isDirty },
  } = useForm<LoginFormInputs>({
    resolver: yupResolver(schema),
    defaultValues: { pin: '' },
  });

  const onSubmit = async (data: LoginFormInputs) => {
    const { pin } = data;
    // navigation.navigate('AccesoRapidoExitosoScreen', {
    //   method: { type: 'pin', code: Number(pin) },
    // });
  };

  const handleOnPress = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <KeyboardAvoidingComponent collapse>
      <View
        style={{
          flex: 1,
          padding: 24,
          alignItems: 'center',
          justifyContent: 'space-between',
          // { paddingBottom: safeAreaInsets.bottom },}
        }}
      >
        <View
          style={{
            gap: 24,
            alignItems: 'center',
          }}
        >
          <TextBase size="xl" type="Bold" color={color.BLACK}>
            Configura el pin para acceder
          </TextBase>

          <View style={{ gap: 8 }}>
            <TextBase type="Regular">
              Ingresa un código de 4 a 6 dígitos
            </TextBase>
            <Controller
              name="pin"
              control={control}
              render={({ field: { onChange, value } }) => (
                <InputPin
                  value={Number(value)}
                  handleOnPress={handleOnPress}
                  onChange={text => onChange(text)}
                  inputRef={inputRef}
                  error={false}
                  maxLength={6}
                />
              )}
            />
            {!isNil(errors.pin) && (
              <View
              // style={styles`flex-row items-center`}
              >
                {/* <ErrorIcon /> */}
                <TextBase>{errors.pin.message}</TextBase>
              </View>
            )}
          </View>
        </View>
        <Button
          text="Continuar"
          onPress={handleSubmit(onSubmit)}
          disabled={!isNil(errors.pin) || !isDirty}
        />
      </View>
    </KeyboardAvoidingComponent>
  );
}
