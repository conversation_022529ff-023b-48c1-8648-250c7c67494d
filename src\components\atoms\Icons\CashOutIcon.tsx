import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const CashOutIcon: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 12 13">
      <Path
        d="M0 11.6666H12V13H0V11.6666ZM6.66667 2.88531V10.3333H5.33333V2.88531L1.286 6.93331L0.343333 5.99065L6 0.333313L11.6567 5.98998L10.714 6.93265L6.66667 2.88665V2.88531Z"
        fill={color}
      />
    </Svg>
  );
};

export default CashOutIcon;
