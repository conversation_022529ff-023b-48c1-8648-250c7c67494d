import React, { FC } from 'react';
import { View } from 'react-native';
import { Input, Text } from '../../atoms';
import { InputProps } from '../../atoms/Input';
import { styles } from './styles';
import color from '../../../theme/pallets/pallet';

interface InputFormProps extends InputProps {
  touched?: boolean;
  error?: boolean;
  errorText?: string;
  numeric?: boolean;
  maxLength?: number;
  style?: any;
  editable?: boolean;
  resend?: boolean;
  resendCode?: () => void;
}

const InputForm: FC<InputFormProps> = ({
  text,
  setText,
  onBlur,
  error,
  placeholder,
  hideText,
  errorText,
  numeric = false,
  maxLength,
  style,
  onFocus,
  editable,
  resend,
  resendCode,
}) => {
  const keyboardType = numeric ? 'numeric' : 'default';
  return (
    <View style={style}>
      <Input
        text={text}
        setText={setText}
        onBlur={onBlur}
        placeholder={placeholder}
        hideText={hideText}
        keyboardType={keyboardType}
        errorStyle={{
          borderColor: error ? color.RED_700 : color.NEUTRALS_200,
          color: error ? color.RED_700 : color.BLACK,
        }}
        maxLength={maxLength}
        onFocus={onFocus}
        editable={editable}
        resend={resend}
        resendCode={resendCode}
      // error={error}
      />
      {errorText && (
        <Text variant="R7" style={styles.error}>
          {errorText}
        </Text>
      )}
    </View>
  );
};

export default InputForm;
