import React from 'react';
import { KeyboardAvoidingView, Platform, ScrollView } from 'react-native';

import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

interface Props {
  children: React.ReactNode;
  collapse?: boolean;
  scrollEnabled?: boolean;
}

const KeyboardAvoidingComponent = ({
  children,
  collapse,
  scrollEnabled,
}: Props) => {
  if (Platform.OS === 'ios') {
    if (collapse) {
      return (
        <KeyboardAvoidingView
          behavior="padding"
          style={{ flex: 1 }}
          keyboardVerticalOffset={10}
        >
          <ScrollView
            contentContainerStyle={{ flexGrow: 1 }}
            showsHorizontalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {children}
          </ScrollView>
        </KeyboardAvoidingView>
      );
    }
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        keyboardShouldPersistTaps="handled"
        showsHorizontalScrollIndicator={false}
      >
        {children}
      </KeyboardAwareScrollView>
    );
  }

  return (
    <ScrollView
      contentContainerStyle={{ flexGrow: 1 }}
      keyboardShouldPersistTaps="handled"
      showsHorizontalScrollIndicator={false}
      scrollEnabled={scrollEnabled}
    >
      {children}
    </ScrollView>
  );
};

export default KeyboardAvoidingComponent;
