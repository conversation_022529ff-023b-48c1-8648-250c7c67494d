import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const ErrorIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 21">
      <Path
        d="M9.99996 18.8333C5.39746 18.8333 1.66663 15.1025 1.66663 10.5C1.66663 5.89749 5.39746 2.16666 9.99996 2.16666C14.6025 2.16666 18.3333 5.89749 18.3333 10.5C18.3333 15.1025 14.6025 18.8333 9.99996 18.8333ZM9.99996 17.1667C11.7681 17.1667 13.4638 16.4643 14.714 15.214C15.9642 13.9638 16.6666 12.2681 16.6666 10.5C16.6666 8.73188 15.9642 7.03619 14.714 5.78594C13.4638 4.5357 11.7681 3.83332 9.99996 3.83332C8.23185 3.83332 6.53616 4.5357 5.28591 5.78594C4.03567 7.03619 3.33329 8.73188 3.33329 10.5C3.33329 12.2681 4.03567 13.9638 5.28591 15.214C6.53616 16.4643 8.23185 17.1667 9.99996 17.1667ZM9.16663 13H10.8333V14.6667H9.16663V13ZM9.16663 6.33332H10.8333V11.3333H9.16663V6.33332Z"
        fill="#B83232"
      />
    </Svg>
  );
};

export default ErrorIcon;
