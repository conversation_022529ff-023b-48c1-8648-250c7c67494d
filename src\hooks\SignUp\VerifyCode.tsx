import { api } from '../../services/apiService';
import { useState } from 'react';

export const useVerifyCode = ({ setLoading, setError }: any) => {
  const [successData, setSuccessData] = useState<any>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const postCode = async (body: any) => {
    try {
      setLoading(true);
      const response = await api.post('/auth/valid-signup-otp', body);

      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
      }
    } catch (err: any) {
      console.error('Error:', err.response);
      setError(true);
    } finally {
      setLoading(false);
    }
  };

  return { postCode, successData, success };
};

export const useResendCode = () => {
  const [successData, setSuccessData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const resendCode = async (email: string) => {
    try {
      const response = await api.post('/auth/resend-signup-otp', email);

      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError('Unexpected status code');
      }
    } catch (err: any) {
      console.error('Error:', err.response);
      setError(err.message || 'An error occurred');
    }
  };

  return { resendCode, successData, error, success };
};
