import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const ArrowRight: FC<IconProps> = ({ size, color }) => {
  const width = size ? size + (66 % size) : 42;
  return (
    <Svg width={width} height={size} viewBox="0 0 16 16">
      <Path
        d="M8.78132 7.99999L5.48132 4.69999L6.42399 3.75732L10.6667 7.99999L6.42399 12.2427L5.48132 11.3L8.78132 7.99999Z"
        fill={color}
      />
    </Svg>
  );
};

export default ArrowRight;
