import React, { useState } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Text } from '../../atoms';
import ArrowDownIcon from '../../atoms/Icons/ArrowDownIcon';

export const SimpleAccordion = ({
  title,
  content,
}: {
  title: string;
  content: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <TouchableOpacity onPress={() => setIsOpen(!isOpen)}>
      <View style={styles.accordionContainer}>
        <Text variant="B6">{title}</Text>
        <ArrowDownIcon />
      </View>
      {isOpen && (
        <Text variant="R6" style={styles.content}>
          {content}
        </Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  accordionContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  content: {
    marginTop: 4,
  },
});
