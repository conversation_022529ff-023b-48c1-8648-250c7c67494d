import React, { FC } from 'react';
import Svg, { Circle, Ellipse, Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const SuccessImage: FC<IconProps> = ({ width, height }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 136 105" fill="none">
      <Circle cx="68.4138" cy="52.178" r="52.178" fill="#4DA66B" />
      <Rect
        x="38.5977"
        y="33.1289"
        width="59.632"
        height="71.2271"
        rx="8"
        fill="white"
      />
      <Path
        d="M65.0803 61.0291L80.4003 45.7074L82.7586 48.0641L65.0803 65.7424L54.4736 55.1358L56.8303 52.7791L65.0803 61.0291Z"
        fill="#4DA66B"
      />
      <Path
        d="M52.4136 83.7424H84.4136"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Path
        d="M49.4136 92.7424H87.4136"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Path
        d="M52.4136 101.742H84.4136"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Ellipse
        cx="11.2679"
        cy="6.62578"
        rx="6.62578"
        ry="6.62578"
        fill="#E6F7EC"
      />
      <Ellipse
        cx="128.874"
        cy="82.8223"
        rx="6.62578"
        ry="6.62578"
        fill="#E6F7EC"
      />
      <Circle cx="4.227" cy="82.408" r="3.727" fill="#E6F7EC" />
      <Circle cx="128.459" cy="10.3528" r="3.727" fill="#E6F7EC" />
    </Svg>
  );
};

export default SuccessImage;
