// src/useContactStore.js
import { create } from 'zustand';

const useContactStore = create(set => ({
  contactName: '',
  isNameChanged: false,
  isDeleteAccount: false,
  setContactName: (name: string) => set({ contactName: name }),
  resetContactName: () => set({ contactName: '' }),
  setNameChanged: (changed: boolean) => set({ isNameChanged: changed }),
  setDeleteAccount: (changed: boolean) => set({ isDeleteAccount: changed }),
}));

export default useContactStore;
