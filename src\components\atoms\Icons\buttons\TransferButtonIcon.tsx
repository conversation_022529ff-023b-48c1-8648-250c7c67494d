import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const TransferButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#FFE7E3" />

      <Path
        d="M22.1458 18.5858C22.6865 17.3011 22.811 15.8792 22.5017 14.52C22.1924 13.1609 21.465 11.9328 20.4216 11.0085C19.3782 10.0843 18.0714 9.51022 16.6849 9.36714C15.2983 9.22405 13.9018 9.51911 12.6917 10.2108L11.865 8.76333C13.1297 8.04051 14.5618 7.66197 16.0185 7.66548C17.4752 7.66899 18.9055 8.05443 20.1667 8.78333C23.9083 10.9433 25.3417 15.5683 23.5975 19.425L24.7158 20.07L21.245 21.915L21.1075 17.9867L22.1458 18.5858ZM9.85416 13.4142C9.31345 14.6989 9.18898 16.1208 9.49826 17.48C9.80753 18.8391 10.535 20.0672 11.5784 20.9915C12.6218 21.9157 13.9286 22.4898 15.3151 22.6329C16.7017 22.776 18.0982 22.4809 19.3083 21.7892L20.135 23.2367C18.8703 23.9595 17.4382 24.338 15.9815 24.3345C14.5248 24.331 13.0945 23.9456 11.8333 23.2167C8.09166 21.0567 6.65833 16.4317 8.40249 12.575L7.28333 11.9308L10.7542 10.0858L10.8917 14.0142L9.85333 13.415L9.85416 13.4142ZM17.1792 18.3567L14.82 16L12.4633 18.3567L11.285 17.1783L14.8208 13.6433L17.1783 16L19.5358 13.6433L20.7142 14.8217L17.1783 18.3567H17.1792Z"
        fill="#FF0033"
      />
    </Svg>
  );
};

export default TransferButtonIcon;
