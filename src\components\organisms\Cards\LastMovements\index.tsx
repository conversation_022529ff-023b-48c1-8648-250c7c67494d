import { View, Pressable } from 'react-native';
import React from 'react';
import { styles } from './styles';
import { TransferItem } from '../../../molecules';
import { Skeleton } from './Skeleton';
import { Transactions } from '../../../../types/Transfer';
import { Text } from '../../../atoms';

type LastMovementsCardProps = {
  navigationToActivity: (screen: string) => void;
  transfers: Transactions[];
  loading: boolean;
};

const LastMovementsCard = ({
  navigationToActivity,
  transfers,
  loading,
}: LastMovementsCardProps) => {
  return (
    <View style={styles.container}>
      <View style={styles.titleContainer}>
        <Text variant="B6">Últimas transacciones</Text>
        <View>
          <Pressable onPress={() => navigationToActivity('Activity')}>
            <Text variant="B6" color={'PRIMARY_700'}>
              Ver todas
            </Text>
          </Pressable>
        </View>
      </View>

      {loading ? (
        <Skeleton />
      ) : (
        transfers?.map(transfer => (
          <TransferItem key={transfer.id} transfer={transfer} />
        ))
      )}
    </View>
  );
};

export default LastMovementsCard;
