import { <PERSON>, ScrollView, StyleSheet } from 'react-native';
import React, { FC } from 'react';
import { TextBase } from '../components/atoms';

import { colors } from '../constants/colors';
import CardIcon from '../components/atoms/Icons/CardIcon';
import { LastMovementsCard } from '../components/organisms';
import CreditCard from '../components/molecules/GolletCreditCard';
import ChangePinIcon from '../components/atoms/Icons/ChangePinIcon';
import PauseIcon from '../components/atoms/Icons/PauseIcon';
import { Banner } from '../components/screens/home/<USER>';
import { api } from '../services/apiService';
import { getData } from '../services/asyncStorageService';
import { USER_ID } from '../constants/asyncStorageKeys';
import { useScreenLoaderContext } from '../context/screen-loader-context';
import color from '../theme/pallets/pallet';

const fakeTransfers = [
  {
    id: '7b299874-d11c-4916-a76d-12fcb35ee501',
    accountId: 'fa736f56-9ae7-47a8-9776-0a6a4315c63d',
    amount: 12.3,
    description: 'Transferencia',
    impactedOn: '2023-11-15T12:09:11.685Z',
    createdOn: '2023-11-15T12:09:11.833269Z',
    transactionReason: {
      id: 'e3ce08a5-99ad-4ad8-ba50-d7c8e9c24534',
      description: 'Credit',
    },
    sgInfo: {
      idCoelsa: 'n/a',
      cuitCredito: '***********',
      cvuCredito: '0000242600000000048886',
      txStatus: 'Procesado',
      txStatusId: '5fb79b38-00ed-47b8-b7ce-b760cbeae9d8',
      txTypeTxt: 'Transferencia',
    },
    smUnit: {
      symbol: 'ARS',
      description: 'Peso Argentino',
    },
    userName: 'Agus Zarate Zarate',
    counterpartName: 'Adalberto Marcelo  Gomez Fernandez',
  },
  {
    id: '********-b8ae-4843-9650-824e5ca00007',
    accountId: 'fa736f56-9ae7-47a8-9776-0a6a4315c63d',
    amount: 25.5,
    description: 'Transferencia',
    impactedOn: '2023-11-15T11:43:07.075Z',
    createdOn: '2023-11-15T11:43:07.259723Z',
    transactionReason: {
      id: 'e3ce08a5-99ad-4ad8-ba50-d7c8e9c24534',
      description: 'Credit',
    },
    sgInfo: {
      idCoelsa: 'n/a',
      cuitCredito: '***********',
      cvuCredito: '0000242600000000048886',
      txStatus: 'Procesado',
      txStatusId: '5fb79b38-00ed-47b8-b7ce-b760cbeae9d8',
      txTypeTxt: 'Transferencia',
    },
    smUnit: {
      symbol: 'ARS',
      description: 'Peso Argentino',
    },
    userName: 'Agus Zarate Zarate',
    counterpartName: 'Adalberto Marcelo  Gomez Fernandez',
  },
];

const buttons = [
  {
    icon: <CardIcon size={20} color={color.PRIMARY_700} />,
    title: 'Ver datos',
    onPress: () => {},
  },
  {
    icon: <ChangePinIcon size={20} color={color.PRIMARY_700} />,
    title: 'Cambiar PIN',
    onPress: () => {},
  },
  {
    icon: <PauseIcon size={20} color={color.PRIMARY_700} />,
    title: 'Pausar',
    onPress: () => {},
  },
];

const CardDetail: FC<any> = ({ route, navigation }) => {
  const { hasCardsShipping } = route.params;

  const setGlobalLoading = useScreenLoaderContext();

  const handleNavigateToRequestCardScreen = async () => {
    setGlobalLoading(true);
    try {
      const userId = await getData(USER_ID);
      const response = await api.post(`cards/activation-token/${userId}`);

      const end_user_token = response.data.data.access_token;

      const uri = `https://secure-data-web-stage.pomelo.la/v1/activate-card?auth=${end_user_token}&styles=https://my-style-page.com/styles.css&field_list=name,code,pan,expiration&layout=list`;

      navigation.navigate('ActivationWebViewScreen', { link: uri });
    } catch (error: any) {
      console.error('Error creating physical card:', error.response);
      throw error;
    } finally {
      setGlobalLoading(false);
    }
  };

  return (
    <ScrollView
      style={{
        flex: 1,
        backgroundColor: color.WHITE,
        paddingHorizontal: 16,
      }}
    >
      <View style={{ gap: 6 }}>
        <TextBase size="xxl" type="Bold">
          Tarjeta virtual
        </TextBase>
        <CreditCard cardNumber="**** 1234" cardHolder="Nombre Apellido" />
        <View
          style={{ flexDirection: 'row', gap: 8, justifyContent: 'center' }}
        >
          {buttons.map((button, index) => (
            <View
              key={index}
              style={{
                alignItems: 'center',
                flex: 1,
                gap: 4,
                padding: 12,
                backgroundColor: colors.backgroundLightPrimary,
                borderRadius: 4,
              }}
            >
              {button.icon}
              <TextBase type="Bold" size="m" color={color.PRIMARY_700}>
                {button.title}
              </TextBase>
            </View>
          ))}
        </View>

        <View style={{ marginHorizontal: -16 }}>
          {hasCardsShipping ? (
            <Banner
              activateCard
              title="Tu tarjeta está en camino"
              subtitle="Cuando la recibas, activala para empezar a comprar."
              buttonText="Activar mi tarjeta"
              navigateToRequestCardScreen={handleNavigateToRequestCardScreen}
            />
          ) : (
            <Banner
              title="Pedí la tarjeta física"
              subtitle="Usala en comercios y tiendas online."
              buttonText="Pedir tarjeta física"
              navigateToRequestCardScreen={() => {}}
            />
          )}
        </View>

        <LastMovementsCard
          navigationToActivity={() => {}}
          transfers={fakeTransfers}
          loading={false}
        />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    height: 170,
    marginVertical: 24,
  },
  imageBackground: {
    flex: 1,
  },
  cardContent: {
    margin: 30,
    width: '50%',
  },
  getCardButton: {
    width: 140,
    height: 30,
    backgroundColor: 'white',
    paddingVertical: 2,
    borderRadius: 5,
    marginTop: 12,
  },
});

export default CardDetail;
