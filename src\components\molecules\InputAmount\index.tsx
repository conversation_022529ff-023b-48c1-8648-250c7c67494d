import React from 'react';
import {
  NativeSyntheticEvent,
  Pressable,
  Text,
  TextInput,
  TextInputKeyPressEventData,
  View,
} from 'react-native';
import { styles } from '../InputPin/styles';
import { formatCurrency } from '../../../helpers/formatCurrency';
import color from '../../../theme/pallets/pallet';
type Props = {
  amount: number;
  handleKeyPress: (e: NativeSyntheticEvent<TextInputKeyPressEventData>) => void;
  inputRef: React.RefObject<TextInput>;
  handleOnPress: () => void;
  error?: boolean;
  editable?: boolean;
};

export const InputAmount = ({
  amount,
  handleOnPress,
  handleKeyPress,
  inputRef,
  error,
  editable,
}: Props) => {
  return (
    <View
      style={[
        styles.mainContainer,
        { borderBottomColor: error ? color.RED_700 : color.PRIMARY_700 },
      ]}
    >
      <Pressable onPress={handleOnPress} style={{ flex: 1, minHeight: 64 }}>
        <View style={styles.container}>
          <View style={{ zIndex: 1 }}>
            <Text
              style={[
                styles.textInput,
                {
                  color: amount
                    ? error
                      ? color.RED_700
                      : color.TEXT_PRIMARY
                    : color.NEUTRALS_400,
                },
              ]}
            >
              $ {formatCurrency(Number(amount)) || '00.00'}
            </Text>
          </View>
          <TextInput
            ref={inputRef}
            style={styles.input}
            onKeyPress={handleKeyPress}
            keyboardType="numeric"
            editable={editable}
          />
        </View>
      </Pressable>
    </View>
  );
};
