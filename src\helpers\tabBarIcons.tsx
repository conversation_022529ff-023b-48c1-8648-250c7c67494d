import React from 'react';
import { HomeIcon } from '../components/atoms/Icons';
import CarsIcon from '../components/atoms/Icons/CarsIcon';
import WalletIcon from '../components/atoms/Icons/WalletIcon';
import ProfileIcon from '../components/atoms/Icons/ProfileIcon';

export const tabBarIcons = (
  route: string,
  focused: boolean,
  activeIcon: string,
  disabledIcon: string,
) => {
  let icon: Element = '';

  const color = focused ? activeIcon : disabledIcon;
  switch (route) {
    case 'Home':
      icon = <HomeIcon selected={false} size={30} color={color} />;
      break;
    case 'Cars':
      icon = <CarsIcon selected={false} size={30} color={color} />;
      break;
    case 'Cards':
      icon = <WalletIcon size={30} color={'#BAC2C7'} />;
      break;
    case 'More':
      icon = <ProfileIcon selected={false} size={30} color={color} />;
      break;
    default:
      icon = <></>;
  }
  return icon;
};
