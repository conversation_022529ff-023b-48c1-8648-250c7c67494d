/* eslint-disable no-catch-shadow */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useContext, useEffect, useRef, useState } from 'react';
import { Text, TextInput, TouchableWithoutFeedback, View } from 'react-native';
import { stylesCountry, stylesVerify } from './styles';
import { useNavigation } from '@react-navigation/native';
import { Button, TextBase } from '../../components/atoms';
import { InputVerify } from '../../components/molecules/InputVerify';
import { useResendCode, useVerifyCode } from '../../hooks/SignUp/VerifyCode';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { AuthContext } from '../../context/AuthContext';
import color from '../../theme/pallets/pallet';

export const VerifyCode = ({ route }: any) => {
  const [code, setCode] = useState('');
  const [resendDisabled, setResendDisabled] = useState(true);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(35);
  const [loading, setLoading] = useState<boolean>(false);
  const [isCodeComplete, setIsCodeComplete] = useState(false);
  const [error, setError] = useState<boolean>(false);
  const navigation: any = useNavigation();

  const { signIn } = useContext(AuthContext);

  const email = route.params.email;
  const password = route.params.password;

  const inputRef = useRef<TextInput>(null);

  const handleOnPress = () => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const { postCode, success } = useVerifyCode({ setLoading, setError });

  const { resendCode } = useResendCode();

  useEffect(() => {
    const body: any = {
      email: email,
    };
    resendCode(body);
  }, []);

  const handleContinue = async () => {
    setLoading(true);
    try {
      const body = {
        username: email,
        code: code,
      };
      await postCode(body);
    } catch (err) {
      setLoading(false);
    }
  };

  const successLogin = async () => {
    setLoading(true);
    try {
      const loginResponse = await signIn(email, password);
      if (loginResponse.data) {
        navigation.navigate('ChangePassword', {
          email: email,
          password: password,
        });
      }
    } catch (err) {
      console.error('Error al iniciar sesión:', err);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (success) {
      successLogin();
    }
  }, [success]);

  const handleResendCode = async () => {
    setMinutes(0);
    setSeconds(35);
    setResendDisabled(true);
    const body: any = {
      email: email,
    };
    await resendCode(body);
  };

  const handleCodeChange = (value: string) => {
    setCode(value);
    if (value.length === 6) {
      setIsCodeComplete(true);
    } else {
      setIsCodeComplete(false);
    }
  };

  useEffect(() => {
    if (code.length > 0) {
      setError(false);
    }
  }, [code]);

  useEffect(() => {
    const timer = setInterval(() => {
      if (seconds === 0) {
        if (minutes === 0) {
          clearInterval(timer);
          setResendDisabled(false);
          return;
        } else {
          setMinutes(minutes - 1);
          setSeconds(59);
        }
      } else {
        setSeconds(seconds - 1);
      }
    }, 1000);
    return () => clearInterval(timer);
  }, [minutes, seconds]);

  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

  return (
    <KeyboardAvoidingComponent collapse>
      <View style={stylesCountry.container}>
        <View>
          <View style={stylesVerify.containerText}>
            <Text style={stylesVerify.topText}>
              Enviamos un código al correo electrónico
            </Text>
            <Text style={stylesVerify.botText}>{email}</Text>
          </View>
          <View style={stylesVerify.containerCode}>
            <Text style={stylesVerify.txtCode}>Ingresa el código</Text>
            <InputVerify
              code={code}
              handleOnPress={handleOnPress}
              handleOnChange={handleCodeChange}
              inputRef={inputRef}
              error={error}
              maxLength={6}
            />
            {error && (
              <TextBase
                size="l"
                type="SemiBold"
                color={color.RED_700}
                style={stylesVerify.errorCodetxt}
              >
                Código incorrecto
              </TextBase>
            )}
          </View>
          <View style={stylesVerify.resendContainer}>
            {resendDisabled ? (
              <Text style={stylesVerify.textCont}>
                Reenviar código{' '}
                <Text style={stylesVerify.countdownText}>
                  {minutes < 10 ? `0${minutes}` : minutes}:{formattedSeconds}
                </Text>
              </Text>
            ) : (
              <TouchableWithoutFeedback onPress={handleResendCode}>
                <Text style={stylesVerify.txtResend}>
                  Reenviar código{' '}
                  <Text style={stylesVerify.txtCodeResend}>00:00</Text>
                </Text>
              </TouchableWithoutFeedback>
            )}
          </View>
        </View>
        <Button
          text="Verificar código"
          onPress={handleContinue}
          loading={loading}
          disabled={!isCodeComplete}
        />
      </View>
    </KeyboardAvoidingComponent>
  );
};
