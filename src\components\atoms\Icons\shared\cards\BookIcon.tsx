import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../../types';

interface Props extends IconProps {
  bgColor: string;
}

const BookIcon: FC<Props> = ({ size, color, bgColor }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 28 28" fill="none">
      <Rect width={size} height={size} rx="4" fill={bgColor} />
      <Path d="M20.6667 5.66669H7.33341C6.40841 5.66669 5.66675 6.40835 5.66675 7.33335V16.5C5.66675 17.425 6.40841 18.1667 7.33341 18.1667H10.6667V22.3334L14.0001 20.6667L17.3334 22.3334V18.1667H20.6667C21.5917 18.1667 22.3334 17.425 22.3334 16.5V7.33335C22.3334 6.40835 21.5917 5.66669 20.6667 5.66669ZM20.6667 16.5H7.33341V14.8334H20.6667V16.5ZM20.6667 12.3334H7.33341V7.33335H20.6667V12.3334Z" fill={color} />
    </Svg>
  );
};

export default BookIcon;