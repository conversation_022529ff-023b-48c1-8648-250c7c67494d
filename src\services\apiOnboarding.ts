import axios from 'axios';
import Config from 'react-native-config';
import { getDecryptedData } from '../helpers/decryptCBC';

const getApiKey = async () => {
  const dataDecrypted = await getDecryptedData(Config.API_KEY ?? '');
  return dataDecrypted;
};

const createApiOnboarding = async () => {
  const apiKey = await getApiKey();
  const axiosRequestConfig = {
    baseURL: Config.API_URL,
    headers: {
      'x-api-key': apiKey,
    },
  };

  return axios.create(axiosRequestConfig);
};

export const getApiOnboardingInstance = async () => {
  return createApiOnboarding();
};
