<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Toshify</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Need to access your camera to capture a photo add and update profile picture.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Requires FaceID access to allows you quick and secure access.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>UIAppFonts</key>
	<array>
		<string>Sora-Bold.ttf</string>
		<string>Sora-ExtraBold.ttf</string>
		<string>Sora-ExtraLight.ttf</string>
		<string>Sora-Light.ttf</string>
		<string>Sora-Medium.ttf</string>
		<string>Sora-Regular.ttf</string>
		<string>Sora-SemiBold.ttf</string>
		<string>Sora-Thin.ttf</string>
		<string>Satoshi-Black.otf</string>
		<string>Satoshi-BlackItalic.otf</string>
		<string>Satoshi-BoldItalic.otf</string>
		<string>Satoshi-Italic.otf</string>
		<string>Satoshi-Light.otf</string>
		<string>Satoshi-LightItalic.otf</string>
		<string>Satoshi-Medium.otf</string>
		<string>Satoshi-MediumItalic.otf</string>
		<string>Satoshi-Bold.ttf</string>
		<string>Satoshi-Regular.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarHidden</key>
	<true/>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
