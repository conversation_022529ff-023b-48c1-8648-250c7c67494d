import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Platform, PermissionsAndroid } from 'react-native';
import {
  Camera,
  Code,
  useCameraDevice,
  useCodeScanner,
} from 'react-native-vision-camera';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { TextBase } from '../../components/atoms';

const NoCameraDeviceError = () => {
  return (
    <View style={styles.container}>
      <TextBase>No camera device available</TextBase>
    </View>
  );
};

export const QrScreen = () => {
  const [cameraPermission, setCameraPermission] = useState(false);

  const navigation: any = useNavigation();

  const device = useCameraDevice('back');

  const isFocussed = useIsFocused();

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Permisos de cámara',
            message:
              'La aplicación necesita acceder a la cámara para funcionar correctamente.',
            buttonNeutral: 'Preguntar después',
            buttonNegative: 'Cancelar',
            buttonPositive: 'OK',
          },
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setCameraPermission(true);
        } else {
          setCameraPermission(false);
        }
      } catch (err) {
        console.warn(err);
        setCameraPermission(false);
      }
    } else {
      setCameraPermission(true);
    }
  };

  useEffect(() => {
    requestCameraPermission();
  }, []);

  const handleCodeScanned = (data: Code[]) => {
    try {
      JSON.parse(data[0].value);
      navigation.navigate('QrPayment', data);
    } catch (e) {
      console.log('error', e);
      navigation.navigate('ErrorScreen');
    }
  };

  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13'],
    onCodeScanned: data => {
      if (data) {
        handleCodeScanned(data);
      }
    },
  });

  if (!device) {
    return <NoCameraDeviceError />;
  }

  return (
    <View style={styles.container}>
      {!cameraPermission ? (
        <TextBase>Por favor, conceda permisos de cámara</TextBase>
      ) : (
        <>
          <Camera
            style={StyleSheet.absoluteFill}
            device={device!}
            codeScanner={codeScanner}
            isActive={isFocussed}
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  camera: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
});
