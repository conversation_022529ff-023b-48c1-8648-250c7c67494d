import EncryptedStorage from 'react-native-encrypted-storage';
import { ENCRYPTED_CREDENTIALS } from '../constants/asyncStorageKeys';

export const removeCredentials = async () => {
  try {
    await EncryptedStorage.removeItem(ENCRYPTED_CREDENTIALS);
  } catch (error) {
    console.error('Error removing credentials', error);
  }
};

export const storeCredentials = async (username: string, password: string) => {
  await EncryptedStorage.setItem(
    ENCRYPTED_CREDENTIALS,
    JSON.stringify({ username, password }),
  );
};

export const getCredentials = async () => {
  const { username, password } = JSON.parse(
    (await EncryptedStorage.getItem(ENCRYPTED_CREDENTIALS)) ?? '{}',
  );
  return { username, password };
};
