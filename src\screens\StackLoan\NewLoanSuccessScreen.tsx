import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  Pressable,
  ToastAndroid,
  Clipboard,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { colors } from '../../constants/colors';
import TextBase from '../../components/atoms/TextBase';
import NewLoanSuccesIcon from '../../components/shared/Icons/SuccessScreenCheck';
import { Button } from '../../components/atoms';
import { formatCurrency } from '../../helpers/formatCurrency';
import CopyIcon from '../../components/atoms/Icons/CopyIcon';
import FlagError from '../../components/atoms/Icons/FlagError';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

type ItemProps = {
  label: string;
  value: string;
  copyIcon?: JSX.Element;
};

export const NewLoanSuccessScreen = ({ navigation, route }: Props) => {
  const { loanId, requestAmount } = route.params;

  const navigateToLoanDetailedScreen = () => {
    navigation.navigate('StackLoan', {
      screen: 'LoanDetailedScreen',
      params: { loanId },
    });
  };

  const navigateToLoansHome = () => {
    navigation.navigate('StackLoan', {
      screen: 'LoansHomeScreen',
    });
  };

  const DescriptionItem = ({ label, value, copyIcon }: ItemProps) => {
    const copyText = (text: string) => {
      Clipboard.setString(text);
      ToastAndroid.show('Texto copiado al portapapeles', ToastAndroid.SHORT);
    };
    return (
      <View style={styles.containerItem}>
        <Text style={styles.label}>{label}</Text>
        <View style={styles.flexRow}>
          <Text style={styles.value}>{value}</Text>
          {copyIcon && (
            <Pressable onPress={() => copyText(value)}>{copyIcon}</Pressable>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.messageContainer}>
        <NewLoanSuccesIcon />
        <TextBase size="l" type="Bold">
          ¡Has solicitado tu préstamo!
        </TextBase>
        <TextBase color={color.NEUTRALS_800} style={styles.textCenter}>
          Estamos revisando tu solicitud, en breve recibirás un correo
          electrónico de seguimiento.
        </TextBase>
      </View>

      <View style={styles.detailsContainer}>
        <TextBase type="Bold">Detalle del préstamo</TextBase>
        <DescriptionItem
          label="Importe"
          value={`$${formatCurrency(Number(requestAmount))}`}
        />
        <DescriptionItem
          label="Nº solicitud"
          value={loanId}
          copyIcon={<CopyIcon size={24} color={color.PRIMARY_500} />}
        />
        <View style={styles.errorContainer}>
          <FlagError width={20} height={20} color={colors.negative} />
          <Text style={styles.error}>Reportar un problema</Text>
        </View>
      </View>

      <View style={styles.buttonsContainer}>
        <ButtonOutline
          text="Ver detalle"
          onPress={navigateToLoanDetailedScreen}
        />
        <Button text="Volver a préstamos" onPress={navigateToLoansHome} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  messageContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    gap: 8,
  },
  detailsContainer: {
    marginTop: 30,
    width: '90%',
    gap: 16,
  },
  textCenter: {
    textAlign: 'center',
  },
  value: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
  containerItem: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    paddingHorizontal: 12,
    paddingVertical: 16,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  label: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 12,
    color: '#78838D',
    textTransform: 'capitalize',
  },
  error: {
    color: colors.negative,
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    marginLeft: 2,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
  },
  buttonsContainer: {
    width: '100%',
    paddingHorizontal: 20,
    paddingVertical: 10,
    gap: 12,
  },
});
