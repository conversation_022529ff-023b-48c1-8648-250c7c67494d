import React from 'react';
import { Modal, Pressable, Text, View } from 'react-native';
import { styles } from './styles';
import RecibirTransferencia from '../../../atoms/Icons/RecibirTransferencia';
import EnviarTransferencia from '../../../atoms/Icons/EnviarTransferencia';
import moment from 'moment';
import 'moment/locale/es';
import FlagError from '../../../atoms/Icons/FlagError';
import { colors } from '../../../../constants/colors';
import CopyIcon from '../../../atoms/Icons/CopyIcon';
import QrPayment from '../../../atoms/Icons/QrPayment';
import ButtonOutline from '../../../atoms/ButtonOutline';
import { useNavigation } from '@react-navigation/native';
import { useUserContext } from '../../../../context/UserContext';
import Toast from 'react-native-toast-message';
import { useCopyCustomToast } from '../../../../hooks/useCopyCustomToast';
import color from '../../../../theme/pallets/pallet';

type ItemProps = {
  label: string;
  value: string;
  icon?: React.JSX.Element;
};

const Item = ({ label, value, icon }: ItemProps) => {
  const { showToast, toastConfig } = useCopyCustomToast();
  return (
    <View style={styles.containerItem}>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.flexRow}>
        <Text style={styles.value}>{value}</Text>
        {icon && <Pressable onPress={() => showToast(value)}>{icon}</Pressable>}
      </View>
      <Toast config={toastConfig} />
    </View>
  );
};

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  accountData?: any;
  date: any;
  amountNegative: string;
  amountPositive: string;
};

export const InfoMovementModal = ({
  modalVisible,
  setModalVisible,
  accountData,
  date,
  amountNegative,
  amountPositive,
}: Props) => {
  const { amount, counterpartName } = accountData || {};
  moment.locale('es');
  const navigation: any = useNavigation();
  const { user } = useUserContext();
  const nrTransaction = accountData?.operationId;
  const idCoelsa =
    accountData?.sgInfo.idCoelsa === 'n/a' ||
    accountData?.sgInfo.idCoelsa === ''
      ? 'No aplica'
      : accountData?.sgInfo.idCoelsa;
  const idCoelsaIcon =
    accountData?.sgInfo.idCoelsa !== 'n/a' &&
    accountData?.sgInfo.idCoelsa !== '' ? (
      <CopyIcon size={24} color={color.PRIMARY_500} />
    ) : null;

  const Items = [
    // {
    //   label: 'Banco',
    //   value: 'Banco Nombre',
    // },
    {
      label: 'Fecha',
      value: date,
    },
    {
      label: 'Nº transacción',
      value: nrTransaction,
      icon: <CopyIcon size={24} color={color.PRIMARY_500} />,
    },
    {
      label: 'ID coelsa',
      value: idCoelsa,
      icon: idCoelsaIcon,
    },
  ];
  const goReceipt = () => {
    navigation.navigate('ReceiptScreen', {
      operationId: accountData.operationId,
      // date: new Date(),
      amount: Number(amount),
      motive: accountData.trxSubject,
      from: user.nombre + ' ' + user.apellido,
      fromCuit: user.cuit,
      fromCVU: user.cvu[0].cvu,
      toName: accountData.counterpartName,
      toCuit: accountData.cuitCredito,
      toCvu: accountData.cvuCredito,
      idCoelsa: accountData.sgInfo.idCoelsa,
    });
    setModalVisible(false);
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <View style={styles.overlay}>
        <View style={styles.modalView}>
          <View>
            <View style={styles.containerAccount}>
              {accountData?.description?.toLowerCase()?.includes('pago qr') ? (
                <QrPayment />
              ) : amount > 0 ? (
                <RecibirTransferencia />
              ) : (
                <EnviarTransferencia />
              )}
              <View style={styles.account}>
                <Text style={styles.name} numberOfLines={2}>
                  {counterpartName}
                </Text>
                <Text style={styles.data}>
                  {accountData?.transactionReason.description}
                </Text>
              </View>
              <Pressable
                onPress={() => setModalVisible(false)}
                style={styles.close}
              >
                <Text style={styles.textClose}>Listo</Text>
              </Pressable>
            </View>
            {amount !== undefined && amount > 0 ? (
              <View style={styles.containerItemAmountGreen}>
                <Text style={styles.valueGreen}>+${amountPositive}</Text>
              </View>
            ) : (
              <View style={styles.containerItemAmountRed}>
                <Text style={styles.valueRed}>-${amountNegative}</Text>
              </View>
            )}
            {Items.map((item, i) => (
              <Item
                label={item.label}
                value={item.value!}
                key={i}
                icon={item.icon!}
              />
            ))}
          </View>
          <View style={styles.bot}>
            {amount !== undefined && amount < 0 && (
              <ButtonOutline onPress={goReceipt} text="Ver comprobante" />
            )}
            <View style={styles.mt24}>
              <FlagError width={20} height={20} color={colors.negative} />
              <Text style={styles.error}>Reportar un problema</Text>
            </View>
          </View>
        </View>
      </View>
    </Modal>
  );
};
