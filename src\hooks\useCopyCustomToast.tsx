import { Platform, Text, ToastAndroid, View } from 'react-native';
import { colors } from '../constants/colors';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast, { BaseToastProps } from 'react-native-toast-message';

export const useCopyCustomToast = () => {
  const toastConfig = {
    success: ({ text1 }: BaseToastProps) => (
      <View
        style={{
          backgroundColor: colors.backgroundGrey,
          paddingVertical: 12,
          paddingHorizontal: 16,
          borderRadius: 16,
        }}
      >
        <Text>{text1}</Text>
      </View>
    ),
  };

  const showToast = (text: string) => {
    Clipboard.setString(text);
    if (Platform.OS === 'android') {
      ToastAndroid.show('Texto copiado al portapapeles', ToastAndroid.SHORT);
    } else {
      Toast.show({
        type: 'success',
        text1: 'Texto copiado al portapapeles',
        position: 'bottom',
      });
    }
  };

  return { showToast, toastConfig };
};
