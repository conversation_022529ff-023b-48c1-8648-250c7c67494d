import ReactNativeBiometrics, { BiometryType } from 'react-native-biometrics';

const ReactNativeBiometricsInstance = new ReactNativeBiometrics();

const checkSensorAvailability = async () => {
  try {
    return await ReactNativeBiometricsInstance.isSensorAvailable();
  } catch (error) {
    console.error('Error checking biometrics availability:', error);
    return { available: false, biometryType: undefined };
  }
};

const promptBiometrics = async (unlockMethod: BiometryType) => {
  try {
    const result = await ReactNativeBiometricsInstance.simplePrompt({
      promptMessage: `Activar ${
        unlockMethod === 'FaceID' ? 'FaceID' : 'huella'
      }`,
    });
    return result.success;
  } catch (error) {
    return false;
  }
};

export { checkSensorAvailability, promptBiometrics };
