export interface UserLoansWrapResponse {
  error: boolean;
  data: UserLoansResponse;
}

export interface UserLoanWrapResponse {
  error: boolean;
  data: UserLoan;
}

export interface UserLoansResponse {
  current: UserLoan[];
  finished: UserLoan[];
}

export interface UserLoan {
  _id: string;
  userId: string;
  userInfo: UserInfo;
  loanType: string;
  loanId: string;
  loanStatus: string;
  requestAmount: number;
  totalPayment: number;
  totalPaymentStr: string;
  totalInstallments: number;
  pendingInstallments: number;
  expired: number;
  duesPaid: number;
  firstPaymentDate: Date;
  nextPayment: NextPayment;
  finishDate: string;
  nextPaymentOrder: number;
  tna: string;
  tea: string;
  ctf: string;
  installments: Installment[];
  createdAt: Date;
  updatedAt: Date;
  cuoteAmount: number;
}

export interface Installment {
  _id: string;
  loanId: string;
  quotaId: string;
  amount: string;
  status: string;
  estimatedPaymentDate: Date;
  completedPaymentDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface UserInfo {
  email: string;
  name: string;
}

interface NextPayment {
  date: Date;
  index: number;
}
