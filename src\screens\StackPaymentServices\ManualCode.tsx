import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, TextBase } from '../../components/atoms';
import { InputWithLabel } from '../../components/atoms/InputWithLabel';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const ManualCodeScreen = ({ navigation }: Props) => {
  const [code, setCode] = useState('');
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <TextBase style={styles.textInput} size="xxl" type="Bold">
          Ingresa el código
        </TextBase>
        <InputWithLabel
          label="Código de barras"
          value={code}
          onChange={setCode}
          placeholder=" Completa el código de barras"
        />
      </View>
      <View style={styles.buttons}>
        <Button
          text="Continuar"
          onPress={() => {
            navigation.navigate('ScannerPayment');
          }}
          disabled={code === ''}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: color.WHITE,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  buttons: {
    marginBottom: 16,
  },
  textInput: {
    marginBottom: 20,
  },
});
