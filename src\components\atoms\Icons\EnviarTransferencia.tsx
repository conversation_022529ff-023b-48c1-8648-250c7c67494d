import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const EnviarTransferencia: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width="32" height="32" viewBox="0 0 32 32" fill="none">
      <Rect width="32" height="32" rx="8" fill="#F0F6FF" />
      <Path
        d="M13.5 10.1667V11.8333H18.9916L9.33331 21.4917L10.5083 22.6667L20.1666 13.0083V18.5H21.8333V10.1667H13.5Z"
        fill="#0068FF"
      />
    </Svg>
  );
};

export default EnviarTransferencia;
