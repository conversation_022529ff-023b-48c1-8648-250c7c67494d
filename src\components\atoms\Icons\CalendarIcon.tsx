import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const CalendarIcon: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M15.8333 2.50004H15V0.833374H13.3333V2.50004H6.66667V0.833374H5V2.50004H4.16667C3.24167 2.50004 2.5 3.25004 2.5 4.16671V15.8334C2.5 16.75 3.24167 17.5 4.16667 17.5H15.8333C16.75 17.5 17.5 16.75 17.5 15.8334V4.16671C17.5 3.25004 16.75 2.50004 15.8333 2.50004ZM15.8333 15.8334H4.16667V7.50004H15.8333V15.8334ZM15.8333 5.83337H4.16667V4.16671H15.8333V5.83337ZM5.83333 9.16671H10V13.3334H5.83333V9.16671Z"
        fill={color}
      />
    </Svg>
  );
};

export default CalendarIcon;
