import React, { useState } from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import { Button, Text } from '../../components/atoms';
import color from '../../theme/pallets/pallet';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { InvestmentStackParams } from '../../navigation/stack-investment/types';

type Props = NativeStackScreenProps<
  InvestmentStackParams,
  'InvestmentAmountScreen'
>;

export default function InvestmentAmountScreen({ navigation }: Props) {
  const [tokens, setTokens] = useState<string>();
  const tokenValue = 500000; // Valor de cada token en ARS
  const totalInvestment = parseInt(tokens || '0', 10) * tokenValue;

  const handleNavigateToSummary = () => {
    navigation.navigate('InvestmentSummaryScreen');
  };

  const onChangeText = (text: string) => {
    // Elimina "-" o "0" al inicio del texto
    const sanitizedText = text.replace(/^[-0]+/, '');

    // Regex para validar enteros positivos
    const regex = /^[0-9]*$/;
    if (regex.test(sanitizedText)) {
      setTokens(sanitizedText);
    }
  };

  return (
    <View style={styles.container}>
      <View style={{ gap: 24 }}>
        <Text variant="B2">¿Cuánto querés invertir?</Text>
        <View style={{ gap: 8, alignItems: 'center' }}>
          <Text variant="R6" color="NEUTRALS_600">
            Ingresa el total de tokens
          </Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={styles.tokenInput}
              value={tokens}
              placeholder="0"
              placeholderTextColor={color.NEUTRALS_400}
              onChangeText={onChangeText}
              keyboardType="numeric"
              maxLength={6} // Limitar a un máximo de 6 caracteres
            />
            <Text variant="B2" align="center">
              token{parseInt(tokens || '0') > 1 && 's'}
            </Text>
          </View>

          <Text variant="R7" color="NEUTRALS_600">
            32 tokens disponibles
          </Text>
        </View>
        <View style={{ alignItems: 'center' }}>
          <Text variant="R6" color="NEUTRALS_800">
            Total a invertir en pesos argentinos
          </Text>
          <Text variant="B2">${totalInvestment.toLocaleString('es-AR')}</Text>
          <Text variant="R7" color="NEUTRALS_600">
            Valor del token ARS {tokenValue.toLocaleString('es-AR')}
          </Text>
        </View>
      </View>
      <View
        style={{
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          borderWidth: 1,
          borderColor: color.NEUTRALS_200,
          padding: 16,
          marginHorizontal: -16,
          gap: 16,
        }}
      >
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <View>
            <Text variant="B5" color="PRIMARY_500">
              ARS {(totalInvestment * 4.9).toLocaleString('es-AR')}
            </Text>
            <Text variant="R7">Ganancias estimadas</Text>
          </View>
          <View>
            <Text variant="B5" align="right" color="PRIMARY_500">
              4,2 - 4,9%
            </Text>
            <Text variant="R7">Rendimiento mensual</Text>
          </View>
        </View>
        <Button
          text="Continuar"
          onPress={handleNavigateToSummary}
          disabled={Number(tokens) > 0 ? false : true}
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  backButton: {
    marginBottom: 20,
  },
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
    borderBottomWidth: 2,
    borderBottomColor: color.PRIMARY_500,
    width: '80%',
  },
  tokenInput: {
    fontSize: 30,
    fontFamily: 'Satoshi-Bold',
    textAlign: 'auto',
    color: color.BLACK,
  },
  divider: {
    height: 1,
    backgroundColor: color.PRIMARY_500,
    marginVertical: 10,
    alignSelf: 'center',
    width: '50%',
  },
  footer: {
    alignItems: 'center',
    marginTop: 'auto',
  },
  continueButton: {
    width: '100%',
    paddingVertical: 15,
    borderRadius: 5,
    alignItems: 'center',
  },
  activeButton: {
    backgroundColor: color.PRIMARY_500,
  },
  disabledButton: {
    backgroundColor: '#F0F0F0',
  },
});
