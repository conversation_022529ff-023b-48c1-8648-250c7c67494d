import React, { useRef } from 'react';
import {
  Animated,
  PanResponder,
  StyleSheet,
  View,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Text } from '../../../atoms';
import color from '../../../../theme/pallets/pallet';
import DoubleArrowRight from '../../../atoms/Icons/DoubleArrowRight';

const SCREEN_WIDTH = Dimensions.get('window').width;
const TRACK_WIDTH = SCREEN_WIDTH - 40;
const THUMB_SIZE = 50;
const TRIGGER_THRESHOLD = TRACK_WIDTH - THUMB_SIZE;

export default function SwipeButton({
  onSwipeComplete,
  isLoading,
}: {
  onSwipeComplete: () => void;
  isLoading: boolean;
}) {
  const translateX = useRef(new Animated.Value(0)).current;

  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => gestureState.dx > 5,
      onPanResponderMove: (_, gesture) => {
        if (gesture.dx >= 0 && gesture.dx <= TRIGGER_THRESHOLD) {
          translateX.setValue(gesture.dx);
        }
      },
      onPanResponderRelease: (_, gesture) => {
        if (gesture.dx > TRIGGER_THRESHOLD - 10) {
          onSwipeComplete();
          Animated.timing(translateX, {
            toValue: 0,
            duration: 300,
            useNativeDriver: false,
          }).start();
        } else {
          Animated.spring(translateX, {
            toValue: 0,
            useNativeDriver: false,
          }).start();
        }
      },
    }),
  ).current;

  const fillWidth = translateX.interpolate({
    inputRange: [0, TRIGGER_THRESHOLD],
    outputRange: [THUMB_SIZE, TRACK_WIDTH],
    extrapolate: 'clamp',
  });

  return (
    <View style={styles.container}>
      <View
        style={[
          styles.track,
          isLoading && { backgroundColor: color.PRIMARY_500 },
        ]}
      >
        {!isLoading && (
          <Animated.View style={[styles.fill, { width: fillWidth }]} />
        )}
        {isLoading ? (
          <ActivityIndicator style={styles.spinner} color="#fff" />
        ) : (
          <Text variant="B6" style={styles.label} color="WHITE">
            Desliza para confirmar
          </Text>
        )}
        {!isLoading && (
          <Animated.View
            style={[styles.thumb, { transform: [{ translateX }] }]}
            {...panResponder.panHandlers}
          >
            <DoubleArrowRight />
          </Animated.View>
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
  },
  track: {
    height: THUMB_SIZE,
    width: '100%',
    borderRadius: 12,
    backgroundColor: color.PRIMARY_700,
    overflow: 'hidden',
    justifyContent: 'center',
  },
  fill: {
    position: 'absolute',
    backgroundColor: color.PRIMARY_500,
    top: 0,
    bottom: 0,
    left: 0,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    zIndex: 1,
  },
  label: {
    textAlign: 'center',
    zIndex: 0,
  },
  thumb: {
    width: THUMB_SIZE,
    height: THUMB_SIZE,
    borderRadius: THUMB_SIZE / 2,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
    position: 'absolute',
    left: 0,
  },
  arrow: {
    fontSize: 24,
    color: '#fff',
  },
  spinner: {
    alignSelf: 'center',
    zIndex: 0,
  },
});
