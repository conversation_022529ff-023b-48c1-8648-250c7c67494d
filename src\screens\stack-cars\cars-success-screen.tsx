import React, { useContext } from 'react';
import { View, StyleSheet, Pressable, ScrollView } from 'react-native';
import { Button, Text } from '../../components/atoms';
import { colors } from '../../constants/colors';
import SuccessImage from '../../components/atoms/Icons/SuccessImage';
import FlagError from '../../components/atoms/Icons/FlagError';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { formatCurrency } from '../../helpers/formatCurrency';
import CopyIcon from '../../components/atoms/Icons/CopyIcon';
import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { AuthContext } from '../../context/AuthContext';
import { useCopyCustomToast } from '../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';
import color from '../../theme/pallets/pallet';
import { CarsStackParams } from '../../navigation/stack-cars/types';

type DetailProps = {
  title: string;
  value: string;
  icon?: JSX.Element;
};

const Detail = ({ title, value, icon }: DetailProps) => {
  const { showToast, toastConfig } = useCopyCustomToast();

  useDisableBackButton();
  return (
    <View style={styles.containerDetail}>
      <Text
        style={{
          fontFamily: 'Satoshi-Regular',
          fontSize: 12,
          color: '#78838D',
        }}
      >
        {title}
      </Text>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Text
          style={{
            fontFamily: 'Satoshi-Bold',
            fontSize: 12,
            color: color.NEUTRALS_800,
          }}
        >
          {value}
        </Text>
        {icon && (
          <Pressable onPress={() => showToast(value.toString())}>
            {icon}
          </Pressable>
        )}
      </View>
      <Toast config={toastConfig} />
    </View>
  );
};
type Props = NativeStackScreenProps<CarsStackParams, 'CarsSuccessScreen'>;

export default function CarsSuccessScreen({ navigation }: Props) {
  const { enableScreenLock } = useContext(AuthContext);
  enableScreenLock();

  const detalles = [
    {
      title: 'Venta de',
      value: '4 tokens',
    },
    {
      title: 'Inporte acreditado',
      value: '$ 5.000.000',
    },
    {
      title: 'Nº de orden',
      value: '213123123213231233',
      icon: <CopyIcon size={24} color={color.PRIMARY_500} />,
    },
  ];

  return (
    <KeyboardAvoidingComponent>
      <ScrollView contentContainerStyle={styles.container}>
        <View>
          <View style={styles.itemsCenter}>
            <SuccessImage width={135} height={104} />
            <Text style={styles.title}>¡Listo!</Text>
            <Text variant="R6" align="center" color="NEUTRALS_800">
              Se completó la transacción, ya tenés el dinero disponible.{' '}
            </Text>
          </View>
          <View style={[styles.gap8, styles.mt35]}>
            <Text style={styles.detalleTxt}>Detalle de pago</Text>

            {detalles.map(detalle => (
              <Detail
                title={detalle.title}
                value={detalle.value}
                key={detalle.value}
                icon={detalle.icon}
              />
            ))}
            <View style={styles.errorContainer}>
              <FlagError width={20} height={20} color={colors.negative} />
              <Text variant="B6" color="PRIMARY_700">
                Reportar un problema
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.gap8}>
          <Button
            text="Ir al inicio"
            onPress={() => {
              navigation.navigate('Home');
            }}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
    marginTop: 27,
  },
  itemsCenter: {
    alignItems: 'center',
  },
  mt35: {
    marginTop: 35,
  },
  gap8: {
    gap: 8,
  },
  error: {
    color: colors.negative,
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    marginLeft: 2,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  detalleTxt: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
  subTxt: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
  title: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 18,
    color: color.TEXT_PRIMARY,
    marginTop: 16,
    marginBottom: 6,
  },
  containerDetail: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    borderRadius: 8,
  },
});
