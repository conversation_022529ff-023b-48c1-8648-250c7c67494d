import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  modalView: {
    backgroundColor: 'white',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    padding: 16,
    marginTop: 'auto',
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
  },
  containerAccount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  account: {
    flex: 1,
    marginHorizontal: 12,
    marginVertical: 6,
  },
  name: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 16,
    color: color.TEXT_PRIMARY,
    textTransform: 'capitalize',
  },
  data: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 16,
    color: '#78838D',
  },
  close: {
    alignSelf: 'flex-start',
    marginLeft: 'auto',
  },
  textClose: {
    color: color.PRIMARY_700,
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
  },
  mt24: {
    marginVertical: 24,
  },
  containerItem: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    paddingHorizontal: 12,
    paddingVertical: 16,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconAccount: {
    marginRight: 10,
  },
  label: {
    flex: 1,
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 10,
  },
  value: {
    fontSize: 16,
  },
  flexRow: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  circle: {
    backgroundColor: color.NEUTRALS_100,
    width: 46,
    height: 46,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  plus: {
    fontSize: 16,
    color: color.NEUTRALS_800,
  },
  options: {
    marginLeft: 10,
  },
});
