import React from 'react';
import { ImageBackground, StyleSheet, View } from 'react-native';
import { TextBase } from '../../atoms';
import color from '../../../theme/pallets/pallet';

type Props = {
  cardNumber: string;
  cardHolder: string;
};
export default function CreditCard({ cardNumber, cardHolder }: Props) {
  return (
    <View style={styles.cardContainer}>
      <ImageBackground
        source={require('../../../assets/ilustrations/credit-card/card-gollet-hd.png')}
        style={styles.imageBackground}
        resizeMode="cover"
      >
        <View style={styles.cardContent}>
          <View>
            <TextBase type="Regular" size="s" color={color.WHITE}>
              Disponible
            </TextBase>
            <TextBase type="Bold" size="xxl" color={color.WHITE}>
              $1,000.00
            </TextBase>
          </View>
          <View>
            <TextBase type="Bold" size="m" color={color.WHITE}>
              {cardNumber}
            </TextBase>
            <TextBase type="Regular" size="s" color={color.WHITE}>
              {cardHolder}
            </TextBase>
          </View>
        </View>
      </ImageBackground>
    </View>
  );
}

const styles = StyleSheet.create({
  cardContainer: {
    width: 380,
    height: 280,
    borderRadius: 10,
    overflow: 'hidden',
  },
  imageBackground: {
    flex: 1,
  },
  cardContent: {
    flex: 1,
    justifyContent: 'flex-end',
    margin: 30,
    gap: 24,
  },
});
