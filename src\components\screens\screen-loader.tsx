import React from 'react';
import { ActivityIndicator, Dimensions, StyleSheet, View } from 'react-native';

import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import { colors } from '../../constants/colors';
import color from '../../theme/pallets/pallet';

const { height, width } = Dimensions.get('window');

export default function ScreenLoader() {
  useDisableBackButton();

  return (
    <View style={styles.loader}>
      <ActivityIndicator size={64} color={colors.primary} />
    </View>
  );
}

const styles = StyleSheet.create({
  loader: {

    height,
    width,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    zIndex: 1000,
    backgroundColor: color.WHITE,
    flex: 1,
  },
});
