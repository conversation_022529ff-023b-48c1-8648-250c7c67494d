import React, { useContext } from 'react';
import { View, StyleSheet, Pressable, ScrollView } from 'react-native';
import { Button, Text } from '../../components/atoms';
import { colors } from '../../constants/colors';
import SuccessImage from '../../components/atoms/Icons/SuccessImage';
import FlagError from '../../components/atoms/Icons/FlagError';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { formatCurrency } from '../../helpers/formatCurrency';
import CopyIcon from '../../components/atoms/Icons/CopyIcon';
import useDisableBackButton from '../../hooks/utils/useDisableBackButton';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { useUserContext } from '../../context/UserContext';
import { AuthContext } from '../../context/AuthContext';
import { useCopyCustomToast } from '../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';
import color from '../../theme/pallets/pallet';

type DetailProps = {
  title: string;
  value: string;
  icon?: JSX.Element;
};

const Detail = ({ title, value, icon }: DetailProps) => {
  const { showToast, toastConfig } = useCopyCustomToast();

  useDisableBackButton();
  return (
    <View style={styles.containerDetail}>
      <Text variant="R7" color="NEUTRALS_600">
        {title}
      </Text>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Text variant="B7" color="NEUTRALS_800">
          {value}
        </Text>
        {icon && (
          <Pressable onPress={() => showToast(value.toString())}>
            {icon}
          </Pressable>
        )}
      </View>
      <Toast config={toastConfig} />
    </View>
  );
};
type Props = NativeStackScreenProps<any>;

export const SuccessScreen = ({ navigation, route }: Props) => {
  const { enableScreenLock } = useContext(AuthContext);
  enableScreenLock();
  const { amount, nombre, motive, numTransaccion, cuit, cvu }: any =
    route.params;
  const { user } = useUserContext();
  const detalles = [
    {
      title: 'Contacto',
      value: nombre,
    },
    {
      title: 'Importe',
      value: '$' + formatCurrency(Number(amount)),
    },
    {
      title: 'Motivo',
      value: motive,
    },
    {
      title: 'Nº transacción',
      value: numTransaccion,
      icon: <CopyIcon size={24} color={color.PRIMARY_500} />,
    },
  ];

  const handleViewReceipt = () => {
    navigation.navigate('ReceiptScreen', {
      operationId: numTransaccion,
      // date: new Date(),
      amount: Number(amount),
      motive: motive,
      from: user.nombre + ' ' + user.apellido,
      fromCuit: user.cuit,
      fromCVU: user.cvu[0].cvu,
      toName: nombre,
      toCuit: cuit,
      toCvu: cvu,
    });
  };

  return (
    <KeyboardAvoidingComponent>
      <ScrollView contentContainerStyle={styles.container}>
        <View>
          <View style={styles.itemsCenter}>
            <SuccessImage width={135} height={104} />
            <Text variant="B5">¡Transferencia exitosa!</Text>
            <Text variant="R6" color="NEUTRALS_800">
              La transferencia se ha realizado correctamente.
            </Text>
          </View>
          <View style={[styles.gap8, styles.mt35]}>
            <Text variant="B6">Detalle de la transferencia</Text>

            {detalles.map(detalle => (
              <Detail
                title={detalle.title}
                value={detalle.value}
                key={detalle.value}
                icon={detalle.icon}
              />
            ))}
            <View style={styles.errorContainer}>
              <FlagError width={20} height={20} color={color.PRIMARY_700} />
              <Text variant="B6" color="PRIMARY_700">
                Reportar un problema
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.gap8}>
          {/* <ButtonOutline text="Ver comprobante" onPress={handleViewReceipt} /> */}
          <Button
            text="Ir al inicio"
            onPress={() => {
              navigation.navigate('Home');
            }}
          />
        </View>
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    justifyContent: 'space-between',
    marginTop: 27,
  },
  itemsCenter: {
    alignItems: 'center',
  },
  mt35: {
    marginTop: 35,
  },
  gap8: {
    gap: 8,
  },
  error: {
    color: colors.negative,
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    marginLeft: 2,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  detalleTxt: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
  subTxt: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
  title: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 18,
    color: color.TEXT_PRIMARY,
    marginTop: 16,
    marginBottom: 6,
  },
  containerDetail: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    borderRadius: 8,
  },
});
