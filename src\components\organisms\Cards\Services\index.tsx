import React, { useState } from 'react';
import { View, FlatList, TouchableOpacity } from 'react-native';
import { TextBase } from '../../../atoms';
import { Account } from '../../../../types/Account';
import { useNavigation } from '@react-navigation/native';
import { LoaderModal } from '../../Modals/Loader';
import { styles } from './styles';
import { Skeleton } from '../../../atoms/Skeleton';
import ChevronIcon from '../../../atoms/Icons/Chevron';
import BankGo from '../../../atoms/Icons/BankGo';
import color from '../../../../theme/pallets/pallet';

const renderItem = ({ item }: any) => {
  return (
    <View style={styles.containerContact}>
      <TouchableOpacity onPress={() => {}}>
        <View style={styles.leftContent}>
          <View style={styles.circle}>
            <TextBase style={styles.plus}>
              <BankGo />
            </TextBase>
          </View>
          <View>
            <TextBase
              numberOfLines={1}
              ellipsizeMode="tail"
              style={styles.name}
              color={color.TEXT_PRIMARY}
              type="Bold"
              size="xl"
            >
              {item.item.servicio}
            </TextBase>
            <TextBase style={styles.name} type="Regular" color={'#78838D'}>
              {item.item.num_service}
            </TextBase>
          </View>
        </View>
      </TouchableOpacity>
      <View style={styles.flexRow}>
        <ChevronIcon />
      </View>
    </View>
  );
};

const ServicesCard = () => {
  const navigation: any = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const data = [
    {
      id: '1',
      servicio: 'Internet',
      monto: 137.591,
      fecha_vencimiento: '11/4',
      num_service: '**********',
    },
    {
      id: '2',
      servicio: 'Electricidad',
      monto: 7.846,
      fecha_vencimiento: '15/4',
      num_service: '*************',
    },
    {
      id: '3',
      servicio: 'Agua',
      monto: 7.846,
      fecha_vencimiento: '20/4',
      num_service: '****************',
    },
  ];

  const navigateToAmount = (accountDetails: Account[]) => {
    const selectedAccountData = accountDetails[0];
    setModalVisible(false);
    navigation.navigate('StackTransfer', {
      screen: 'AmountScreen',
      params: {
        nombre: selectedAccountData?.titulares[0].nombre,
        banco: '',
        cuit: selectedAccountData?.titulares[0].cuit,
        cvu: selectedAccountData?.cvu,
      },
    });
  };

  return (
    <>
      <LoaderModal modalVisible={loading} />

      <>
        <View style={styles.container}>
          <TextBase size="xl" color={color.NEUTRALS_800} style={styles.txt}>
            Servicios adheridos
          </TextBase>
          {loading ? (
            <>
              <Skeleton
                width={400}
                backgroundColor="#F7F8FE"
                highlightColor="#e8e9ed"
                height={40}
              />
            </>
          ) : (
            <FlatList
              data={data}
              renderItem={item =>
                renderItem({
                  item,
                  navigateToAmount,
                })
              }
              keyExtractor={item => item.id}
            />
          )}
        </View>
      </>
    </>
  );
};

export default ServicesCard;
