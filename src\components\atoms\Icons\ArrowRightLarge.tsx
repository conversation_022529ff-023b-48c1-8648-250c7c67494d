import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const ArrowRightLarge: FC<IconProps> = ({ size, color }) => {
    return (
        <Svg width={size} height={size} viewBox="0 0 8 8">
            <Path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M0 3.99966C0 3.86705 0.0526785 3.73987 0.146447 3.6461C0.240215 3.55233 0.367392 3.49966 0.5 3.49966H6.293L4.146 1.35366C4.05211 1.25977 3.99937 1.13243 3.99937 0.999655C3.99937 0.866879 4.05211 0.739542 4.146 0.645655C4.23989 0.551769 4.36722 0.499023 4.5 0.499023C4.63278 0.499023 4.76011 0.551769 4.854 0.645655L7.854 3.64566C7.90056 3.6921 7.93751 3.74728 7.96271 3.80802C7.98792 3.86877 8.00089 3.93389 8.00089 3.99966C8.00089 4.06542 7.98792 4.13054 7.96271 4.19129C7.93751 4.25203 7.90056 4.30721 7.854 4.35366L4.854 7.35366C4.76011 7.44754 4.63278 7.50029 4.5 7.50029C4.36722 7.50029 4.23989 7.44754 4.146 7.35366C4.05211 7.25977 3.99937 7.13243 3.99937 6.99966C3.99937 6.86688 4.05211 6.73954 4.146 6.64566L6.293 4.49966H0.5C0.367392 4.49966 0.240215 4.44698 0.146447 4.35321C0.0526785 4.25944 0 4.13226 0 3.99966Z"
                fill={color}
            />
        </Svg>
    );
};

export default ArrowRightLarge;
