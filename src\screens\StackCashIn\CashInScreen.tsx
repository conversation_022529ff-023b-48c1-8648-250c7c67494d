import React, { useCallback, useState } from 'react';
import { View, StyleSheet, Pressable, Share } from 'react-native';
import HeaderTransfer from '../../components/organisms/Headers/Transfer';
import { Button, Text } from '../../components/atoms';
import color from '../../theme/pallets/pallet';
import { useUserContext } from '../../context/UserContext';
import CopyIcon from '../../components/atoms/Icons/CopyIcon';
import { useCopyCustomToast } from '../../hooks/useCopyCustomToast';
import Toast from 'react-native-toast-message';

export const CashInScreen = () => {
  const { user } = useUserContext();
  console.log(user);
  const [activeTab, setActiveTab] = useState<number>(0);

  const tabs = ['Pesos (AR$)', 'Dólares (US$)'];
  const handleTabPress = (index: number) => {
    setActiveTab(index);
  };

  const { showToast, toastConfig } = useCopyCustomToast();
  const handleShareData = useCallback(async () => {
    const textToShare = `${user?.nombre}\nCVU:${user.cvu[0].cvu}\nAlias:${user.cvu[0].cvuAlias}\nCUIT/CUIL:${user.cuit}\nBanco coinag`;
    await Share.share({ message: textToShare });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <View style={styles.container}>
      <HeaderTransfer text="Ingresar dinero" />
      <View style={{ paddingVertical: 24 }}>
        <View style={styles.scrollContainer}>
          {tabs.map((tab, index) => (
            <Pressable
              key={index}
              onPress={() => handleTabPress(index)}
              style={[
                styles.tabButton,
                activeTab === index && styles.underline,
              ]}
            >
              <Text
                variant={activeTab === index ? 'B6' : 'R6'}
                color={activeTab === index ? 'PRIMARY_700' : 'NEUTRALS_800'}
              >
                {tab}
              </Text>
            </Pressable>
          ))}
        </View>
      </View>
      <View>
        <Text variant="R6">
          Para ingresar y recibir dinero desde cuentas bancarias o digitales
        </Text>
        <View
          style={{
            padding: 16,
            backgroundColor: color.NEUTRALS_50,
            borderWidth: 1,
            borderColor: color.NEUTRALS_100,
            borderRadius: 12,
            gap: 8,
            marginVertical: 16,
          }}
        >
          <Text variant="B5">{user?.nombre}</Text>
          <Text variant="R5">Banco coinag</Text>
          <Text variant="R5">Cuenta corriente</Text>
          <View
            style={{ flexDirection: 'row', justifyContent: 'space-between' }}
          >
            <Pressable
              onPress={() => showToast(user.cvu[0].cvu)}
              style={{ flexDirection: 'row', gap: 8 }}
            >
              <Text variant="R5">CVU: {user.cvu[0].cvu} </Text>
              <CopyIcon size={24} color={color.PRIMARY_700} />
            </Pressable>
          </View>
          <View
            style={{ flexDirection: 'row', justifyContent: 'space-between' }}
          >
            <Pressable
              onPress={() => showToast(user.cvu[0].cvuAlias)}
              style={{ flexDirection: 'row', gap: 8 }}
            >
              <Text variant="R5">Alias: {user.cvu[0].cvuAlias}</Text>
              <CopyIcon size={24} color={color.PRIMARY_700} />
            </Pressable>
          </View>
          <View style={{ marginTop: 16 }}>
            <Button onPress={() => handleShareData()} text="Compartir datos" />
          </View>
          <Toast config={toastConfig} />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    justifyContent: 'space-between',
  },
  scrollContainer: {
    flexDirection: 'row',
  },
  tabButton: {
    padding: 11,
    alignItems: 'center',
    width: '50%',
  },
  activeTabText: {
    color: '#D50000',
    fontWeight: 'bold',
  },
  underline: {
    borderBottomWidth: 2,
    borderBottomColor: color.PRIMARY_700,
  },
});
