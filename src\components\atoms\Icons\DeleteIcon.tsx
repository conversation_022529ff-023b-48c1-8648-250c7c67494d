import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const DeleteIcon: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 20 20">
      <Path
        d="M13.3333 7.5V15.8333H6.66666V7.5H13.3333ZM12.0833 2.5H7.91666L7.08332 3.33333H4.16666V5H15.8333V3.33333H12.9167L12.0833 2.5ZM15 5.83333H4.99999V15.8333C4.99999 16.75 5.74999 17.5 6.66666 17.5H13.3333C14.25 17.5 15 16.75 15 15.8333V5.83333Z"
        fill={color}
      />
    </Svg>
  );
};

export default DeleteIcon;
