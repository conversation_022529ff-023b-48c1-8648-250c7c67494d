import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const LogoButton: FC<IconProps> = ({ width, height }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 33 32" fill="none">
      <Path
        d="M25.1702 5.80406L22.5607 8.41381L16.1899 15.8963L16.6685 16.3746L24.1555 10.0002L26.7608 7.39463L25.1702 5.80406Z"
        fill="white"
      />
      <Path
        d="M16.3091 15.8914L16.6735 16.2557L24.0989 9.9334L26.6372 7.39463L25.1702 5.92742L22.6225 8.47562L16.3091 15.8914ZM16.6638 16.4935L16.0713 15.901L22.4942 8.35697L25.1702 5.6802L26.8846 7.39463L24.2173 10.0617L16.6638 16.4935Z"
        fill="white"
      />
      <Path
        d="M10.4393 23.7158L16.8098 16.2333L16.3315 15.7547L8.84455 22.1294L6.23926 24.735L7.82983 26.3256L10.4393 23.7158Z"
        fill="white"
      />
      <Path
        d="M6.3632 24.735L7.83016 26.2019L10.3754 23.6565L16.6913 16.238L16.3269 15.8736L8.90171 22.1962L6.3632 24.735ZM7.83016 26.4494L6.11572 24.735L8.78306 22.0676L16.3366 15.6358L16.9293 16.2283L10.5015 23.7778L7.83016 26.4494Z"
        fill="white"
      />
      <Path
        d="M26.7608 24.735L24.151 22.1255L16.6685 15.7547L16.1899 16.2333L22.5647 23.7203L25.1702 26.3256L26.7608 24.735Z"
        fill="white"
      />
      <Path
        d="M16.3091 16.238L22.6314 23.6634L25.1702 26.2019L26.6372 24.735L24.0894 22.1873L16.6735 15.8736L16.3091 16.238ZM25.1702 26.4494L22.5028 23.7821L16.0713 16.2283L16.6638 15.6358L24.2078 22.0589L26.8846 24.735L25.1702 26.4494Z"
        fill="white"
      />
      <Path
        d="M8.84901 10.0041L16.3315 16.3746L16.8101 15.8961L10.4354 8.40934L7.82983 5.80406L6.23926 7.39463L8.84901 10.0041Z"
        fill="white"
      />
      <Path
        d="M8.90866 9.93985L16.3271 16.2557L16.6915 15.8916L10.3689 8.46619L7.83016 5.92767L6.3632 7.39463L8.90866 9.93985ZM16.3366 16.4935L8.78753 10.0659L6.11572 7.39463L7.83016 5.6802L10.4975 8.34754L16.9293 15.901L16.3366 16.4935Z"
        fill="white"
      />
      <Path
        d="M15.3755 2.67841L15.3757 6.36915L16.1618 16.1648H16.8385L17.6251 6.36294V2.67841H15.3755Z"
        fill="white"
      />
      <Path
        d="M16.2422 16.0774H16.7573L17.5374 6.35602L17.5372 2.76605H15.4626L15.4628 6.36917L16.2422 16.0774ZM16.9191 16.2524H16.0809L15.2881 6.37637L15.2876 2.59106H17.7122V6.36322L16.9191 16.2524Z"
        fill="white"
      />
      <Path
        d="M17.6248 25.7604L16.8385 15.9648H16.1618L15.3755 25.7664V29.4509H17.6251L17.6248 25.7604Z"
        fill="white"
      />
      <Path
        d="M15.4631 29.3634H17.5377L17.5374 25.764L16.7578 16.0523H16.2427L15.4628 25.7734L15.4631 29.3634ZM17.7127 29.5384H15.2881V25.7662L16.0811 15.8773H16.9194L16.9258 15.9577L17.7124 25.7603L17.7127 29.5384Z"
        fill="white"
      />
      <Path
        d="M29.8863 14.9399L26.1955 14.9401L16.3999 15.7262V16.4031L26.2018 17.1895H29.8863V14.9399Z"
        fill="white"
      />
      <Path
        d="M16.4875 16.3224L26.2087 17.1025L29.7986 17.102V15.0274L26.1955 15.0277L16.4875 15.8071V16.3224ZM29.9736 17.277H26.2017L16.3125 16.4837V15.6458L26.1886 14.8529L29.9736 14.8527V17.277Z"
        fill="white"
      />
      <Path
        d="M6.80451 17.1892L16.6002 16.4031V15.7262L6.79855 14.9399H3.11377V17.1895L6.80451 17.1892Z"
        fill="white"
      />
      <Path
        d="M3.20136 15.0274V17.102L6.80101 17.1018L16.5125 16.3224V15.8071L6.79157 15.0272L3.20136 15.0274ZM3.02637 17.277V14.8525H6.79852L16.6875 15.6458V16.4837L6.80448 17.277H3.02637Z"
        fill="white"
      />
    </Svg>
  );
};

export default LogoButton;
