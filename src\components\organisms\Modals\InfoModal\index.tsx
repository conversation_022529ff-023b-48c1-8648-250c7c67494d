import React from 'react';
import { Modal, View, TouchableWithoutFeedback } from 'react-native';
import { styles } from './styles';
import { Button, TextBase } from '../../../atoms';
import WarningIcon from '../../../atoms/Icons/Warning';
import { useNavigation } from '@react-navigation/native';
import color from '../../../../theme/pallets/pallet';

type Props = {
  modalVisible: boolean;
  textVisible?: boolean;
  text?: string;
  setErrorCancel?: any;
};

export const InfoModal = ({ modalVisible, setErrorCancel }: Props) => {
  const navigation: any = useNavigation();

  const goTransfer = () => {
    setErrorCancel(false);
    navigation.navigate('StackTransfer', {
      screen: 'TransferScreen',
    });
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => setErrorCancel(false)}
    >
      <TouchableWithoutFeedback onPress={() => setErrorCancel(false)}>
        <View style={styles.modal}>
          <View style={styles.indicator}>
            <WarningIcon />
            <TextBase color={color.NEUTRALS_800} type="Bold" size="l">
              Aún tienes dinero en tu billetera
            </TextBase>
            <TextBase color={color.NEUTRALS_800}>
              Por favor, retiralo de tu cuenta mediante una transferencia y
              intentarlo.
            </TextBase>
            <Button onPress={goTransfer} text="Ir a transferir" />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};
