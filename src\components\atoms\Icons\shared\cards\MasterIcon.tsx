import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from '../../types';

const VisaIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height="40" viewBox="0 0 64 40" fill="none">
      <Path d="M40.635 4.98621H23.3154V35.8064H40.635V4.98621Z" fill="#FF5A00" />
      <Path d="M24.4678 20.3964C24.4678 14.1346 27.4398 8.57708 32.0002 4.98625C28.6439 2.37244 24.4101 0.792725 19.792 0.792725C8.85195 0.792725 0 9.56044 0 20.3964C0 31.2323 8.85195 40 19.792 40C24.4101 40 28.6439 38.4203 32.0002 35.8065C27.4334 32.2664 24.4678 26.6581 24.4678 20.3964Z" fill="#EB001B" />
      <Path d="M64 20.3964C64 31.2323 55.1481 40 44.208 40C39.5899 40 35.3561 38.4203 31.9998 35.8065C36.6179 32.2093 39.5322 26.6581 39.5322 20.3964C39.5322 14.1346 36.5602 8.57708 31.9998 4.98625C35.3497 2.37244 39.5835 0.792725 44.2016 0.792725C55.1481 0.792725 64 9.61753 64 20.3964Z" fill="#F79E1B" />
    </Svg>
  );
};

export default VisaIcon;
