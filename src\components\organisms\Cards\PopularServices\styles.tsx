import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  containerContact: {
    borderColor: color.NEUTRALS_100,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#EAEAEA',
  },
  image: {
    width: 34,
    height: 34,
    marginRight: 16,
  },
  txt: {
    fontFamily: 'Satoshi-Bold',
    marginVertical: 16,
  },
  leftContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  circle: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  plus: {
    color: '#858585',
    fontSize: 16,
  },
  name: {
    marginLeft: 10,
    maxWidth: 200,
    textTransform: 'capitalize',
  },
  cuenta: {
    fontSize: 14,
    marginLeft: 10,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  search: {
    marginBottom: 16,
  },
  star: {
    marginRight: 16,
  },
});
