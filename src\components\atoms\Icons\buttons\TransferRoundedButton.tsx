import React, { FC } from 'react';
import Svg, { Circle, Path } from 'react-native-svg';
import { IconProps } from '../types';

const TransferRoundedButton: FC<IconProps> = ({ size = 24 }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 48 48" fill="none">
      <Circle cx="24" cy="24" r="24" fill="#FFE7E3" />

      <Path
        d="M31.375 27.103C32.0239 25.5613 32.1732 23.855 31.8021 22.224C31.431 20.593 30.558 19.1194 29.3059 18.0102C28.0539 16.9011 26.4857 16.2123 24.8218 16.0405C23.158 15.8688 21.4822 16.2229 20.03 17.053L19.038 15.316C20.5556 14.4486 22.2742 13.9944 24.0222 13.9986C25.7702 14.0028 27.4866 14.4653 29 15.34C33.49 17.932 35.21 23.482 33.117 28.11L34.459 28.884L30.294 31.098L30.129 26.384L31.375 27.103ZM16.625 20.897C15.9762 22.4387 15.8268 24.145 16.1979 25.776C16.5691 27.4069 17.442 28.8806 18.6941 29.9897C19.9461 31.0989 21.5143 31.7877 23.1782 31.9594C24.842 32.1311 26.5178 31.7771 27.97 30.947L28.962 32.684C27.4444 33.5514 25.7258 34.0056 23.9778 34.0014C22.2298 33.9972 20.5134 33.5347 19 32.66C14.51 30.068 12.79 24.518 14.883 19.89L13.54 19.117L17.705 16.903L17.87 21.617L16.624 20.898L16.625 20.897ZM25.415 26.828L22.584 24L19.756 26.828L18.342 25.414L22.585 21.172L25.414 24L28.243 21.172L29.657 22.586L25.414 26.828H25.415Z"
        fill="#FF0033"
      />
    </Svg>
  );
};

export default TransferRoundedButton;
