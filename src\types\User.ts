// Generated by https://quicktype.io

import { Balance } from './Balances';
import { Cards } from './StackCards/Cards';

export type UserDetailResponse = {
  error: boolean;
  data: UserDetail;
};

export type UserDetail = {
  _id: string;
  userId: string;
  email: string;
  nombre: string;
  apellido: string;
  sexo: string;
  idEntidadTipoDocumento: TipoDocumento;
  numeroDocumento: string;
  fechaNacimiento: string;
  cuit: string;
  codigoAreaTelefono: string;
  numeroTelefono: string;
  noTramite: string;
  nacionalidad: string;
  paisResidencia: string;
  calle: string;
  numero: string;
  departamento: string;
  ciudad: string;
  provincia: string;
  codigoPais: string;
  perfil: string;
  group: string;
  images: Image[];
  banksAccounts: BanksAccount[];
  cvuAccounts: CvuAccount[];
  smAccounts: SmAccount[];
  onboarding: Onboarding;
  createdAt: string;
  updatedAt: string;
  balance: Balance;
  piso: string;
  cards: Cards;
};

interface BanksAccount {
  _id: string;
  cuit: string;
  cvu: string;
  createdAt: string;
}

export interface CvuAccount {
  _id: string;
  cvuUserId: string;
  cvuAlias: string;
  cvu: string;
  createdAt: string;
}

interface Image {
  _id: string;
  imageName: string;
  imageUrl: string;
  createdAt: string;
}

interface Onboarding {
  _id: string;
  onboardingCompleted: boolean;
  onboardingStatus: string;
  onboardingStatusCode: number;
  onboardingId: string;
  createdAt: string;
  updatedAt: string;
}

interface SmAccount {
  _id: string;
  accountId: string;
  personId: string;
  unitId: string;
  createdAt: string;
}

interface TipoDocumento {
  idEntidadTipoDocumento: string;
	descripcion: string;
}