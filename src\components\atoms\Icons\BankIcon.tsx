import React, { <PERSON> } from 'react';
import Svg, { Circle, Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const BankIcon: FC<IconProps> = ({ size, color }) => {
  return (
    <Svg width="48" height="48" viewBox="0 0 48 48" fill="none">
      <Circle cx="24" cy="24" r="24" fill="#E0EDFF" />
      <Path
        d="M19 23H17V30H19V23ZM25 23H23V30H25V23ZM33.5 32H14.5V34H33.5V32ZM31 23H29V30H31V23ZM24 16.26L29.21 19H18.79L24 16.26ZM24 14L14.5 19V21H33.5V19L24 14Z"
        fill="#0068FF"
      />
    </Svg>
  );
};

export default BankIcon;

<svg
  width="48"
  height="48"
  viewBox="0 0 48 48"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
>
  <circle cx="24" cy="24" r="24" fill="#E0EDFF" />
  <path
    d="M19 23H17V30H19V23ZM25 23H23V30H25V23ZM33.5 32H14.5V34H33.5V32ZM31 23H29V30H31V23ZM24 16.26L29.21 19H18.79L24 16.26ZM24 14L14.5 19V21H33.5V19L24 14Z"
    fill="#0068FF"
  />
</svg>;
