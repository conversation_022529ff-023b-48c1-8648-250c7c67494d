import { api } from '../../services/apiService';
import { useState } from 'react';

export const usePostUser = ({ setLoading, setError, setSuccess }: any) => {
  const [successData, setSuccessData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const postUser = async (register: any) => {
    try {
      setLoading(true);
      const response = await api.post('/digital/signup/mobile', register);

      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
        setLoading(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setError(true);
      }
      setLoading(false);
    } catch (err: any) {
      console.error('Error:', err.response);
      setError(true);
      setErrorMessage(err.response.data.message);
      setLoading(false);
    }
  };

  return { postUser, successData, errorMessage };
};
