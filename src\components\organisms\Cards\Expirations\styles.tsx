import { StyleSheet } from 'react-native';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    paddingVertical: 12,
  },
  card: {
    backgroundColor: '#F7F8FE',
    borderRadius: 8,
    padding: 8,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  circleContainer: {
    alignItems: 'center',
    marginHorizontal: 8,
    gap: 5,
    paddingBottom: 10,
  },
  circleImage: {
    width: 64,
    height: 64,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: color.PRIMARY_700,
  },
  circle: {
    backgroundColor: color.NEUTRALS_100,
    width: 50,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  plus: {
    fontSize: 28,
    color: color.NEUTRALS_800,
  },
  row: {
    flexDirection: 'row',
    paddingRight: 40,
  },
  expiration: {
    flexDirection: 'column',
    marginLeft: 10,
    marginBottom: 14,
  },
});
