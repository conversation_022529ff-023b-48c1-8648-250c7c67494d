import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Keyboard,
  NativeSyntheticEvent,
  TextInputKeyPressEventData,
  TouchableWithoutFeedback,
  Text,
} from 'react-native';
import HeaderTransfer from '../../components/organisms/Headers/Transfer';
import { Button } from '../../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { isEmpty } from 'lodash';
import { InfoAccount } from '../../components/organisms/Cards/InfoAccount';
import SelectMotive from '../../components/molecules/SelectMotive';
import { formatCurrency } from '../../helpers/formatCurrency';
import { useUserContext } from '../../context/UserContext';
import { InputAmount } from '../../components/molecules/InputAmount';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const AmountScreen = ({ navigation, route }: Props) => {
  const { user } = useUserContext();

  const { nombre, banco, cuit, cvu } = route.params;

  const [amount, onChangeValue] = useState(0);
  const [motive, setMotive] = useState('Varios');
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const inputRef = useRef<TextInput>(null);
  const [keyboardOpen, setKeyboardOpen] = useState(false);

  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardOpen(true);
    });
    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardOpen(false);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  const handleAmount = useCallback(
    (value: string) => {
      onChangeValue(value);
    },
    [onChangeValue],
  );

  const handleOnPress = () => {
    setDropdownVisible(false);
    if (keyboardOpen) {
      inputRef.current?.blur();
    }
    inputRef.current?.focus();
  };

  const handleKeyPress = (
    event: NativeSyntheticEvent<TextInputKeyPressEventData>,
  ) => {
    const key = event.nativeEvent.key;

    let newValue = amount;
    const maxDecimals = 2;

    const sanitizedKey = key.replace(',', '.');
    const validKeyRegExp = /^[0-9.]$/;
    const decimalRegExp = /^\./;
    const validNumberRegExp = new RegExp(`^\\d+(\\.\\d{0,${maxDecimals}})?$`);

    if (key === 'Backspace' && newValue.length > 0) {
      newValue = newValue.slice(0, -1);
      handleAmount(newValue);
      return;
    }
    if (isEmpty(newValue) && decimalRegExp.test(newValue + sanitizedKey)) {
      newValue = '0' + sanitizedKey;
      handleAmount(newValue);
      return;
    }
    if (isEmpty(newValue) && sanitizedKey === '0') {
      newValue = sanitizedKey + '.';
      handleAmount(newValue);
      return;
    }

    if (!validKeyRegExp.test(sanitizedKey)) {
      return;
    }
    if (!validNumberRegExp.test(newValue + sanitizedKey)) {
      return;
    }

    newValue += sanitizedKey;

    handleAmount(newValue);
  };

  const keyboardDismiss = () => {
    Keyboard.dismiss();
  };

  const navigateToConfirm = () => {
    navigation.navigate('ConfirmScreen', {
      nombre,
      banco,
      amount,
      motive,
      cuit,
      cvu,
      balance: user.balance,
    });
  };

  const insufficientFunds = amount > user.balance;

  return (
    <TouchableWithoutFeedback accessible={false} onPress={keyboardDismiss}>
      <View style={styles.container}>
        <HeaderTransfer text="Transferir" />
        <View style={styles.header}>
          <InfoAccount nombre={nombre} banco={banco} />
          <Text style={styles.txt}>Ingresar importe</Text>
          <InputAmount
            amount={amount}
            handleOnPress={handleOnPress}
            inputRef={inputRef}
            handleKeyPress={handleKeyPress}
            error={insufficientFunds}
          />
          <Text
            style={[
              styles.txt,
              { color: insufficientFunds ? color.RED_700 : '#78838D' },
            ]}
          >
            Saldo disponible: ${formatCurrency(user.balance)}
          </Text>
          {/* <SelectMotive
            setMotive={setMotive}
            keyboardDismiss={keyboardDismiss}
            dropdownVisible={dropdownVisible}
            setDropdownVisible={setDropdownVisible}
          /> */}
        </View>

        <View style={styles.button}>
          <Button
            text="Continuar"
            onPress={navigateToConfirm}
            disabled={!(amount > 0 && !insufficientFunds)}
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 16,
    paddingBottom: 16,
    marginBottom: 16,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  button: {
    marginVertical: 32,
    width: '100%',
  },
  txt: {
    fontSize: 12,
    fontFamily: 'Satoshi-Regular',
    color: '#78838D',
    marginVertical: 8,
  },
});
