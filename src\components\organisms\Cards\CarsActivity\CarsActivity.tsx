import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Text } from '../../../atoms';
import color from '../../../../theme/pallets/pallet';

interface IActivity {
  name: string;
  date: string;
  moneyDif: string;
}

const activities: IActivity[] = [
  { name: 'Pago de rendimiento', date: 'Hoy', moneyDif: '+$ 1.451,04' },
  {
    name: 'Inversión en Fiat Cronos',
    date: '25/07/2025',
    moneyDif: '-$ 2.000.000',
  },
  { name: 'Pago de rendimiento', date: 'Hoy', moneyDif: '+$ 1.451,04' },
  {
    name: 'Inversión en Fiat Cronos',
    date: '25/07/2025',
    moneyDif: '-$ 2.000.000',
  },
  { name: 'Pago de rendimiento', date: 'Hoy', moneyDif: '+$ 1.451,04' },
  {
    name: 'Inversión en Fiat Cronos',
    date: '25/07/2025',
    moneyDif: '-$ 2.000.000',
  },
];

export const CarsActivity = () => {
  return (
    <View>
      <Text variant="R6">Actividad</Text>
      {activities.map((act, idx) => (
        <View id={`${idx}+${act.date}`} style={styles.activitiesContainer}>
          <View>
            <Text variant="B7">{act.name}</Text>
            <Text variant="B7" color="NEUTRALS_600">
              {act.date}
            </Text>
          </View>
          <Text
            variant="R7"
            color={act.moneyDif.startsWith('+') ? 'GREEN_500' : 'BLACK'}
          >
            {act.moneyDif}
          </Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  activitiesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    padding: 8,
    borderBottomWidth: 1,
    borderBottomColor: color.NEUTRALS_100,
  },
});
