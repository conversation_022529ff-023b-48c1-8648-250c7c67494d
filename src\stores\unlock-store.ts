import { isNil } from 'lodash';
import EncryptedStorage from 'react-native-encrypted-storage';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

import { checkSensorAvailability } from '../lib/biometrics';
import { getCredentials } from '../helpers/asyncStorage';

type Method =
  | { type: 'biometric'; biometry: 'TouchID' | 'FaceID' | 'Biometrics' }
  | { type: 'pin'; code: number };
type UnlockStore = {
  init: () => Promise<void>;
  unlockMethod: Method | null;
  setUnlockMethod: (method: Method | null) => void;
  unlockAttempts: number;
  setUnlockAttempts: (attempt: number) => void;
  isOpen: boolean;
};

const unlockStore = create(
  persist<UnlockStore>(
    (set, get) => ({
      init: async () => {
        try {
          if (get().unlockMethod === null) {
            const [credentials, { available, biometryType }] =
              await Promise.all([getCredentials(), checkSensorAvailability()]);
            if (!isNil(credentials) && available && !isNil(biometryType)) {
              get().setUnlockMethod({
                type: 'biometric',
                biometry: biometryType,
              });
            }
          }
        } catch {
          console.error('error');
        }
      },
      unlockMethod: null,
      setUnlockMethod: method => set({ unlockMethod: method }),
      unlockAttempts: 3,
      setUnlockAttempts: attempt => set({ unlockAttempts: attempt }),
      isOpen: false,
    }),
    {
      name: 'unlockStore',
      storage: createJSONStorage(() => EncryptedStorage),
    },
  ),
);

export default unlockStore;
