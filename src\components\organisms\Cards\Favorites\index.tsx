/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from 'react';
import { View, FlatList, TouchableOpacity } from 'react-native';
import { TextBase } from '../../../atoms';
import { useFavoriteContact } from '../../../../hooks/Transfers/recentTransfer';
import { styles } from './styles';
import { Account, AccountResponse } from '../../../../types/Account';
import { useNavigation } from '@react-navigation/native';
import { api } from '../../../../services/apiService';
import { LoaderModal } from '../../Modals/Loader';
import { Accounts } from '../../Modals/Accounts';
import { Skeleton } from '../../../atoms/Skeleton';
import { useTransferContext } from '../../../../context/TransferContext';

const UserCircle = ({ user, circle, validateAccount }: any) => {
  const firstLetter = user.contactName.charAt(0).toUpperCase();
  const secondLetter =
    user.contactName.split(' ')[1]?.charAt(0).toUpperCase() || '';
  const firstName = user.contactName.split(' ')[0];
  const formattedFirstName =
    firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();
  const numAccounts = user.accounts.length;
  return (
    <View style={styles.circleContainer}>
      {user.id === '0' ? (
        <>
          {!circle && (
            <>
              <View style={styles.circle}>
                <TextBase style={styles.plus}>+</TextBase>
              </View>
              <TextBase size="s">{user.contactName}</TextBase>
            </>
          )}
        </>
      ) : (
        <>
          <TouchableOpacity
            onPress={() => validateAccount(user.accounts, numAccounts === 1)}
          >
            <View style={styles.circle}>
              <TextBase style={styles.plus}>
                {firstLetter}
                {secondLetter}
              </TextBase>
            </View>
          </TouchableOpacity>
          <TextBase size="s">{formattedFirstName}</TextBase>
        </>
      )}
    </View>
  );
};

const FavoritesCard = ({ text, circle }: any) => {
  const navigation: any = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [accountData, setAccountData] = useState<Account[]>([]);
  const [loading, setLoading] = useState(false);

  const {
    successData,
    GetFavorites,
    loading: loadingFavorites,
  } = useFavoriteContact();
  const { setReloadFavsCallback } = useTransferContext();

  useEffect(() => {
    GetFavorites();
    setReloadFavsCallback(GetFavorites);
  }, []);

  const validateAccount = async (
    accounts: Account[],
    hasOneDestinyAcc: boolean,
  ) => {
    setLoading(true);
    try {
      const accountDetails = await Promise.all(
        accounts?.map(async account => {
          const response = await api.get<AccountResponse>(
            `/utils/validCvuAlias/${account.cbu}`,
          );
          return response.data.data;
        }),
      );
      setAccountData(accountDetails);
      if (hasOneDestinyAcc) {
        navigateToAmount(accountDetails);
        return;
      }
      setModalVisible(true);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false)
    }
  };

  const navigateToAmount = (accountDetails: Account[]) => {
    const selectedAccountData = accountDetails[0];
    setModalVisible(false);
    navigation.navigate('StackTransfer', {
      screen: 'AmountScreen',
      params: {
        nombre: selectedAccountData?.titulares[0].nombre,
        banco: '',
        cuit: selectedAccountData?.titulares[0].cuit,
        cvu: selectedAccountData?.cvu,
      },
    });
  };

  return (
    <View style={styles.container}>
      <LoaderModal modalVisible={loading} />
      <View style={styles.titleContainer}>
        <TextBase type="SemiBold" size="l">
          Favoritos
        </TextBase>
      </View>
      {loadingFavorites ? (
        <Skeleton
          width={400}
          backgroundColor="#F7F8FE"
          highlightColor="#e8e9ed"
          height={40}
        />
      ) : (
        <FlatList
          data={successData?.data}
          keyExtractor={item => item._id}
          horizontal
          renderItem={({ item }) => (
            <UserCircle
              user={item}
              circle={circle}
              validateAccount={validateAccount}
            />
          )}
          showsHorizontalScrollIndicator={false}
        />
      )}

      <Accounts
        modalVisible={modalVisible}
        setModalVisible={setModalVisible}
        accountData={accountData}
      />
    </View>
  );
};

export default FavoritesCard;
