import { api } from '../../services/apiService';
import { useState } from 'react';

export const useRequestCode = ({
  setLoadingCode,
  setErrorCode,
  setErrorUser,
  setEmailError,
}: any) => {
  const [successData, setSuccessData] = useState<any>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const requestCode = async (body: any) => {
    try {
      setLoadingCode(true);
      const response = await api.post('/auth/password/reset-request', body);
      console.log('response', JSON.stringify(response, null, 4));
      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
        setLoadingCode(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setErrorCode(true);
      }
      setLoadingCode(false);
    } catch (err: any) {
      console.error('Error:', err);
      if (err.response.data.message.toLowerCase().includes('not found')) {
        setErrorUser(true);
        setEmailError('');
      }
      setErrorCode(true);
      setLoadingCode(false);
    }
  };

  return { requestCode, successData, success, setSuccess };
};
