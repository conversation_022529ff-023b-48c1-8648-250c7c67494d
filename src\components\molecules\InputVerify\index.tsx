/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import {
  NativeSyntheticEvent,
  Pressable,
  Text,
  TextInput,
  TextInputKeyPressEventData,
  View,
} from 'react-native';
import { styles } from './styles';
import color from '../../../theme/pallets/pallet';
type Props = {
  code: string;
  handleKeyPress?: (
    e: NativeSyntheticEvent<TextInputKeyPressEventData>,
  ) => void;
  inputRef: React.RefObject<TextInput>;
  handleOnPress: () => void;
  error?: boolean;
  handleOnChange?: any;
  maxLength?: number;
};

export const InputVerify = ({
  code,
  handleOnPress,
  handleKeyPress,
  inputRef,
  error,
  handleOnChange,
  maxLength,
}: Props) => {
  return (
    <View style={styles.mainContainer}>
      <Pressable onPress={handleOnPress} style={{ flex: 1, minHeight: 64 }}>
        <View style={styles.container}>
          <View style={{ zIndex: 1 }}>
            <Text
              style={[
                styles.textInput,
                {
                  color: code
                    ? error
                      ? color.RED_700
                      : color.TEXT_PRIMARY
                    : color.NEUTRALS_400,
                },
              ]}
            >
              {code ? `${code.slice(0, 3)}-${code.slice(3)}` : 'XXX-XXX'}
            </Text>
          </View>
          <TextInput
            ref={inputRef}
            style={styles.input}
            onKeyPress={handleKeyPress}
            onChangeText={handleOnChange}
            keyboardType="numeric"
            defaultValue={code}
            value={code}
            maxLength={maxLength}
          />
        </View>
      </Pressable>
    </View>
  );
};
