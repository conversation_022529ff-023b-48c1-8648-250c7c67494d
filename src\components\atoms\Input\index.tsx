import React, { FC, useState, useEffect, useRef } from 'react';
import {
  StyleProp,
  TextInput,
  TextStyle,
  TouchableOpacity,
  View,
  TextInputProps,
} from 'react-native';
import { EyeIcon, EyeSlashIcon } from '../Icons';
import { styles } from './styles';
import TextBase from '../TextBase';
import color from '../../../theme/pallets/pallet';

export type InputProps = {
  text: string;
  setText: (text: string) => void;
  placeholder: string;
  onBlur?: (e: any) => void;
  hideText?: boolean;
  icon?: React.JSX.Element;
  errorStyle?: StyleProp<TextStyle>;
  error?: boolean;
  keyboardType?: string;
  maxLength?: number;
  onFocus?: () => void;
  editable?: boolean;
  resend?: boolean;
  resendCode?: () => void;
} & TextInputProps;

const Input: FC<InputProps> = ({
  text,
  setText,
  placeholder,
  onBlur,
  hideText,
  icon,
  errorStyle,
  keyboardType,
  maxLength,
  onFocus,
  editable,
  resend,
  resendCode,
}) => {
  const [showValue, setShowValue] = useState(false);
  const [countdown, setCountdown] = useState(32);
  const inputRef = useRef<TextInput>(null);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (resend && countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(prevCount => prevCount - 1);
      }, 1000);
    }

    return () => {
      clearTimeout(timer);
    };
  }, [countdown, resend]);

  useEffect(() => {
    if (resend) {
      setCountdown(32);
    }
  }, [resend]);

  const handleResend = () => {
    setCountdown(32);
    resendCode && resendCode();
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(
      remainingSeconds,
    ).padStart(2, '0')}`;
  };

  const inputKeyboardType = keyboardType === 'numeric' ? 'numeric' : 'default';

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  return (
    <View>
      {icon && (
        <View
          style={{
            position: 'absolute',
            alignSelf: 'flex-start',
            left: 12,
            top: 12,
          }}
        >
          {icon}
        </View>
      )}
      <TextInput
        ref={inputRef}
        placeholderTextColor={color.NEUTRALS_100}
        style={[
          styles.input,
          { paddingLeft: icon ? 38 : 10 },
          errorStyle,
          isFocused && styles.focusedInput,
        ]}
        onChangeText={setText}
        value={text}
        placeholder={placeholder && placeholder}
        onBlur={e => {
          handleBlur();
          onBlur && onBlur(e);
        }}
        secureTextEntry={hideText && !showValue ? true : false}
        keyboardType={inputKeyboardType}
        maxLength={maxLength}
        onFocus={() => {
          handleFocus();
          onFocus && onFocus();
        }}
        editable={editable}
      />

      {hideText && (
        <TouchableOpacity
          style={{ position: 'absolute', alignSelf: 'flex-end', padding: 6 }}
          onPress={() => setShowValue(!showValue)}
        >
          {showValue ? (
            <EyeIcon size={29} color={color.BLACK} />
          ) : (
            <EyeSlashIcon size={29} color={color.BLACK} />
          )}
        </TouchableOpacity>
      )}
      {resend &&
        (resend && countdown > 0 ? (
          <View
            style={{ position: 'absolute', alignSelf: 'flex-end', padding: 10 }}
          >
            <TextBase>{formatTime(countdown)}</TextBase>
          </View>
        ) : (
          <TouchableOpacity
            style={{ position: 'absolute', alignSelf: 'flex-end', padding: 10 }}
            onPress={handleResend}
          >
            <TextBase color={color.PRIMARY_500} type="Bold">
              Reenviar código
            </TextBase>
          </TouchableOpacity>
        ))}
    </View>
  );
};

export default Input;
