import React, { useCallback, useContext, useEffect, useState } from 'react';
import { LoginForm } from '../components/organisms';
import { LoginScreenParams } from '../types/Navigation';
import { AuthContext } from '../context/AuthContext';
import { useForm } from 'react-hook-form';
import { Keyboard } from 'react-native';
import { useBiometrics } from '../hooks/useBiometrics';
import { storage } from '../lib/mmkv';
import { isNil } from 'lodash';
import { promptBiometrics } from '../lib/biometrics';
import { getCredentials } from '../helpers/asyncStorage';
import { useIsFocused } from '@react-navigation/native';
import { StackScreenProps } from '@react-navigation/stack';

export type InputLogin = {
  email: string;
  password: string;
};
interface Props extends StackScreenProps<LoginScreenParams, 'Login'> {}

export default function LoginScreen({ navigation }: Props) {
  const [loading, setLoading] = useState(false);
  const [keyboardOpen, setKeyboardOpen] = useState(false);

  const { signIn, error, setError } = useContext(AuthContext);

  const { control, handleSubmit, reset, formState } = useForm<InputLogin>();
  const isButtonDisabled = !formState.isValid || formState.isSubmitting;
  const isFocused = useIsFocused();
  const [hasTriggeredBiometrics, setHasTriggeredBiometrics] = useState(false);
  const { available } = useBiometrics();
  const unlockMethodType = storage.getString('unlockMethodType');

  const triggerBiometrics = useCallback(async () => {
    try {
      if (
        unlockMethodType === 'Biometrics' ||
        unlockMethodType === 'FaceID' ||
        unlockMethodType === 'TouchID'
      ) {
        const success = await promptBiometrics(unlockMethodType);
        setHasTriggeredBiometrics(true);

        if (success) {
          setLoading(true);

          const credentials = await getCredentials();

          if (isNil(credentials)) {
            setLoading(false);
            return;
          }

          const loginResponse = await signIn(
            credentials.username,
            credentials.password,
          );
          setLoading(false);
          if (loginResponse.data) {
            navigation.navigate('Auth');
          }
        }
      }
    } catch (ex) {
      setLoading(false);
    }
  }, [unlockMethodType, signIn, navigation]);

  useEffect(() => {
    if (
      available &&
      !isNil(unlockMethodType) &&
      isFocused &&
      !hasTriggeredBiometrics
    ) {
      triggerBiometrics();
    }
  }, [
    available,
    triggerBiometrics,
    unlockMethodType,
    isFocused,
    hasTriggeredBiometrics,
  ]);

  useEffect(() => {
    error && setLoading(false);
  }, [error]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardOpen(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardOpen(false);
      },
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);
  const handleOnSubmit = async (values: any) => {
    setLoading(true);
    try {
      const res: any = await signIn(values.email, values.password);
      if (res.data) {
        navigation.navigate('Auth');
        reset();
        setLoading(false);
        setError(false);
      } else if (res.err.response.status === 403) {
        navigation.navigate('StackVerifyAccount', {
          screen: 'VerifyCode',
          params: {
            email: values.email,
            password: values.password,
          },
        });
        reset();
        setLoading(false);
        setError(false);
      } else {
        setError(true);
        setLoading(false);
      }
    } catch (ex) {
      setLoading(false);
      setError(true);
    }
  };
  return (
    <LoginForm
      control={control}
      handleSubmit={handleSubmit}
      loading={loading}
      error={error!}
      keyboardOpen={keyboardOpen}
      handleOnSubmit={handleOnSubmit}
      isButtonDisabled={isButtonDisabled}
      triggerBiometrics={triggerBiometrics}
    />
  );
}
