import React, { FC } from 'react';
import { View } from 'react-native';
import { styles } from './styles';
import FilterIcon from '../../../atoms/Icons/FilterIcon';
import { TextBase } from '../../../atoms';
import { InputSearch } from '../../../molecules';
import _ from 'lodash';

type HeaderActivityProps = {
  handleSearchChange: (text: string) => void;
  searchQuery: string;
};

const HeaderActivity: FC<HeaderActivityProps> = ({
  handleSearchChange,
  searchQuery,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.gap8}>
        <TextBase size="xxl" type="SemiBold">
          Actividad
        </TextBase>
        <View style={styles.containerFilter}>
          <View style={styles.flex1}>
            <InputSearch
              placeholder={'Escribe para buscar'}
              setText={handleSearchChange}
              text={searchQuery}
            />
          </View>
          <View style={styles.filter}>
            <FilterIcon />
            <TextBase size="l">Filtros</TextBase>
          </View>
        </View>
      </View>
    </View>
  );
};

export default HeaderActivity;
