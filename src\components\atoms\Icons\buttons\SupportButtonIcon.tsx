import React, { FC } from 'react';
import Svg, { <PERSON>lip<PERSON><PERSON>, Defs, G, Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const SupportButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32" fill="none">
      <Rect width="32" height="32" rx="8" fill="#C9EBE5" />

      <G clip-path="url(#clip0_1866_742)">
        <Path
          d="M22.615 12.6667H23.5C23.942 12.6667 24.366 12.8423 24.6785 13.1548C24.9911 13.4674 25.1667 13.8913 25.1667 14.3333V17.6667C25.1667 18.1087 24.9911 18.5326 24.6785 18.8452C24.366 19.1577 23.942 19.3333 23.5 19.3333H22.615C22.4119 20.9444 21.6277 22.426 20.4098 23.5C19.1919 24.574 17.6239 25.1666 16 25.1667V23.5C17.3261 23.5 18.5979 22.9732 19.5355 22.0355C20.4732 21.0979 21 19.8261 21 18.5V13.5C21 12.1739 20.4732 10.9021 19.5355 9.96446C18.5979 9.02678 17.3261 8.5 16 8.5C14.6739 8.5 13.4022 9.02678 12.4645 9.96446C11.5268 10.9021 11 12.1739 11 13.5V19.3333H8.50001C8.05798 19.3333 7.63406 19.1577 7.3215 18.8452C7.00894 18.5326 6.83334 18.1087 6.83334 17.6667V14.3333C6.83334 13.8913 7.00894 13.4674 7.3215 13.1548C7.63406 12.8423 8.05798 12.6667 8.50001 12.6667H9.38501C9.58837 11.0557 10.3726 9.57439 11.5905 8.50056C12.8084 7.42673 14.3763 6.83423 16 6.83423C17.6237 6.83423 19.1916 7.42673 20.4095 8.50056C21.6274 9.57439 22.4116 11.0557 22.615 12.6667ZM8.50001 14.3333V17.6667H9.33334V14.3333H8.50001ZM22.6667 14.3333V17.6667H23.5V14.3333H22.6667ZM12.4667 19.1542L13.35 17.7408C14.1442 18.2384 15.0628 18.5015 16 18.5C16.9372 18.5015 17.8558 18.2384 18.65 17.7408L19.5333 19.1542C18.4744 19.8176 17.2496 20.1686 16 20.1667C14.7504 20.1686 13.5256 19.8176 12.4667 19.1542Z"
          fill="#2A917E"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_1866_742">
          <Rect
            width="20"
            height="20"
            fill="white"
            transform="translate(6 6)"
          />
        </ClipPath>
      </Defs>
    </Svg>
  );
};

export default SupportButtonIcon;
