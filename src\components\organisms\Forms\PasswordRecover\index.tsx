import React, { useEffect, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { styles } from './styles';
import { useNavigation } from '@react-navigation/native';
import KeyboardAvoidingComponent from '../../../molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { Button, TextBase } from '../../../atoms';
import Logo from '../../../atoms/Icons/Logo';
import GoBack from '../../Buttons/GoBackButton';
import { InputForm } from '../../../molecules';
import { useChangePassword } from '../../../../hooks/RecoverPassword/ChangePassword';
import { useRequestCode } from '../../../../hooks/RecoverPassword/RequestCode';
import { usePasswordValidation } from '../../../../hooks/use-password-validation';
import { PasswordRepeat } from '../PasswordRepeat';
import color from '../../../../theme/pallets/pallet';

export const PasswordRecover = ({ route }: any) => {
  const { email } = route.params;
  const {
    password,
    repeatPassword,
    passwordValidation,
    passwordMatch,
    passwordValid,
    handlePasswordChange,
    handleRepeatPasswordChange,
  } = usePasswordValidation();
  const navigation: any = useNavigation();
  const [loading, setLoading] = useState<boolean>(false);
  const [errorPass, setErrorPass] = useState<boolean>(false);

  const [code, setCode] = useState<string>('');

  const cleanCode = (code: string) => {
    return code.replace(/-/g, '');
  };

  const { changePassword, errorCorde, setErrorCorde, success } =
    useChangePassword({ setLoading });
  const { requestCode } = useRequestCode({ setLoading });
  const handleSubmit = async () => {
    if (!passwordValid) {
      setErrorPass(true);
      return;
    }
    const body = {
      email: email,
      password: password,
      repeatPassword: repeatPassword,
      code: cleanCode(code),
    };
    changePassword(body);
  };
  useEffect(() => {
    if (success) {
      navigation.navigate('SuccessChange');
    }
  }, [success]);

  useEffect(() => {
    if (!code) {
      setErrorCorde(false);
    }
  }, [code]);

  const handleCodeChange = (code: string) => {
    const cleanedCode = code.replace(/\D/g, '');

    let formattedCode = cleanedCode;
    if (cleanedCode.length > 3) {
      formattedCode = cleanedCode.slice(0, 3) + '-' + cleanedCode.slice(3);
    }

    setCode(formattedCode);
  };

  const resendCode = () => {
    const body = {
      email: email,
    };
    requestCode(body);
  };

  const isDisabled = () => !Object.values(passwordValidation).every(Boolean);

  return (
    <KeyboardAvoidingComponent collapse>
      <View
        style={{
          flex: 1,
          backgroundColor: 'red',
        }}
      >
        <View style={styles.header}>
          <View style={styles.left}>
            <GoBack onPress={() => navigation.goBack()} />
          </View>
          <View style={styles.center}>
            <Logo width={80} height={24} />
          </View>
          <View style={styles.right} />
        </View>
        <ScrollView contentContainerStyle={styles.container}>
          <View>
            <View>
              <Text style={styles.title}>Ingresa tu nueva contraseña</Text>
            </View>
            <View style={styles.containerInputs}>
              <TextBase>Código de verificación</TextBase>
              <InputForm
                placeholder="XXX-XXX"
                text={code}
                setText={handleCodeChange}
                error={errorCorde}
                resend
                maxLength={7}
                numeric={true}
                resendCode={resendCode}
              />
              {errorCorde && (
                <TextBase size="s" color={color.RED_700}>
                  El código ingresado no es correcto, por favor verifique.
                </TextBase>
              )}
              <TextBase size="s" style={styles.txtmail}>
                Ingresa el código que enviamos al correo electrónico.
              </TextBase>
              <PasswordRepeat
                {...{
                  password,
                  repeatPassword,
                  passwordValidation,
                  passwordMatch,
                  errorPass,
                  handlePasswordChange,
                  handleRepeatPasswordChange,
                }}
              />
            </View>
          </View>

          <Button
            text="Actualizar contraseña"
            onPress={handleSubmit}
            loading={loading}
            disabled={isDisabled()}
          />
        </ScrollView>
      </View>
    </KeyboardAvoidingComponent>
  );
};
