import React from 'react';
import { Modal, View } from 'react-native';
import { Button, TextBase } from '../../../atoms';
import { styles } from './styles';
import { Account } from '../../../../types/Account';
import ButtonOutline from '../../../atoms/ButtonOutline';
import WarningIcon from '../../../atoms/Icons/Warning';
import color from '../../../../theme/pallets/pallet';

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  DisabledSecurity: () => void;
  accountData?: Account;
};

export const DesactivateSecurityModal = ({
  modalVisible,
  setModalVisible,
  DisabledSecurity,
}: Props) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <View style={styles.overlay}>
        <View style={styles.modalView}>
          <View style={styles.containerAccount}>
            <WarningIcon />
            <TextBase
              size="l"
              color={'#191919'}
              type="Bold"
              style={styles.textModal}
            >
              Estás por desactivar la biometría
            </TextBase>
            <TextBase
              size="m"
              color={color.NEUTRALS_800}
              style={styles.textModal}
            >
              Si la desactivas tu cuenta quedará desprotegida y vulnerable.
              ¿Deseas continuar?
            </TextBase>
          </View>
          <View style={styles.mt24}>
            <Button
              onPress={DisabledSecurity}
              text="Si, desactivar biometría"
            />
            <ButtonOutline
              onPress={() => setModalVisible(false)}
              text="No, cancelar"
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};
