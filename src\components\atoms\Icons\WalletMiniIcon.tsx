import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const WalletMiniIcon: FC<IconProps> = () => {
  return (
    <Svg width="30" height="28" viewBox="0 0 30 28" fill="none">
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.80207 18.4805C4.35436 18.4805 4.80207 18.9282 4.80207 19.4805C4.80207 21.028 6.27186 22.4005 8.57543 22.4005C10.8613 22.4005 12.3354 21.0182 12.3354 19.4805C12.3354 18.9282 12.7832 18.4805 13.3354 18.4805C13.8877 18.4805 14.3354 18.9282 14.3354 19.4805V22.1872C14.3354 25.1586 11.6195 27.3338 8.57543 27.3338C5.53518 27.3338 2.80207 25.162 2.80207 22.1872V19.4805C2.80207 18.9282 3.24979 18.4805 3.80207 18.4805ZM5.14014 23.4746C5.71392 24.5299 6.9737 25.3338 8.57543 25.3338C10.1781 25.3338 11.4342 24.5279 12.0032 23.4687C11.017 24.0713 9.81142 24.4005 8.57543 24.4005C7.33626 24.4005 6.12821 24.0745 5.14014 23.4746Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.54662 12.68C7.45544 12.68 6.50682 13.054 5.83265 13.6452C5.16595 14.2286 4.78669 14.9931 4.78669 15.8133C4.78669 16.2859 4.91719 16.7211 5.14803 17.1076C5.71797 18.0306 6.96838 18.7333 8.55997 18.7333C10.158 18.7333 11.4036 18.0394 11.9707 17.1096C12.2023 16.7225 12.3333 16.2867 12.3333 15.8133C12.3333 14.9907 11.9536 14.223 11.2798 13.6616L11.2672 13.651C10.5848 13.0632 9.63076 12.68 8.54662 12.68ZM4.51401 12.1415C5.57318 11.2127 6.99781 10.68 8.54662 10.68C10.0994 10.68 11.5162 11.2281 12.5665 12.1304C13.6491 13.0354 14.3333 14.3455 14.3333 15.8133C14.3333 16.6723 14.0915 17.4621 13.6841 18.1411L13.681 18.1463C12.6741 19.8018 10.6938 20.7333 8.55997 20.7333C6.42082 20.7333 4.44601 19.785 3.44132 18.1503L3.43574 18.1412C3.0283 17.4621 2.78669 16.6722 2.78669 15.8133C2.78669 14.3403 3.47404 13.0514 4.51401 12.1415Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.57543 12.6667C6.35093 12.6667 4.80207 14.1998 4.80207 15.8133V19.4805C4.80207 21.015 6.28567 22.4005 8.57543 22.4005C10.8637 22.4005 12.3354 21.0296 12.3354 19.4805V15.8133C12.3354 14.9906 11.955 14.2155 11.2932 13.6351C10.611 13.0486 9.65804 12.6667 8.57543 12.6667ZM2.80207 15.8133C2.80207 12.8402 5.51993 10.6667 8.57543 10.6667C10.1313 10.6667 11.5505 11.2169 12.6015 12.1224L12.6073 12.1274C13.6504 13.0401 14.3354 14.3439 14.3354 15.8133V19.4805C14.3354 22.5181 11.5405 24.4005 8.57543 24.4005C5.61186 24.4005 2.80207 22.506 2.80207 19.4805V15.8133Z"
        fill="#0068FF"
      />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.40538 2.73506C7.69862 2.68994 8.00765 2.66669 8.33332 2.66669H20.3333C20.6594 2.66669 20.9291 2.67976 21.1689 2.71973C21.1852 2.72244 21.2015 2.72474 21.2178 2.72664C22.7135 2.90056 23.8921 3.53098 24.6996 4.46668C25.5089 5.4045 26 6.71295 26 8.33335V9.26662H24.227C23.9743 9.26662 23.7263 9.2915 23.4858 9.33969C23.3642 9.36404 23.2445 9.39435 23.1271 9.43043C22.5481 9.60828 22.0244 9.92614 21.599 10.36C20.8406 11.1054 20.403 12.1794 20.5106 13.3289C20.6831 15.359 22.5072 16.7333 24.3866 16.7333H26V17.6667C26 19.4394 25.4134 20.839 24.4595 21.7929C23.5057 22.7468 22.1061 23.3334 20.3333 23.3334H17C16.4477 23.3334 16 23.7811 16 24.3334C16 24.8856 16.4477 25.3334 17 25.3334H20.3333C22.5606 25.3334 24.4943 24.5866 25.8738 23.2071C27.2532 21.8277 28 19.8939 28 17.6667V16.4935C28.788 16.1109 29.3337 15.3105 29.3337 14.3733V11.6266C29.3337 10.6894 28.788 9.88899 28 9.50636V8.33335C28 6.30042 27.3777 4.50886 26.2137 3.16001C25.0526 1.81456 23.4067 0.973012 21.4726 0.742833C21.081 0.679691 20.6929 0.666687 20.3333 0.666687H8.33332C7.91718 0.666687 7.51083 0.696071 7.1151 0.756198C5.19748 0.999889 3.56981 1.84694 2.42393 3.19134C1.27736 4.53654 0.666656 6.3162 0.666656 8.33335V11C0.666656 11.5523 1.11437 12 1.66666 12C2.21894 12 2.66666 11.5523 2.66666 11V8.33335C2.66666 6.72384 3.14928 5.4235 3.94605 4.4887C4.74077 3.5563 5.90141 2.92418 7.37784 2.7389L7.40538 2.73506ZM23.8976 11.297C24.0035 11.2771 24.1135 11.2667 24.2266 11.2667H26.9786C27.1934 11.2812 27.3337 11.4494 27.3337 11.6266V14.3733C27.3337 14.5508 27.1929 14.7194 26.9774 14.7333H24.387C23.3882 14.7333 22.5741 14.0035 22.5035 13.1569L22.5025 13.1451C22.4526 12.6215 22.6508 12.1284 23.0055 11.7822L23.0234 11.7642C23.2132 11.5693 23.4487 11.4237 23.7155 11.3419C23.7747 11.3237 23.8355 11.3087 23.8976 11.297Z"
        fill="#0068FF"
      />
    </Svg>
  );
};

export default WalletMiniIcon;
