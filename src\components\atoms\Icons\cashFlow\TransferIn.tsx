import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const TransferIn: FC<IconProps> = ({ color, size }) => {
  return (
    <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <Path
        d="M15 19V17H8.41L20 5.41L18.59 4L7 15.59V9H5V19H15Z"
        fill="#EF6C00"
      />
    </Svg>
  );
};

export default TransferIn;
