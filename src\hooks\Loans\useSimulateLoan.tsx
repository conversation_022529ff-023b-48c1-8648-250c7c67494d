import { useEffect, useState } from 'react';
import { simulateLoan } from '../../services/loanServices';
import { SimulateLoanResponse } from '../../types/StackLoan/SimulateLoanResponse';

export const useSimulateLoan = (loanTypeId: string, amount: number) => {
  const [simulationResult, setSimulationResult] = useState<SimulateLoanResponse | undefined>();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);

  const getLoanSimulationFromApi = async () => {
    setLoading(true);
    try {
      const simulateLoanResponse = await simulateLoan(loanTypeId, amount);
      setSimulationResult(simulateLoanResponse);

    } catch (err: any) {
      setError(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getLoanSimulationFromApi();
  }, []);

  return { simulationResult, loading, error };
};
