import React, { FC } from 'react';
import { View, ViewStyle } from 'react-native';
import color from '../../../theme/pallets/pallet';

const DEFAULT_SIZE = 32;
const DEFAULT_BACKBGROUND_COLOR = color.PRIMARY_50;
const DEFAULT_BACKBGROUND_SHAPE = 'circle';

interface IconWrapperProps {
  size?: number;
  backgroundColor?: string;
  shape?: 'circle' | 'square' | 'roundedSquare';
  children: React.ReactNode;
}

const BackgroundWrapper: FC<IconWrapperProps> = ({
  size = DEFAULT_SIZE,
  backgroundColor = DEFAULT_BACKBGROUND_COLOR,
  shape = DEFAULT_BACKBGROUND_SHAPE,
  children,
}) => {
  const borderRadius =
    shape === 'circle' ? size / 2 : shape === 'roundedSquare' ? 8 : 0;

  const wrapperStyle: ViewStyle = {
    backgroundColor,
    width: size,
    height: size,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius,
  };

  return <View style={wrapperStyle}>{children}</View>;
};

export default BackgroundWrapper;
