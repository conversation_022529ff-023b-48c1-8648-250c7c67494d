import React from 'react';
import { View } from 'react-native';
import { Image } from 'react-native';
import { styles } from './styles';
import { Text } from '../../../atoms';

type InfoAccountProps = {
  nombre: string;
  banco?: string;
};

export const InfoAccount = ({ nombre, banco }: InfoAccountProps) => {
  const firstLetter = nombre.charAt(0).toUpperCase();
  const secondLetter = nombre.split(' ')[1]?.charAt(0).toUpperCase() || '';
  return (
    <View style={styles.container}>
      <View style={styles.circle}>
        <Text variant="R3" color={'PRIMARY_700'}>
          {firstLetter}
          {secondLetter}
        </Text>
      </View>
      <View>
        <Text numberOfLines={2} style={styles.name}>
          {nombre}
        </Text>
        {banco && <Text style={styles.cuenta}>{banco}</Text>}
      </View>
    </View>
  );
};
