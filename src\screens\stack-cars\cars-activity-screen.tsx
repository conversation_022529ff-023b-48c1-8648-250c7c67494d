import { Pressable, View } from 'react-native';
import React from 'react';
import { Text } from '../../components/atoms';
import { InputSearch } from '../../components/molecules';
import { FlashList } from '@shopify/flash-list';
import color from '../../theme/pallets/pallet';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { CarsStackParams } from '../../navigation/stack-cars/types';
import { formatCurrency } from '../../helpers/formatCurrency';

type Props = NativeStackScreenProps<CarsStackParams, 'CarsActivityScreen'>;
export default function CarsActivityScreen({ navigation }: Props) {
  const handleNavigateToDetail = () => {
    navigation.navigate('CarsDetailScreen');
  };

  return (
    <View style={{ flex: 1, paddingHorizontal: 16, gap: 24 }}>
      <Text variant="B2">Actividad</Text>
      <InputSearch
        placeholder={'Busca un vehiculo'}
        setText={() => { }}
        text={''}
      />
      <FlashList
        data={[
          {
            id: '1',
            name: 'Fiat Cronos',
            detail: 'FCR',
            invested: 'ARS 500.000',
            return: '4,2 - 4,9%',
          },
        ]}
        keyExtractor={(item, index) => item.id + index}
        renderItem={() => (
          <Pressable
            style={{
              borderBottomColor: color.NEUTRALS_100,
              borderBottomWidth: 1,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingVertical: 16,
            }}
            onPress={handleNavigateToDetail}
          >
            <View>
              <Text variant="B7">Fiat Cronos</Text>
              <Text variant="R7" color="NEUTRALS_600">
                Invertiste hoy
              </Text>
            </View>
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              {true ? (
                <Text variant="R7" color="GREEN_500">
                  +$ {formatCurrency(500)}
                </Text>
              ) : (
                <Text variant="R7">-$ {formatCurrency(500)}</Text>
              )}
            </View>
          </Pressable>
        )}
        estimatedItemSize={300}
        onEndReached={() => { }}
        onEndReachedThreshold={0.1}
        ListFooterComponent={<View />}
        refreshControl={<View />}
      />
    </View>
  );
}
