import React, { FC } from 'react';
import { StyleSheet, View } from 'react-native';
import TextBase from '../../components/atoms/TextBase';
import IntroSwiper from '../../components/shared/IntroSwiper';
import { Button } from '../../components/atoms';
import color from '../../theme/pallets/pallet';

const RequestCardIntroScreen: FC<any> = ({ navigation }) => {
  const navigateToRequestCardScreen = () => {
    navigation.navigate('StackCards', {
      screen: 'RequestCardScreen',
    });
  };

  return (
    <View style={styles.container}>
      <View>
        <TextBase style={styles.title}>Tarjeta Prepaga</TextBase>
      </View>
      <IntroSwiper
        titleOne="Obtené tu Tarjeta Prepaga al instante"
        subtitleOne="En pocos pasos podrás acceder a tu tarjeta prepaga hoy mismo."
        titleTwo="Sin costos de emisión ni mantenimiento"
        subtitleTwo="Tu dinero dónde y cuando quieras, no te vamos a cobrar nada por usarla."
        titleThree="Activala o pausala las veces que quieras"
        subtitleThree="Podrás pausar, denunciar o dar de baja la tarjeta cuando lo desees."
      />
      <View style={styles.buttonsContainer}>
        <Button onPress={navigateToRequestCardScreen} text="Continuar" />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 26,
    fontFamily: 'Satoshi-Regular',
    fontWeight: 'bold',
    color: color.BLACK,
    paddingHorizontal: 16,
  },
  buttonsContainer: {
    paddingVertical: 20,
    alignItems: 'center',
    display: 'flex',
    paddingHorizontal: 16,
  },
});

export default RequestCardIntroScreen;
