import React, { FC, useEffect, useRef } from 'react';
import { View, Animated, StyleSheet } from 'react-native';

interface Props {
  color: string;
}

const StickLoader: FC<Props> = ({ color }) => {
  const animatedValues = useRef([...Array(8)].map(() => new Animated.Value(1))).current;

  const styles = StyleSheet.create({
    container: {
      width: 100,
      height: 100,
      justifyContent: 'center',
      alignItems: 'center',
    },
    ray: {
      position: 'absolute',
      width: 4,
      height: 12,
      backgroundColor: color, // Adjust color as needed
      borderRadius: 3,
    },
  });

  useEffect(() => {
    const animations = animatedValues.map((animatedValue, index) =>
      Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 0.2, // Minimum opacity
            duration: 700, // Fade out duration
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 1, // Maximum opacity
            duration: 700, // Fade in duration
            useNativeDriver: true,
          }),
        ]),
      ),
    );

    Animated.stagger(100, animations).start();
  }, [animatedValues]);

  return (
    <View style={styles.container}>
      {[...Array(8)].map((_, index) => (
        <Animated.View
          key={index}
          style={[
            styles.ray,
            {
              transform: [
                { rotate: `${index * 45}deg` }, // Position rays around the circle
                { translateY: -20 },
              ],
              opacity: animatedValues[index],
            },
          ]}
        />
      ))}
    </View>
  );
};

export default StickLoader;
