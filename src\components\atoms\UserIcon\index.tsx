import React, { FC } from 'react';
import { View } from 'react-native';
import { styles } from './styles';
import color from '../../../theme/pallets/pallet';
import Text from '../Text';

type Props = {
  backgroundColor?: string;
  usernameInitials: string;
};

const UserIcon: FC<Props> = ({
  backgroundColor = color.PRIMARY_100,
  usernameInitials,
}) => {
  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor,
        },
      ]}
    >
      <View style={styles.letter}>
        <Text variant="R6" color="PRIMARY_700">
          {usernameInitials}
        </Text>
      </View>
    </View>
  );
};

export default UserIcon;
