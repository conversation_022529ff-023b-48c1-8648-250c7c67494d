import React from 'react';
import { Modal, Pressable, Text, View } from 'react-native';
import { styles } from './styles';

const Item = ({ label, onPress }: { label: string; onPress: () => void }) => {
  return (
    <Pressable style={styles.containerItem} onPress={onPress}>
      <Text style={styles.label}>{label}</Text>
    </Pressable>
  );
};

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  navigateTo: (screen: string) => void;
};

export const SelectCashInModal = ({
  modalVisible,
  setModalVisible,
  navigateTo,
}: Props) => {
  const Items = [
    {
      label: 'Añadir cuenta bancaria',
      onPress: () => navigateTo('NewBankAccount'),
    },
    {
      label: 'Añadir tarjeta de Crédito o Débito',
    },
    {
      label: 'Añadir Paypal',
    },
  ];
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <Pressable
        style={styles.overlay}
        onPress={() => setModalVisible(!modalVisible)}
      >
        <View style={styles.modalView}>
          <View>
            {Items.map(item => (
              <Item
                label={item.label}
                key={item.label}
                onPress={item.onPress!}
              />
            ))}
          </View>
          <View style={styles.mt24} />
        </View>
      </Pressable>
    </Modal>
  );
};
