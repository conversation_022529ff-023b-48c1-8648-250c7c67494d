import { useEffect, useState } from 'react';
import { getLoans } from '../../services/loanServices';
import { LoanResponse } from '../../types/StackLoan/LoansResponse';

export const useLoansList = () => {
  const [loansList, setLoansList] = useState<LoanResponse[] | undefined>();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<boolean>(false);

  const getLoansListFromApi = async () => {
    setLoading(true);
    try {
      const loansResponse = await getLoans();
      setLoansList(loansResponse);

    } catch (err: any) {
      setError(true);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getLoansListFromApi();
  }, []);

  return { getLoansListFromApi, loansList, loading, error };
};
