import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const ArrowLeftSmall: FC<IconProps> = ({ size = 24, color = '#BAC2C7' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24">
      <Path
        d="M17.8852 3.77L16.1152 2L6.11523 12L16.1152 22L17.8852 20.23L9.65523 12L17.8852 3.77Z"
        fill={color}
      />
    </Svg>
  );
};

export default ArrowLeftSmall;
