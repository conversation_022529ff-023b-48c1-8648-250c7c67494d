import React, { FC } from 'react';
import Svg, { Path } from 'react-native-svg';
import { IconProps } from './types';

const WatchIcon: FC<IconProps> = () => {
  return (
    <Svg width="28" height="28" viewBox="0 0 28 28" fill="none">
      <Path
        d="M13.9866 0.666667C6.62663 0.666667 0.666626 6.64 0.666626 14C0.666626 21.36 6.62663 27.3333 13.9866 27.3333C21.36 27.3333 27.3333 21.36 27.3333 14C27.3333 6.64 21.36 0.666667 13.9866 0.666667ZM14 24.6667C8.10663 24.6667 3.33329 19.8933 3.33329 14C3.33329 8.10667 8.10663 3.33333 14 3.33333C19.8933 3.33333 24.6666 8.10667 24.6666 14C24.6666 19.8933 19.8933 24.6667 14 24.6667ZM14.6666 7.33333H12.6666V15.3333L19.6666 19.5333L20.6666 17.8933L14.6666 14.3333V7.33333Z"
        fill="#FF0033"
      />
    </Svg>
  );
};

export default WatchIcon;
