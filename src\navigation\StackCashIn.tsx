import React from 'react';

import GoBack from '../components/organisms/Buttons/GoBackButton';
import { CashInScreen } from '../screens/StackCashIn/CashInScreen';
import { NewBankAccount } from '../screens/StackCashIn/NewBankAccount';
import { useNavigation } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<any>();

function ArrowBack() {
  const navigation = useNavigation();
  return <GoBack onPress={() => navigation.goBack()} />;
}

export default function StackCashIn() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'CashInScreen'}
        component={CashInScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'NewBankAccount'}
        component={NewBankAccount}
      />
    </Stack.Navigator>
  );
}
