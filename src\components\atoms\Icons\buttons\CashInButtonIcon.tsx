import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const CashInButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#E6F7EC" />

      <Path
        d="M8.5 21.8333H23.5V23.5H8.5V21.8333ZM16.8333 10.8567V20.1667H15.1667V10.8567L10.1075 15.9167L8.92917 14.7383L16 7.66666L23.0708 14.7375L21.8925 15.9158L16.8333 10.8583V10.8567Z"
        fill="#289B4F"
      />
    </Svg>
  );
};

export default CashInButtonIcon;
