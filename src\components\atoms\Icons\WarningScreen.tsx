import React, { FC } from 'react';
import Svg, { Circle, Path, Rect } from 'react-native-svg';
import { IconProps } from './types';

const WarningImage: FC<IconProps> = ({ width, height }) => {
  return (
    <Svg width={width} height={height} viewBox="0 0 164 126" fill="none">
      <Circle cx="82.5" cy="63" r="63" fill="#FDC228" />
      <Rect x="46.5" y="40" width="72" height="86" rx="8" fill="white" />
      <Path
        d="M82.5 59.9834L95.05 81.6667H69.95L82.5 59.9834ZM82.5 53.3334L64.1666 85H100.833L82.5 53.3334ZM84.1666 76.6667H80.8333V80H84.1666V76.6667ZM84.1666 66.6667H80.8333V73.3334H84.1666V66.6667Z"
        fill="#FDC228"
      />
      <Path
        d="M66.5 98H98.5"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Path
        d="M63.5 107H101.5"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Path
        d="M66.5 116H98.5"
        stroke="#BAC2C7"
        stroke-width="2"
        stroke-linecap="round"
      />
      <Circle cx="13.5" cy="8" r="8" fill="#FFECBA" />
      <Circle cx="155.5" cy="100" r="8" fill="#FFECBA" />
      <Circle cx="5" cy="99.5" r="4.5" fill="#FFECBA" />
      <Circle cx="155" cy="12.5" r="4.5" fill="#FFECBA" />
    </Svg>
  );
};

export default WarningImage;
