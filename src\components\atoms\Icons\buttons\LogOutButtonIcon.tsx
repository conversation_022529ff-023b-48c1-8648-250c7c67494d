import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const LogOutButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#FFF7F7" />

      <Path
        d="M20.1667 12.6667L18.9917 13.8417L20.3083 15.1667H13.5V16.8333H20.3083L18.9917 18.15L20.1667 19.3333L23.5 16L20.1667 12.6667ZM10.1667 10.1667H16V8.5H10.1667C9.25 8.5 8.5 9.25 8.5 10.1667V21.8333C8.5 22.75 9.25 23.5 10.1667 23.5H16V21.8333H10.1667V10.1667Z"
        fill="#E05555"
      />
    </Svg>
  );
};

export default LogOutButtonIcon;
