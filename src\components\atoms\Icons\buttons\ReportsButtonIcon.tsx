import React, { FC } from 'react';
import Svg, { Path, Rect } from 'react-native-svg';
import { IconProps } from '../types';

const ReportsButtonIcon: FC<IconProps> = ({ size }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32">
      <Rect width="32" height="32" rx="8" fill="#F2E1F2" />

      <Path
        d="M15.1667 6.45251C15.4417 6.42834 15.7192 6.41667 16 6.41667C21.2925 6.41667 25.5833 10.7075 25.5833 16C25.5833 16.2808 25.5717 16.5583 25.5475 16.8333H24.2925C23.8742 21.0442 20.3208 24.3333 16 24.3333C11.3975 24.3333 7.66666 20.6025 7.66666 16C7.66666 11.6792 10.9558 8.12584 15.1667 7.70834V6.45167V6.45251ZM15.1667 16.8333V9.385C13.4856 9.59891 11.949 10.4449 10.8693 11.751C9.78959 13.0571 9.24772 14.7254 9.35384 16.4167C9.45996 18.108 10.2061 19.6954 11.4406 20.8564C12.6751 22.0173 14.3054 22.6646 16 22.6667C17.6238 22.6666 19.1918 22.074 20.4098 21C21.6277 19.926 22.4118 18.4444 22.615 16.8333H15.1667ZM23.8733 15.1667C23.6823 13.3649 22.8792 11.6832 21.598 10.402C20.3168 9.12082 18.6351 8.31772 16.8333 8.12667V15.1667H23.8733Z"
        fill="#963898"
      />
    </Svg>
  );
};

export default ReportsButtonIcon;
