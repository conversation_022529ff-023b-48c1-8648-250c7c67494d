import React, { useEffect, useState } from 'react';
import {
  View,
  StyleSheet,
  Platform,
  PermissionsAndroid,
  Button,
  TouchableOpacity,
} from 'react-native';
import Orientation from 'react-native-orientation-locker';
import {
  Camera,
  Code,
  useCameraDevice,
  useCodeScanner,
} from 'react-native-vision-camera';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import { TextBase } from '../../components/atoms';
import ButtonOutline from '../../components/atoms/ButtonOutline';
import ChevronLeft from '../../components/atoms/Icons/ChevronLeft';
import ButtonOutlineWhite from '../../components/atoms/ButtonOutlineWhite';

const NoCameraDeviceError = () => {
  return (
    <View style={styles.container}>
      <TextBase>No camera device available</TextBase>
    </View>
  );
};

export const ScannerCodeScreen = () => {
  const [cameraPermission, setCameraPermission] = useState(false);

  const navigation: any = useNavigation();

  const device = useCameraDevice('back');

  const isFocussed = useIsFocused();

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Permisos de cámara',
            message:
              'La aplicación necesita acceder a la cámara para funcionar correctamente.',
            buttonNeutral: 'Preguntar después',
            buttonNegative: 'Cancelar',
            buttonPositive: 'OK',
          },
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setCameraPermission(true);
        } else {
          setCameraPermission(false);
        }
      } catch (err) {
        console.warn(err);
        setCameraPermission(false);
      }
    } else {
      setCameraPermission(true);
    }
  };

  useEffect(() => {
    requestCameraPermission();
    Orientation.lockToLandscape();
    return () => {
      Orientation.unlockAllOrientations();
      Orientation.lockToPortrait();
    };
  }, []);

  const handleCodeScanned = (data: Code[]) => {
    try {
      JSON.parse(data[0].value);
      navigation.navigate('QrPayment', data);
    } catch (e) {
      console.log('error', e);
      navigation.navigate('ErrorScreen');
    }
  };

  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13'],
    onCodeScanned: data => {
      if (data) {
        handleCodeScanned(data);
      }
    },
  });

  if (!device) {
    return <NoCameraDeviceError />;
  }

  return (
    <View style={styles.container}>
      <View style={styles.cameraContainer}>
        <Camera
          style={styles.camera}
          device={device!}
          codeScanner={codeScanner}
          isActive={isFocussed}
        />
        <View style={styles.overlay}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          >
            <ChevronLeft />
            <TextBase type="Bold" color={'#FFFFFF'} style={styles.leftText}>
              Volver
            </TextBase>
          </TouchableOpacity>
          <TextBase color={'#FFFFFF'} style={styles.headerText}>
            Escanea el código de barras con tu cámara
          </TextBase>
        </View>
      </View>
      <View style={styles.footerOverlay}>
        <View style={styles.margin}>
          <ButtonOutlineWhite
            text="Ingresar código manualmente"
            onPress={() => {
              Orientation.lockToPortrait();
              navigation.navigate('ManualCode');
            }}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#00000040',
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftText: {
    marginLeft: 8,
  },
  headerText: {
    fontSize: 18,
    textAlign: 'center',
    flex: 2,
  },
  footerOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: '#00000040',
  },
  margin: {
    marginHorizontal: '20%',
  },
});
