import React, { useEffect, useState } from 'react';
import { Modal, Pressable, Text, View } from 'react-native';
import { Button, TextBase } from '../../../atoms';
import { styles } from './styles';
import { Account } from '../../../../types/Account';
import { InputForm } from '../../../molecules';
import { useRequestCode } from '../../../../hooks/RecoverPassword/RequestCode';
import { useNavigation } from '@react-navigation/native';
import KeyboardAvoidingComponent from '../../../molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import color from '../../../../theme/pallets/pallet';

const isValidEmail = (email: any) => {
  // Expresión regular para validar el formato de email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  accountData?: Account;
  navigaToPasswordRecover?: () => void;
  email?: string;
  setEmail?: any;
  loadingCode?: boolean;
};

export const RecoverPasswordModal = ({
  modalVisible,
  setModalVisible,
}: Props) => {
  const [email, setEmail] = useState('');
  const [loadingCode, setLoadingCode] = useState(false);
  const [errorCode, setErrorCode] = useState(false);
  const [errorUser, setErrorUser] = useState(false);
  const [emailError, setEmailError] = useState('');

  const { requestCode, success, setSuccess } = useRequestCode({
    setLoadingCode,
    setErrorCode,
    setErrorUser,
    setEmailError,
  });
  const navigation: any = useNavigation();
  const navigaToPasswordRecover = () => {
    if (!isValidEmail(email)) {
      setEmailError('Ingrese un email válido.');
      setErrorUser(false);
      return;
    }
    const body = {
      email,
    };
    requestCode(body);
  };

  useEffect(() => {
    if (success) {
      navigation.navigate('PasswordRecover', { email: email });
      setModalVisible(false);
      setEmail('');
      setEmailError('');
      setErrorUser(false);
      setSuccess(false);
    }
  }, [success]);

  const handleEmailChange = (text: string) => {
    setEmail(text);
    if (text === '') {
      setEmailError('');
      setErrorUser(false);
    }
  };
  const closeModal = () => {
    setModalVisible(false);
    setEmail('');
    setEmailError('');
    setErrorUser(false);
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <KeyboardAvoidingComponent collapse>
        <View style={styles.overlay}>
          <View style={styles.modalView}>
            <View>
              <View style={styles.containerAccount}>
                <View style={styles.account}>
                  <Text style={styles.name} numberOfLines={2}>
                    ¿Olvidaste la contraseña?
                  </Text>
                  <TextBase>
                    Ingresa tu correo electrónico para recuperar la contraseña
                  </TextBase>
                  <View style={styles.inputEmail}>
                    <TextBase>Correo electrónico</TextBase>
                    <InputForm
                      placeholder="ej. <EMAIL>"
                      text={email}
                      setText={handleEmailChange}
                      error={emailError !== '' || errorUser}
                    />
                    {emailError !== '' && (
                      <TextBase color={color.RED_700}>{emailError}</TextBase>
                    )}
                    {errorUser && (
                      <TextBase color={color.RED_700}>
                        El correo electrónico ingresado no pertenece a un
                        usuario activo.
                      </TextBase>
                    )}
                  </View>
                </View>
                <Pressable onPress={closeModal} style={styles.close}>
                  <Text style={styles.textClose}>Cerrar</Text>
                </Pressable>
              </View>
            </View>
            <View style={styles.mt24}>
              <Button
                onPress={navigaToPasswordRecover}
                text="Enviar código"
                disabled={!email}
                loading={loadingCode}
              />
            </View>
          </View>
        </View>
      </KeyboardAvoidingComponent>
    </Modal>
  );
};
