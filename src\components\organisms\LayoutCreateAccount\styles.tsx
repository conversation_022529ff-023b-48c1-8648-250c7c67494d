import { StyleSheet } from 'react-native';
import color from '../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 24,
    justifyContent: 'space-between',
  },
  terms: {
    color: color.PRIMARY_500,
    fontFamily: 'DMSans-Regular',
    width: '100%',
  },
  termsTxt: {
    color: '#191919',
    fontFamily: 'DMSans-Regular',
  },
  termsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 6,
  },
  termsTextContainer: {
    flexDirection: 'row',
    width: '100%',
    flexWrap: 'wrap',
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 1.5,
    borderColor: '#00000099',
    marginRight: 9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checked: {
    backgroundColor: color.PRIMARY_500,
  },
  checkmark: {
    transform: [{ rotate: '45deg' }],
    height: 12,
    width: 7,
    borderColor: 'white',
    borderBottomWidth: 2,
    borderRightWidth: 2,
    marginBottom: 2,
  },
  checkboxStyle: {
    marginBottom: 16,
  },
});
