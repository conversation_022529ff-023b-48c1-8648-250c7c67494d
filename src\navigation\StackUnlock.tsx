import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import CreatePINScreen from '../screens/StackUnlock/CreatePINScreen';

const Stack = createNativeStackNavigator<UnlockStackParams>();

export default function StackUnlock() {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="CrearClaveScreen" component={CreatePINScreen} />
      {/* <Stack.Screen
        name="AccesoRapidoExitosoScreen"
        component={UnlockSuccessScreen}
      /> */}
    </Stack.Navigator>
  );
}
