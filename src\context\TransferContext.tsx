import React, { createContext, useContext, ReactNode, useState } from 'react';

interface TransferContextType {
  setReloadFavsCallback: (callback: () => void) => void;
  reloadFavs: () => void;
  setReloadContactsCallback: (callback: () => void) => void;
  reloadContacts: () => void;
}

const TransferContext = createContext<TransferContextType | undefined>(
  undefined,
);

export const FavoritesProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [reloadFavsCallback, setReloadFavsCallbackState] = useState<
    (() => void) | null
  >(null);

  const setReloadFavsCallback = (callback: () => void) => {
    setReloadFavsCallbackState(() => callback);
  };

  const [reloadContactsCallback, setReloadContactsCallbackState] = useState<
    (() => void) | null
  >(null);

  const setReloadContactsCallback = (callback: () => void) => {
    setReloadContactsCallbackState(() => callback);
  };

  const reloadFavs = () => {
    if (reloadFavsCallback) {
      reloadFavsCallback();
    }
  };
  const reloadContacts = () => {
    if (reloadContactsCallback) {
      reloadContactsCallback();
    }
  };

  return (
    <TransferContext.Provider
      value={{
        setReloadFavsCallback,
        reloadFavs,
        setReloadContactsCallback,
        reloadContacts,
      }}
    >
      {children}
    </TransferContext.Provider>
  );
};

export const useTransferContext = (): TransferContextType => {
  const context = useContext(TransferContext);
  if (!context) {
    throw new Error('useTransferContext debe ser usado con FavoritesProvider');
  }
  return context;
};
