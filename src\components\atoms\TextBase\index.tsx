import React from 'react';
import {
  StyleProp,
  Text,
  TextStyle,
  TextProps,
  ColorValue,
} from 'react-native';

type FontType =
  | 'Bold'
  | 'ExtraBold'
  | 'ExtraLight'
  | 'Light'
  | 'Medium'
  | 'Regular'
  | 'SemiBold'
  | 'Thin';
type FontSize = 'xxs' | 'xs' | 's' | 'm' | 'l' | 'xl' | 'xxl' | 'xxxl';

const getFontSize = (size: string) => {
  let fontSize: any = {
    xxs: 8,
    xs: 10,
    s: 12,
    m: 14,
    l: 16,
    xl: 18,
    xxl: 24,
    xxxl: 38,
  };
  return fontSize[size];
};

interface Props extends TextProps {
  children: React.ReactNode;
  type?: FontType;
  size?: FontSize;
  style?: StyleProp<TextStyle> | StyleProp<TextStyle>[];
  adjustsFontSizeToFit?: boolean;
  numberOfLines?: number;
  allowFontScaling?: boolean;
  color?: ColorValue;
}

export default function TextBase({
  children,
  style,
  type = 'Regular',
  size = 'm',
  numberOfLines,
  adjustsFontSizeToFit = false,
  allowFontScaling = false,
  color = '#191919',
}: Props) {
  const defaultStyle = {
    fontFamily: `Sora-${type}`,
    fontSize: getFontSize(size),
    color: color,
  };

  return (
    <Text
      numberOfLines={numberOfLines}
      allowFontScaling={allowFontScaling}
      adjustsFontSizeToFit={adjustsFontSizeToFit}
      style={[defaultStyle, style]}
    >
      {children}
    </Text>
  );
}
