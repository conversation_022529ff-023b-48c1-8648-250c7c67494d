import React, { FC } from 'react';
import { View, StyleSheet } from 'react-native';
import { colors } from '../../constants/colors';
import TextBase from '../../components/atoms/TextBase';
import { Button } from '../../components/atoms';
import SuccessScreenCheck from '../../components/shared/Icons/SuccessScreenCheck';
import FailScreenCheck from '../../components/shared/Icons/FailScreenCheck';
import color from '../../theme/pallets/pallet';

export const PayInstallmentResultScreen: FC<any> = ({ navigation, route }) => {
  const { error } = route.params;
  const navigateToHomeScreen = () => {
    navigation.navigate('Home');
  };

  return (
    <View style={[styles.container, { backgroundColor: error ? colors.backgroundError : color.WHITE }]}>
      {
        !error && 
        <View style={styles.centeredView}>
          <SuccessScreenCheck />
          <TextBase type='Bold' size='l' style={styles.textCenter}>¡Listo, una cuota menos de tu Préstamo!</TextBase>
        </View>
      }
      {
        error && 
        <View style={styles.centeredView}>
          <FailScreenCheck />
          <TextBase type='Bold' size='l' style={styles.textCenter}>No pudimos abonar la cuota de tu Préstamo.</TextBase>
          <TextBase style={styles.textCenter}>Verifica tu saldo o volve a intentarlo mas tarde.</TextBase>
        </View>
      }
      <View style={styles.buttonsContainer}>
        <Button
          text="Volver al inicio"
          onPress={navigateToHomeScreen}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: "100%",
    flex: 1,
    paddingHorizontal: 16,
    justifyContent: "space-between"
  },
  centeredView: {
    flex: 1,
    justifyContent: 'center', 
    alignItems: 'center',
    gap: 12,
    paddingHorizontal: 20
  },
  buttonsContainer: {
    paddingVertical: 20,
  },
  textCenter: {
    textAlign: "center"
  }
});