import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import GoBack from '../components/organisms/Buttons/GoBackButton';
import RequestCardIntroScreen from '../screens/StackCards/RequestCardIntroScreen';
import RequestCardScreen from '../screens/StackCards/RequestCardScreen';
import RequestCardFormScreen from '../screens/StackCards/RequestCardFormScreen';
import RequestCardTermsAndConditions from '../screens/StackCards/RequestCardTermsAndConditions';
import { RequestCardResultScreen } from '../screens/StackCards/RequestCardResultScreen';
import { LoadingRequestCardScreen } from '../screens/StackCards/LoadingRequestCardScreen';

import { useNavigation } from '@react-navigation/native';
import CardDetail from '../screens/CardDetail';
import { ActivationWebViewScreen } from '../screens/StackCards/ActivationWebViewScreen';
import { StackCardsParams } from '../types/StackCards/StackCardsParams';
import { ActivationCardResultScreen } from '../screens/StackCards/ActivationCardResultScreen';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<StackCardsParams>();

function ArrowBack() {
  const navigation = useNavigation();
  return (
    <GoBack
      onPress={() => navigation.goBack()}
      // text={true}
      color={color.PRIMARY_700}
    />
  );
}

export default function StackCards() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'RequestCardIntroScreen'}
        component={RequestCardIntroScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'RequestCardScreen'}
        component={RequestCardScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'RequestCardFormScreen'}
        component={RequestCardFormScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: () => <GoBack onPress={() => navigation.goBack()} />,
        })}
        name={'RequestCardTermsAndConditions'}
        component={RequestCardTermsAndConditions}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
        })}
        name={'LoadingRequestCardScreen'}
        component={LoadingRequestCardScreen}
      />
      <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
        })}
        name={'RequestCardResultScreen'}
        component={RequestCardResultScreen}
      />
      <Stack.Screen
        options={{
          headerShown: true,
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          headerTitleAlign: 'center',
        }}
        name="CardDetail"
        component={CardDetail}
      />
      <Stack.Screen
        name="ActivationWebViewScreen"
        component={ActivationWebViewScreen}
        options={{
          headerShown: true,
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
          headerTitleAlign: 'center',
        }}
      />
          <Stack.Screen
        options={({ navigation }) => ({
          headerShown: false,
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
        })}
        name={'ActivationCardResultScreen'}
        component={ActivationCardResultScreen}
      />
    </Stack.Navigator>
  );
}
