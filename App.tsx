import React, { useEffect } from 'react';
import 'react-native-gesture-handler';
import { deletAccessData } from './src/helpers/authHelper';
import { requestUserPermission } from './src/services/notifications';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Layout } from './src/layout';

const App = () => {
  useEffect(() => {
    deletAccessData();
    requestUserPermission();
  }, []);

  return (
    <SafeAreaProvider>
      <Layout />
    </SafeAreaProvider>
  );
};

export default App;
