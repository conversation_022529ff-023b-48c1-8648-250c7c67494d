import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';

import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import LogoButton from '../Icons/LogoButton';
import color from '../../../theme/pallets/pallet';

export function InvestmentNullComponent() {
  return null;
}

export function InvestmentIcon() {
  return (
    <View style={styles.investIcon}>
      <LogoButton width={32} height={32} />
    </View>
  );
}

export function InvestmentButton({
  children,
}: {
  children: React.JSX.Element;
}) {
  const navigation = useNavigation<StackNavigationProp<any>>();

  const handleHubNavigatePress = () => navigation.navigate('StackInvestment');

  return (
    <TouchableOpacity
      style={styles.customButton}
      onPress={handleHubNavigatePress}
    >
      <View style={styles.customButtonContent}>{children}</View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  customButton: {
    top: -20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  customButtonContent: {
    width: 70,
    height: 90,
  },
  investIcon: {
    justifyContent: 'center',
    alignItems: 'center',
    height: 56,
    width: 56,
    borderRadius: 16,
    backgroundColor: color.PRIMARY_500,
  },
});
