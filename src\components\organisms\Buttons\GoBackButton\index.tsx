import React, { FC } from 'react';
import { Pressable, Text } from 'react-native';
import ArrowLeft from '../../../atoms/Icons/ArrowLeft';
import { styles } from './styles';
import color from '../../../../theme/pallets/pallet';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

const colors = color;
type Props = {
  text?: boolean;
  color?: string;
};

const GoBack: FC<Props> = ({ text, color }) => {
  const navigation = useNavigation<StackNavigationProp<any>>();
  return (
    <Pressable onPress={navigation.goBack} style={styles.container}>
      <ArrowLeft size={16} color={color ? color : colors.PRIMARY_700} />
      {!text && <Text style={styles.txt}>Volver</Text>}
    </Pressable>
  );
};

export default GoBack;
