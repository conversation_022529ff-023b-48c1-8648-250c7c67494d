import { USER_ID } from '../constants/asyncStorageKeys';
import { CreateUserLoanRequest } from '../types/StackLoan/CreateUserLoanRequest';
import { CreateUserLoanResponse } from '../types/StackLoan/CreateUserLoanResponse';
import { LoanResponse, LoansWrapResponse } from '../types/StackLoan/LoansResponse';
import { SimulateLoanResponse, SimulateLoanWrapResponse } from '../types/StackLoan/SimulateLoanResponse';
import { UserLoan, UserLoansResponse, UserLoansWrapResponse, UserLoanWrapResponse } from '../types/StackLoan/UserLoansResponse';
import { api } from './apiService';
import { getData } from './asyncStorageService';

const AwardedLoanStatus = "awarded";

export const getUserLoans = async () => {
  try {
    const userId = await getData(USER_ID);
    
    const response = await api.get<UserLoansWrapResponse>(`/loans/userLoans/${userId}`);
    const userLoans: UserLoansResponse = response.data.data;

    return filterAwardedLoans(userLoans);
  } catch (error: any) {
    console.error('Error fetching user loans:', error.response);
  }
};

export const getLoans = async () => {
  try {    
    const response = await api.get<LoansWrapResponse>("/loans/types");
    const loans: LoanResponse[] = response.data.data;

    return loans;
  } catch (error: any) {
    console.error('Error fetching loans:', error.response);
  }
}

export const simulateLoan = async (loanTypeId: string, amount: number) => {
  try {    
    const response = await api.get<SimulateLoanWrapResponse>(`/loans/types/${loanTypeId}/simulate/${amount}`);
    const simulationResponse: SimulateLoanResponse = response.data.data;

    return simulationResponse;
  } catch (error: any) {
    console.error('Error simulating loan:', error.response);
  }
}

export const createUserLoan = async (request: CreateUserLoanRequest) => {
  try {    
    const userId = await getData(USER_ID);

    const response = await api.post<CreateUserLoanResponse>(`/loans/userLoans/${userId}`, request);
    const loanResponse: UserLoan = response.data.data;

    return loanResponse;
  } catch (error: any) {
    console.error('Error creating loan:', error.response);
  }
}

export const getLoanDetailed = async (loanId: string) => {
  try {    
    const response = await api.get<UserLoanWrapResponse>(`/loans/${loanId}`);
    const loanResponse: UserLoan = response.data.data;

    return loanResponse;
  } catch (error: any) {
    console.error('Error getting loan by id:', error.response);
  }
}

const filterAwardedLoans = (loans: UserLoansResponse) => {
  loans.current = loans.current.filter(loan => loan.loanStatus === AwardedLoanStatus);
  loans.finished = loans.finished.filter(loan => loan.loanStatus === AwardedLoanStatus);

  return loans;
}
