/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState } from 'react';
import {
  View,
  Text,
  Pressable,
  TouchableOpacity,
  Keyboard,
  Image,
} from 'react-native';
import { Button, TextBase } from '../../../atoms';
import { InputForm } from '../../../molecules';
import { styles, stylesPass } from './styles';
import Logo from '../../../atoms/Icons/Logo';
import Phone from '../../../atoms/Icons/Phone';
import LineWithText from '../../../../helpers/LineWhitText';
import { Control, Controller } from 'react-hook-form';
import { RecoverPasswordModal } from '../../Modals/RecoverPassword';
import KeyboardAvoidingComponent from '../../../molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { InputLogin } from '../../../../screens/LoginScreen';
import color from '../../../../theme/pallets/pallet';

type Props = {
  control: Control<InputLogin>;
  handleSubmit: any;
  loading: boolean;
  keyboardOpen: boolean;
  error: boolean;
  handleOnSubmit: (values: any) => Promise<void>;
  isButtonDisabled: boolean;
  triggerBiometrics: () => Promise<void>;
};

const LoginForm = ({
  control,
  handleSubmit,
  loading,
  keyboardOpen,
  error,
  handleOnSubmit,
  isButtonDisabled,
  triggerBiometrics,
}: Props) => {
  const [isModalVisible, setModalVisible] = useState(false);

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  return (
    <KeyboardAvoidingComponent collapse>
      <Pressable
        accessible={false}
        onPress={() => {
          Keyboard.dismiss();
        }}
        style={styles.container}
      >
        <View>
          <View style={styles.containerLogo}>{!keyboardOpen && <Phone />}</View>
          <View style={styles.containerInputs}>
            <View style={styles.gap24}>
              <TextBase size="xxl" type="SemiBold">
                ¡Hola de nuevo!
              </TextBase>
              <View>
                <TextBase style={styles.topTextInput}>
                  Correo electrónico
                </TextBase>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <InputForm
                      text={value}
                      setText={text => onChange(text)}
                      onBlur={onBlur}
                      placeholder="Ingresa tu correo electrónico"
                      error={error}
                      editable={!loading === true ? true : false}
                    />
                  )}
                  name="email"
                />
              </View>
              <View>
                <TextBase style={styles.topTextInput}>Contraseña</TextBase>
                <Controller
                  control={control}
                  render={({ field: { onChange, onBlur, value } }) => (
                    <InputForm
                      text={value}
                      setText={text => onChange(text)}
                      onBlur={onBlur}
                      placeholder="Ingresa tu contraseña"
                      hideText={true}
                      error={error}
                      editable={!loading === true ? true : false}
                    />
                  )}
                  name="password"
                  rules={{ required: 'La contraseña es obligatoria' }}
                  defaultValue=""
                />
                {error && (
                  <TextBase size="s" color={color.RED_700}>
                    Verifica tu correo electrónico y contraseña.
                  </TextBase>
                )}
                <Pressable onPress={toggleModal}>
                  <TextBase
                    type="Bold"
                    style={stylesPass.textRestablecer}
                    color={color.PRIMARY_700}
                  >
                    Restablecer contraseña
                  </TextBase>
                </Pressable>
              </View>
              <View
                style={
                  keyboardOpen ? styles.buttonWhitoutMargin : styles.button
                }
              >
                <Button
                  disabled={isButtonDisabled}
                  loading={loading}
                  text="Iniciar sesión"
                  onPress={handleSubmit(handleOnSubmit)}
                />
              </View>
            </View>
          </View>
          {/* <TouchableOpacity
            onPress={triggerBiometrics}
            style={{ marginTop: 16 }}
          >
            <Text style={styles.registerText}>Utilizar datos biometricos</Text>
          </TouchableOpacity>
          <LineWithText text="o" />
          <View
            style={{
              flexDirection: 'row',
              gap: 12,
              justifyContent: 'center',
              marginVertical: 16,
            }}
          >
            <TouchableOpacity
              style={{
                width: 70,
                height: 70,
                borderWidth: 1,
                borderColor: '#1877F2',
                borderRadius: 20,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#FFFFFF',
              }}
            >
              <Image
                source={require('../../../../assets/ilustrations/facebook-logo/facebook-logo.png')}
                style={{ width: 30, height: 30, resizeMode: 'contain' }}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                width: 70,
                height: 70,
                borderWidth: 1,
                borderColor: '#E0E0E0',
                borderRadius: 20,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#FFFFFF',
              }}
            >
              <Image
                source={require('../../../../assets/ilustrations/google-logo/google-logo.png')}
                style={{ width: 30, height: 30, resizeMode: 'contain' }}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                borderColor: '#333333',
                width: 70,
                height: 70,
                borderWidth: 1,
                borderRadius: 20,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: '#FFFFFF',
              }}
            >
              <Image
                source={require('../../../../assets/ilustrations/apple-logo/apple-logo.png')}
                style={{ width: 30, height: 30, resizeMode: 'contain' }}
              />
            </TouchableOpacity>
          </View> */}
        </View>
        <RecoverPasswordModal
          modalVisible={isModalVisible}
          setModalVisible={setModalVisible}
        />
      </Pressable>
    </KeyboardAvoidingComponent>
  );
};

export default LoginForm;
