import React, { useState } from 'react';
import { Modal, Pressable, Text, View } from 'react-native';
import { Input, TextBase } from '../../../atoms';
import { styles } from './styles';
import KeyboardAvoidingComponent from '../../../molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { LoaderModal } from '../Loader';
import ButtonOutline from '../../../atoms/ButtonOutline';
import { useEditContact } from '../../../../hooks/User/editNameContact';
import useContactStore from '../../../../context/UserZustand';
import color from '../../../../theme/pallets/pallet';

type Props = {
  modalVisible: boolean;
  setModalVisible: (value: boolean) => void;
  accountData?: any;
  setNameChanged?: (name: string) => void;
};

export const ModalEditContact = ({
  modalVisible,
  setModalVisible,
  accountData,
}: Props) => {
  const [name, setName] = useState('');
  const firstLetter =
    accountData && accountData?.contactName.charAt(0).toUpperCase();
  const secondLetter =
    (accountData &&
      accountData?.contactName.split(' ')[1]?.charAt(0).toUpperCase()) ||
    '';
  const contactId = accountData?._id;
  const { editContactName, loading } = useEditContact();
  // const { reloadContacts } = useTransferContext();
  const setContactName = useContactStore(state => state.setContactName);
  const setNameChanged = useContactStore(state => state.setNameChanged);

  const handleChangeName = () => {
    try {
      editContactName({
        contactId,
        name,
      });
      setModalVisible(false);
      setContactName(name);
      setNameChanged(true);
    } catch (error) {
      console.error('error', error);
    }
  };

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => {
        setModalVisible(!modalVisible);
      }}
    >
      <LoaderModal modalVisible={loading} />
      <KeyboardAvoidingComponent>
        <View style={styles.overlay}>
          <View style={styles.modalView}>
            <View style={styles.containerAccount}>
              <View style={styles.circle}>
                <TextBase style={styles.plus}>
                  {firstLetter}
                  {secondLetter}
                </TextBase>
              </View>
              <View style={styles.account}>
                <Text style={styles.name} numberOfLines={2}>
                  {accountData && accountData.contactName}
                </Text>
                <Text style={styles.data}>
                  CUIT/CUIL: {accountData && accountData?.accounts[0].cuit}
                </Text>
              </View>
              <Pressable onPress={() => setModalVisible(false)}>
                <TextBase type="Bold" color={color.PRIMARY_700}>
                  Cerrar
                </TextBase>
              </Pressable>
            </View>
            <View>
              <TextBase>Nombre alternativo</TextBase>
              <View style={styles.containerInput}>
                <Input placeholder="" text={name} setText={setName} />
              </View>
              <TextBase>Solo aparecerá en tu lista de contactos</TextBase>
            </View>
            <View style={styles.button}>
              <ButtonOutline onPress={handleChangeName} text="Guardar" />
            </View>
          </View>
        </View>
      </KeyboardAvoidingComponent>
    </Modal>
  );
};
