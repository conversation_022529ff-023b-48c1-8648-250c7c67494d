import * as React from 'react';
import { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { TextBase } from '../components/atoms';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import useDisableBackButton from '../hooks/utils/useDisableBackButton';
import WarningImage from '../components/atoms/Icons/WarningScreen';
import { AuthContext } from '../context/AuthContext';
import color from '../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

const CancelAccount = ({ navigation }: Props) => {
  const [counter, setCounter] = useState(5);
  const { signOut } = React.useContext(AuthContext);

  useDisableBackButton();

  useEffect(() => {
    const timer = setInterval(() => {
      setCounter(prevCounter => prevCounter - 1);
    }, 1000);

    if (counter === 0) {
      signOut();
      navigation.navigate('Login');
    }

    return () => clearInterval(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [counter, navigation]);

  const handleHome = () => {
    navigation.navigate('Login');
  };

  return (
    <View style={styles.error}>
      <View style={[styles.content, styles.contentFlexBox]}>
        <WarningImage width={155} height={134} />
        <TextBase style={styles.h3} type="Bold" size="l" numberOfLines={1}>
          Desactivamos tu cuenta
        </TextBase>
        <View style={styles.width}>
          <TextBase
            style={styles.p}
            numberOfLines={3}
            color={color.NEUTRALS_800}
          >
            A partir de ahora ya no tendrás acceso a las funcionalidades de la
            billetera.
          </TextBase>
        </View>
      </View>
      <View style={[styles.actions, styles.contentFlexBox]}>
        <TouchableOpacity onPress={handleHome} disabled={true}>
          <TextBase color={color.NEUTRALS_800}>
            Serás redirigido al inicio en {counter} segundos...
          </TextBase>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  buttonBtn: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Satoshi-SemiBold',
  },
  buttonBtn1: {
    padding: 12,
  },
  buttonBtn2: {
    alignSelf: 'stretch',
    borderRadius: 4,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  contentFlexBox: {
    alignSelf: 'stretch',
    alignItems: 'center',
  },
  pFlexBox: {
    marginTop: 8,
    textAlign: 'center',
    alignSelf: 'stretch',
  },
  imageIcon: {
    width: 163,
    height: 126,
  },
  h3: {
    marginTop: 16,
  },
  p: {
    textAlign: 'center',
    alignSelf: 'stretch',
    marginTop: 8,
  },
  content: {
    justifyContent: 'center',
    paddingHorizontal: 16,
    paddingVertical: 0,
    flex: 1,
    alignSelf: 'stretch',
  },
  actions: {
    marginBottom: 16,
  },
  error: {
    width: '100%',
    overflow: 'hidden',
    alignItems: 'center',
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  width: {
    width: '70%',
  },
});

export default CancelAccount;
