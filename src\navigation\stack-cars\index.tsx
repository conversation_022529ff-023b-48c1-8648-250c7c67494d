import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import color from '../../theme/pallets/pallet';
import { CarsStackParams } from './types';
import GoBack from '../../components/organisms/Buttons/GoBackButton';
import {
  CarsActivityScreen,
  CarsAmountScreen,
  CarsConfirmScreen,
  CarsDetailScreen,
  CarsScreen,
  CarsSuccessScreen,
} from '../../screens/stack-cars';

const Stack = createNativeStackNavigator<CarsStackParams>();

export default function StackCars() {
  const goBack = () => <GoBack />;
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="CarsScreen"
        component={CarsScreen}
      />
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="CarsActivityScreen"
        component={CarsActivityScreen}
      />
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="CarsDetailScreen"
        component={CarsDetailScreen}
      />
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="CarsAmountScreen"
        component={CarsAmountScreen}
      />
      <Stack.Screen
        options={() => ({
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: goBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        })}
        name="CarsConfirmScreen"
        component={CarsConfirmScreen}
      />
      <Stack.Screen
        options={() => ({
          headerShown: false,
          gestureEnabled: false,
        })}
        name="CarsSuccessScreen"
        component={CarsSuccessScreen}
      />
    </Stack.Navigator>
  );
}
