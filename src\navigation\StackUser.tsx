import React from 'react';

import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { UserScreen } from '../screens/StackUser/UserScreen';
import { useNavigation } from '@react-navigation/native';
import { Pressable } from 'react-native';
import { ArrowLeftLarge } from '../components/atoms/Icons';
import { EditProfileScreen } from '../screens/StackUser/EditProfileScreen';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<any>();

function ArrowBack() {
  const navigation = useNavigation();
  return (
    <Pressable onPress={() => navigation.goBack()}>
      <ArrowLeftLarge size={16} color="white" />
    </Pressable>
  );
}

export default function StackUser() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: 'Mi perfil',
          headerTintColor: color.WHITE,
          headerTitleStyle: {
            fontFamily: 'Satoshi-Bold',
            fontSize: 16,
          },
          headerTitleAlign: 'center',

          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.PRIMARY_500,
          },
        }}
        name={'UserScreen'}
        component={UserScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: 'Editar perfil',
          headerTintColor: color.WHITE,
          headerTitleStyle: {
            fontFamily: 'Satoshi-Bold',
            fontSize: 16,
          },
          headerTitleAlign: 'center',

          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.PRIMARY_500,
          },
        }}
        name={'EditProfile'}
        component={EditProfileScreen}
      />
    </Stack.Navigator>
  );
}
