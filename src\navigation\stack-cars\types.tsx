import { NavigatorScreenParams } from '@react-navigation/native';
import { InvestmentStackParams } from '../stack-investment/types';

export type CarsStackParams = {
  CarsScreen: undefined;
  CarsActivityScreen: undefined;
  CarsDetailScreen: undefined;
  CarsAmountScreen: { type: string };
  CarsConfirmScreen: { type: string };
  CarsSuccessScreen: undefined;
  StackInvestment: NavigatorScreenParams<InvestmentStackParams>;
};
