import React from 'react';

import GoBack from '../components/organisms/Buttons/GoBackButton';
import { useNavigation } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { PaymentServices } from '../screens/StackPaymentServices/PaymentServices';
import { ScannerCodeScreen } from '../screens/StackPaymentServices/ScannerCode';
import { ManualCodeScreen } from '../screens/StackPaymentServices/ManualCode';
import { ScannerPayment } from '../screens/StackPaymentServices/ScannerPayment';
import { SearchService } from '../screens/StackPaymentServices/SearchService';
import color from '../theme/pallets/pallet';

const Stack = createNativeStackNavigator<any>();

function ArrowBack() {
  const navigation = useNavigation();
  return <GoBack onPress={() => navigation.goBack()} />;
}

export default function StackPaymentServices() {
  return (
    <Stack.Navigator
      screenOptions={{
        contentStyle: {
          backgroundColor: color.WHITE,
        },
      }}
    >
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'PaymentServices'}
        component={PaymentServices}
      />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
        name={'ScannerCodeScreen'}
        component={ScannerCodeScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'ManualCode'}
        component={ManualCodeScreen}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'ScannerPayment'}
        component={ScannerPayment}
      />
      <Stack.Screen
        options={{
          headerShadowVisible: false,
          headerBackVisible: false,
          headerTitle: '',
          headerLeft: ArrowBack,
          headerStyle: {
            backgroundColor: color.WHITE,
          },
        }}
        name={'SearchService'}
        component={SearchService}
      />
    </Stack.Navigator>
  );
}
