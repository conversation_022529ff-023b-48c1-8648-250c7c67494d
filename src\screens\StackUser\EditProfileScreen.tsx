import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Keyboard, ScrollView, StyleSheet, View } from 'react-native';
import { Button, UserIcon } from '../../components/atoms';
import { InputWithLabel } from '../../components/atoms/InputWithLabel';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackUserParams } from '../../types/StackUser/StackUserParams';
import { api } from '../../services/apiService';
import { AuthContext } from '../../context/AuthContext';
import { LoaderModal } from '../../components/organisms/Modals/Loader';
import { EditUserModal } from '../../components/organisms/Modals/EditUser';
import { getUserInfo } from '../../services/users';
import { useFocusEffect } from '@react-navigation/native';
import { UserDetail } from '../../types/User';
import KeyboardAvoidingComponent from '../../components/molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import { getArgentinaProvinces } from '../../services/utils';
import SelectInput from '../../components/atoms/SelectInput';
import color from '../../theme/pallets/pallet';
type Props = NativeStackScreenProps<StackUserParams, 'EditProfile'>;

export const EditProfileScreen = ({ route, navigation }: Props) => {
  // const { user } = route.params;

  const { userData, isInBackground } = useContext(AuthContext);
  const [modalVisible, setModalVisible] = useState(false);
  const [loaderModalVisible, setLoaderModalVisible] = useState(false);
  const [user, setUser] = useState<UserDetail>();
  const [disabled, setDisabled] = useState(true);
  const [error, setError] = useState(false);
  const [keyboardOpen, setKeyboardOpen] = useState(false);
  const [provinces, setProvinces] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);

  const [address, setAddress] = useState({
    calle: user?.calle,
    numero: user?.numero,
    departamento: user?.departamento,
    piso: user?.piso,
    provincia: user?.provincia,
    ciudad: user?.ciudad,
  });

  useEffect(() => {
    isInBackground && setModalVisible(false);
  }, [isInBackground]);

  const getProvince = async () => {
    const res = await getArgentinaProvinces();
    setProvinces(res);
  };

  const namesProvinces = provinces.map((province: any) => province.nombre);

  const getUser = useCallback(async () => {
    const res = await getUserInfo();
    setUser(res?.userDetails);
    setAddress({
      calle: res?.userDetails?.calle,
      numero: res?.userDetails?.numero,
      departamento: res?.userDetails?.departamento,
      piso: res?.userDetails?.piso,
      provincia: res?.userDetails?.provincia,
      ciudad: res?.userDetails?.ciudad,
    });
  }, []);

  useFocusEffect(
    useCallback(() => {
      getUser();
      getProvince();
    }, [getUser]),
  );

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        setKeyboardOpen(true);
      },
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardOpen(false);
      },
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  function validarTexto(texto: string): {
    esValido: boolean;
  } {
    if (!/[a-zA-Z0-9]/.test(texto)) {
      return { esValido: false };
    }

    return { esValido: true };
  }

  const handleAddressChange = (key: string, value: string) => {
    setDisabled(false);

    setAddress(prev => ({ ...prev, [key]: value }));
    if (key === 'piso' || key === 'departamento') {
      return;
    }
    setTimeout(() => {
      const resultado = validarTexto(value);

      if (resultado.esValido) {
        setDisabled(false);
      } else {
        setDisabled(true);
      }
    }, 500);
  };

  const handleSave = async () => {
    setLoaderModalVisible(true);
    setError(false);
    setDisabled(true);
    try {
      await api.put(`/digital/users/${userData}/address`, {
        ...address,
      });
      setLoaderModalVisible(false);
      setModalVisible(true);
    } catch (err: any) {
      console.error('Error updating address:', err.response);
      setError(true);
      setModalVisible(true);
      setLoaderModalVisible(false);
    }
  };

  const navigateToProfile = () => {
    setModalVisible(false);
    navigation.navigate('Home');
  };

  const buttonDisabled =
    disabled ||
    address.provincia === '' ||
    address.ciudad === '' ||
    address.calle === '' ||
    address.numero === '';
  return (
    <KeyboardAvoidingComponent scrollEnabled={!showDropdown}>
      <ScrollView>
        <View style={styles.topContainer}>
          {user && (
            <UserIcon usernameInitials={user?.nombre[0] + user?.apellido[0]} />
          )}
          {/* <View style={styles.buttonContainer}>
          <TextBase size="s" color={color.PRIMARY_700}>
            Editar avatar
          </TextBase>
        </View> */}
        </View>
        <View style={styles.botContainer}>
          {!keyboardOpen && (
            <>
              <InputWithLabel
                label="Nombre completo"
                value={user?.nombre + ' ' + user?.apellido}
                disabled
              />
              <InputWithLabel
                label="E-mail"
                value={user?.email ?? ''}
                disabled
              />
              <InputWithLabel
                label="Número de teléfono"
                value={
                  user?.codigoPais! +
                  user?.codigoAreaTelefono! +
                  user?.numeroTelefono ?? ''
                }
                disabled
              />
            </>
          )}
          <SelectInput
            label="Provincia"
            data={namesProvinces}
            input={address.provincia ?? ''}
            name={'provincia'}
            onChange={handleAddressChange}
            showDropdown={showDropdown}
            setShowDropdown={setShowDropdown}
          />
          <InputWithLabel
            label="Ciudad"
            value={address.ciudad ?? ''}
            name={'ciudad'}
            onChange={handleAddressChange}
          />
          <View style={styles.flexRow}>
            <InputWithLabel
              label="Calle"
              value={address.calle ?? ''}
              name={'calle'}
              onChange={handleAddressChange}
            />
            <InputWithLabel
              label="Número"
              value={address.numero ?? ''}
              name={'numero'}
              onChange={handleAddressChange}
            />
          </View>
          <View style={styles.flexRow}>
            <InputWithLabel
              label="Piso"
              value={address.piso ?? ''}
              name={'piso'}
              onChange={handleAddressChange}
            />
            <InputWithLabel
              label="Departamento"
              value={address.departamento ?? ''}
              name={'departamento'}
              onChange={handleAddressChange}
            />
          </View>
        </View>
        <View style={styles.footer}>
          <Button
            text="Guardar"
            onPress={handleSave}
            disabled={buttonDisabled}
          />
          {/* <TextBase color={color.NEUTRALS_800} style={styles.textCenter}>
          Si necesita cambiar los detalles, comuníquese con{' '}
          <TextBase style={styles.textUnderline} type="SemiBold">
            Servicio al Cliente.
          </TextBase>
        </TextBase> */}
        </View>
        <EditUserModal
          setModalVisible={setModalVisible}
          modalVisible={modalVisible}
          onPress={navigateToProfile}
          error={error}
        />
        <LoaderModal modalVisible={loaderModalVisible} />
      </ScrollView>
    </KeyboardAvoidingComponent>
  );
};

const styles = StyleSheet.create({
  topContainer: {
    alignItems: 'center',
    backgroundColor: color.PRIMARY_500,
    gap: 16,
    paddingTop: 16,
    paddingBottom: 24,
  },
  buttonContainer: {
    alignItems: 'center',
    backgroundColor: '#FFE7E3',
    borderRadius: 40,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  botContainer: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    gap: 24,
    flex: 1,
  },
  flexRow: {
    flexDirection: 'row',
    gap: 16,
    width: '100%',
  },
  footer: {
    padding: 16,
    gap: 16,
  },
  textCenter: {
    textAlign: 'center',
  },
  textUnderline: {
    textDecorationLine: 'underline',
  },
});
