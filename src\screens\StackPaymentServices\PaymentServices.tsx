import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { StyleSheet } from 'react-native';
import LineWithText from '../../helpers/LineWhitText';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { TextBase } from '../../components/atoms';
import SearchRounded from '../../components/atoms/Icons/SearchRounded';
import CodeServicesIcon from '../../components/atoms/Icons/CodeServicesIcon';
import ExpirationsCard from '../../components/organisms/Cards/Expirations';
import ServicesCard from '../../components/organisms/Cards/Services';
import UpdateIcon from '../../components/atoms/Icons/UpdateIcon';
import color from '../../theme/pallets/pallet';

type Props = NativeStackScreenProps<any>;

export const PaymentServices = ({ navigation }: Props) => {
  return (
    <View style={styles.containter}>
      <View style={styles.title}>
        <TextBase size="xxl" type="Bold">
          Pago de servicios
        </TextBase>
        <UpdateIcon color={color.PRIMARY_700} size={24} />
      </View>
      <View style={styles.buttons}>
        <Pressable
          style={styles.containerButtons}
          onPress={() => navigation.navigate('SearchService')}
        >
          <SearchRounded height={48} width={48} />
          <Text style={styles.txt}>Buscar un servicio</Text>
        </Pressable>
        <Pressable
          style={styles.containerButtons}
          onPress={() => navigation.navigate('ScannerCodeScreen')}
        >
          <CodeServicesIcon height={48} width={48} />
          <Text style={styles.txt}>Escanear código de barras</Text>
        </Pressable>
      </View>
      <LineWithText text={'o'} />
      <ExpirationsCard />
      <ServicesCard />
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  containter: {
    flex: 1,
    backgroundColor: color.WHITE,
    paddingHorizontal: 16,
  },
  buttons: {
    marginTop: 24,
  },
  containerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  txt: {
    fontSize: 14,
    fontFamily: 'Satoshi-Regular',
    color: color.TEXT_PRIMARY,
    marginLeft: 8,
  },
});
