import { api } from '../../services/apiService';
import { useState } from 'react';

export const useDeleteAccount = ({ errorCancel, setErrorCancel }: any) => {
  const [successData, setSuccessData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const deleteAccount = async ({ userId }: any) => {
    try {
      setLoading(true);
      const response = await api.delete(`/users/${userId}/delete-cvu`);
      if (response.status === 200) {
        setSuccessData(response.data);
        setSuccess(true);
        setLoading(false);
      } else {
        console.error('Error: Unexpected status code', response.status);
        setErrorCancel(true);
      }
      setLoading(false);
    } catch (err: any) {
      console.error('Error:', err.response);
      setErrorCancel(true);
      setErrorMessage(err.response.data.message);
      setLoading(false);
    }
  };

  return {
    deleteAccount,
    successData,
    errorMessage,
    loading,
    success,
    errorCancel,
  };
};
