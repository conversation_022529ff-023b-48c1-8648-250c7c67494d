import { StyleSheet } from 'react-native';
import { colors } from '../../../../constants/colors';
import color from '../../../../theme/pallets/pallet';

export const styles = StyleSheet.create({
  modalView: {
    backgroundColor: 'white',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    padding: 16,
    marginTop: 'auto',
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
  },
  containerAccount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  account: {
    flex: 1,
    marginHorizontal: 12,
    marginVertical: 6,
  },
  name: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 16,
    color: color.TEXT_PRIMARY,
  },
  data: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 16,
    color: '#78838D',
  },
  close: {
    alignSelf: 'flex-start',
    marginLeft: 'auto',
  },
  textClose: {
    color: color.PRIMARY_700,
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
  },
  mt24: {
    marginVertical: 24,
    flexDirection: 'row',
  },
  containerItem: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: color.NEUTRALS_100,
    paddingHorizontal: 12,
    paddingVertical: 16,
    marginBottom: 8,
  },
  label: {
    fontFamily: 'Satoshi-Regular',
    fontSize: 12,
    color: '#78838D',
    textTransform: 'capitalize',
  },
  value: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    color: color.NEUTRALS_800,
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  error: {
    color: colors.negative,
    fontFamily: 'Satoshi-Bold',
    fontSize: 14,
    marginLeft: 2,
  },
  containerItemAmountRed: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FFF6F6',
    paddingHorizontal: 12,
    paddingVertical: 16,
    marginBottom: 8,
    backgroundColor: '#FFF6F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  valueRed: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 22,
    color: '#B83232',
  },
  containerItemAmountGreen: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#F4FFF8',
    paddingHorizontal: 12,
    paddingVertical: 16,
    marginBottom: 8,
    backgroundColor: '#F4FFF8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  valueGreen: {
    fontFamily: 'Satoshi-Bold',
    fontSize: 22,
    color: '#289B4F',
  },
  bot: {
    display: 'flex',
    alignItems: 'center',
    marginTop: 20,
  },
});
