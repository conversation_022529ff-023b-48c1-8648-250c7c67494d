import React from 'react';
import {
  Modal,
  Pressable,
  Text,
  Touchable,
  TouchableOpacity,
  View,
} from 'react-native';
import { TextBase } from '../../../atoms';
import { styles } from './styles';
import KeyboardAvoidingComponent from '../../../molecules/KeyboardAvoidingComponent/KeyboardAvoidingComponent';
import PersonIcon from '../../../atoms/Icons/PersonIcon';
import StarIcon from '../../../atoms/Icons/StarIcon';
import TrashIcon from '../../../atoms/Icons/Trash';
import StarColor from '../../../atoms/Icons/StarColor';
import { useFavorite } from '../../../../hooks/User/addFavorite';
import { LoaderModal } from '../Loader';
import { useNavigation } from '@react-navigation/native';
import {
  FavoritesProvider,
  useTransferContext,
} from '../../../../context/TransferContext';

type Props = {
  modalContact: boolean;
  setModalContact: (value: boolean) => void;
  accountData?: any;
};

export const InfoContact = ({
  modalContact,
  setModalContact,
  accountData,
}: Props) => {
  const firstLetter =
    accountData && accountData?.contactName.charAt(0).toUpperCase();
  const secondLetter =
    (accountData &&
      accountData?.contactName.split(' ')[1]?.charAt(0).toUpperCase()) ||
    '';
  const contactId = accountData?._id;
  const { addFavorite, loading } = useFavorite();
  const navigation: any = useNavigation();
  const { reloadFavs, reloadContacts } = useTransferContext();

  const addFavoriteContact = async () => {
    try {
      await addFavorite({ contactId });
      reloadFavs();
      reloadContacts();
      setModalContact(false);
    } catch (error) {
      console.log('error', error);
    }
  };

  const goToProfile = () => {
    setModalContact(false);
    navigation.navigate('ProfileContactScreen', { accountData: accountData });
  };

  return (
    <FavoritesProvider>
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalContact}
        onRequestClose={() => {
          setModalContact(!modalContact);
        }}
      >
        <LoaderModal modalVisible={loading} />
        <KeyboardAvoidingComponent>
          <View style={styles.overlay}>
            <View style={styles.modalView}>
              <View style={styles.containerAccount}>
                <View style={styles.circle}>
                  <TextBase style={styles.plus}>
                    {firstLetter}
                    {secondLetter}
                  </TextBase>
                </View>
                <View style={styles.account}>
                  <Text style={styles.name} numberOfLines={2}>
                    {accountData && accountData.contactName}
                  </Text>
                  <Text style={styles.data}>
                    CUIT/CUIL: {accountData && accountData?.accounts[0].cuit}
                  </Text>
                </View>
                <Pressable
                  onPress={() => setModalContact(false)}
                  style={styles.close}
                >
                  <Text style={styles.textClose}>Cerrar</Text>
                </Pressable>
              </View>
              <View style={styles.containerItem}>
                <PersonIcon />
                <TouchableOpacity onPress={goToProfile}>
                  <TextBase type="Bold" style={styles.options}>
                    Ver perfil del contacto
                  </TextBase>
                </TouchableOpacity>
              </View>
              <View style={styles.containerItem}>
                {accountData?.isFavorite ? (
                  <>
                    <StarColor />
                    <TouchableOpacity onPress={addFavoriteContact}>
                      <TextBase type="Bold" style={styles.options}>
                        Quitar de favoritos
                      </TextBase>
                    </TouchableOpacity>
                  </>
                ) : (
                  <>
                    <StarIcon />
                    <TouchableOpacity onPress={addFavoriteContact}>
                      <TextBase type="Bold" style={styles.options}>
                        Agregar como favorito
                      </TextBase>
                    </TouchableOpacity>
                  </>
                )}
              </View>
              {/* <View style={styles.containerItem}>
              <TrashIcon />
              <TextBase color={'#B83232'} type="Bold" style={styles.options}>
                Eliminar contacto
              </TextBase>
            </View> */}
            </View>
          </View>
        </KeyboardAvoidingComponent>
      </Modal>
    </FavoritesProvider>
  );
};
