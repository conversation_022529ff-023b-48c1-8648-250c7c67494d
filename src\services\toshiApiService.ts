import axios from 'axios';
import Config from 'react-native-config';
import { TOKEN } from '../constants/asyncStorageKeys';
import { getData } from './asyncStorageService';

const apiToshi = axios.create({
  baseURL: Config.API_TOSHI_URL,
});

apiToshi.interceptors.request.use(
  async config => {
    const userToken = await getData(TOKEN);
    if (userToken) {
      config.headers.Authorization = `Bearer ${userToken}`;
    }
    return config;
  },
  error => Promise.reject(error),
);

export default apiToshi;
