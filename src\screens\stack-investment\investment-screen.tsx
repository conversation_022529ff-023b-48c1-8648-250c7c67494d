import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Pressable,
  ScrollView,
  View,
} from 'react-native';
import { StyleSheet } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import color from '../../theme/pallets/pallet';
import HeaderInvetment from '../../components/organisms/Headers/Investment';
import { Text } from '../../components/atoms';
import { getAllInvestments } from '../../services/investments';
import { InvestmentBidding } from '../../types/Investments';
import { formatCurrency } from '../../helpers/formatCurrency';

type Props = NativeStackScreenProps<any>;

export default function InvestmentScreen({ navigation }: Props) {
  const handleNavigateToDetail = (investmentSelected: InvestmentBidding) => {
    navigation.navigate('InvestmentDetailScreen', investmentSelected);
  };
  const [isLoading, setIsLoading] = useState(true);
  const [investmentData, setInvestmentData] = useState<InvestmentBidding[]>([]);
  const getInvestments = async () => {
    setIsLoading(true);
    const response = await getAllInvestments();
    setIsLoading(false);
    setInvestmentData(response?.data.items || []);
  };

  useEffect(() => {
    getInvestments();
  }, []);
  return (
    <ScrollView contentContainerStyle={styles.containter}>
      <HeaderInvetment />
      {isLoading ? (
        <ActivityIndicator
          size={40}
          style={styles.spinner}
          color={color.PRIMARY_700}
        />
      ) : (
        investmentData.map((investment, index) => (
          <Pressable
            onPress={() => handleNavigateToDetail(investment)}
            style={styles.cardContainer}
            key={`${index}+${investment.id}`}
          >
            <View style={styles.card}>
              {/* Imagen del vehículo */}
              <Image
                source={{ uri: investment.vehicle.model.imageUrl }}
                style={styles.carImage}
                resizeMode="contain"
              />

              {/* Información del vehículo */}
              <View>
                <Text variant="B5" color="NEUTRALS_800">
                  {investment.vehicle?.name}
                </Text>
                <View style={styles.row}>
                  <Text variant="R7">
                    Invertidos:{' '}
                    <Text variant="B7" color="PRIMARY_500">
                      ARS {formatCurrency(investment.token?.totalTokenAmount)}
                    </Text>
                  </Text>

                  <Text variant="R7">
                    Meta:{' '}
                    <Text variant="B7" color="PRIMARY_500">
                      ARS {formatCurrency(investment.goal)}
                    </Text>
                  </Text>
                </View>
                {/* Barra de progreso */}
                <View style={styles.progressBarContainer}>
                  <View
                    style={{
                      ...styles.progressBar,
                      width: `${investment.token?.soldTokenPercentage || 0}%`,
                    }}
                  />
                </View>
                <Text variant="R7">
                  <Text variant="B7" color="PRIMARY_500">
                    {investment.token?.soldTokenPercentage}%
                  </Text>{' '}
                  invertido{' '}
                </Text>

                {/* Información adicional */}
                <View style={styles.row}>
                  <View style={styles.tokenBadge}>
                    <Text variant="R7" color="PRIMARY_700">
                      {investment.token?.availableTokenQuantity} tokens
                      disponibles
                    </Text>
                  </View>
                  <View>
                    <Text variant="B5" align="center" color="PRIMARY_500">
                      4,2 - 4,9%
                    </Text>
                    <Text variant="R7">Rendimiento mensual</Text>
                  </View>
                </View>
              </View>
            </View>
          </Pressable>
        ))
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  containter: {
    backgroundColor: color.WHITE,
    paddingHorizontal: 16,
    gap: 24,
  },
  tabContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 10,
  },
  tabText: {
    fontSize: 16,
    color: '#666',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: color.PRIMARY_700,
  },
  activeIndicator: {
    position: 'absolute',
    bottom: 0,
    height: 2,
    width: '100%',
  },
  ///////////////////////

  cardContainer: {
    padding: 16,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
    borderRadius: 16,
    marginTop: 64,
  },
  card: {
    borderRadius: 10,
    paddingTop: 40,
  },
  carImage: {
    width: 230,
    height: 130,
    position: 'absolute',
    top: -78,
    alignSelf: 'center',
  },

  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#eee',
    borderRadius: 4,
    overflow: 'hidden',
    marginVertical: 8,
  },
  progressBar: {
    width: '20%',
    height: '100%',
    backgroundColor: color.PRIMARY_500,
  },
  tokenBadge: {
    backgroundColor: color.PRIMARY_50,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    alignSelf: 'flex-end',
  },
  /////
  miniCard: {
    padding: 16,
    borderWidth: 1,
    borderColor: color.NEUTRALS_200,
    borderRadius: 16,
    marginTop: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  miniImage: {
    width: 66,
    height: 42,
  },
  spinner: {
    alignSelf: 'center',
    zIndex: 0,
  },
});
