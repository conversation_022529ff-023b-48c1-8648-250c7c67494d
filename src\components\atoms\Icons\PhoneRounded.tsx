import { FC } from 'react';
import Svg, { Path } from 'react-native-svg'
import { LoanIconTypeColor } from './types';

const PhoneRounded: FC<LoanIconTypeColor> = ({ size, iconColor }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 32 32" fill="none">
      <Path 
        d="M19.3333 6.83334H12.6666C11.5166 6.83334 10.5833 7.76668 10.5833 8.91668V23.0833C10.5833 24.2333 11.5166 25.1667 12.6666 25.1667H19.3333C20.4833 25.1667 21.4166 24.2333 21.4166 23.0833V8.91668C21.4166 7.76668 20.4833 6.83334 19.3333 6.83334ZM16 24.3333C15.3083 24.3333 14.75 23.775 14.75 23.0833C14.75 22.3917 15.3083 21.8333 16 21.8333C16.6916 21.8333 17.25 22.3917 17.25 23.0833C17.25 23.775 16.6916 24.3333 16 24.3333ZM19.75 21H12.25V9.33334H19.75V21Z" 
        fill={iconColor}
      />
    </Svg>
  )
}

export default PhoneRounded;