import Config from 'react-native-config';
import { USER_ID } from '../constants/asyncStorageKeys';
import { UserDetailResponse } from '../types/User';
import { apiOnboarding } from './apiOnboarding';
import { api } from './apiService';
import { getData } from './asyncStorageService';

export const getUserInfo = async () => {
  try {
    const userData = await getData(USER_ID);
    const response = await api.get<UserDetailResponse>(
      `/digital/users/${userData}/info`,
    );
    let linkOnboarding = '';
    const balance = response.data.data.balance?.amount ?? 0;
    const userDetails = response.data.data;
    const userOnboarding = response.data.data.onboarding;
    const cvuUser = response.data.data.cvuAccounts;
    const cards = response.data.data.cards;

    if (response.data.data.onboarding.onboardingCompleted === false) {
      const cuit = response.data.data.cuit;
      const userId = response.data.data.userId;

      try {
        const res = await apiOnboarding.get(
          `/utils/getQrCode/${cuit}/${userId}/${Config.FLOW_ID}`,
        );
        linkOnboarding = res.data.data.urlLink;
      } catch (error) {
        console.error(error);
      }
    }

    return {
      balance,
      userDetails,
      userOnboarding,
      linkOnboarding,
      cvuUser,
      cards,
    };
  } catch (error: any) {
    console.error('Error fetching user info:', error);
  }
};

export const getUserBasicInfo = async () => {
  try {
    const userData = await getData(USER_ID);
    const response = await api.get<UserDetailResponse>(
      `/users/${userData}/info`,
    );
    const user = response.data.data;

    return {
      fullName: `${user.nombre} ${user.apellido}`,
      email: user.email,
      documentType: user.idEntidadTipoDocumento.descripcion,
      documentNumber: user.numeroDocumento,
      taxIdentificationNumber: user.cuit,
      telephoneNumber: `${user.codigoAreaTelefono}${user.numeroTelefono}`,
    };
  } catch (error: any) {
    console.error('Error fetching basic user info:', error.response);
    throw error;
  }
};
